name: 'Setup job'
description: ''

runs:
  using: 'composite'
  steps:
    - name: 💿 Setup Nodejs
      uses: actions/setup-node@v4
      with:
        node-version: 22.11.0
        registry-url: 'https://npm.pkg.github.com'

    - name: 🧬 Nx shas
      uses: nrwl/nx-set-shas@v4
      with:
        main-branch-name: ${{ github.base_ref || 'develop' }}

    - name: 📥 Cache and install
      uses: ./.github/actions/yarn-cache-install
      with:
        enable-corepack: false
        cache-node-modules: true
        cache-install-state: true

    - name: 💾 Restore build artifacts
      uses: actions/cache@v4
      with:
        path: |
          packages/*/*/dist
          packages/*/*/build
        key:
          build-${{ runner.os }}-${{ github.ref_name }}-${{ hashFiles('**/package.json',
          '**/yarn.lock', '**/tsconfig.json') }}
        restore-keys: |
          build-${{ runner.os }}-${{ github.ref_name }}-
          build-${{ runner.os }}-

    - uses: actions/cache@v4
      with:
        path: .nx/cache
        key: nx-${{ runner.os }}-${{ hashFiles('yarn.lock', 'nx.json', 'package.json') }}
