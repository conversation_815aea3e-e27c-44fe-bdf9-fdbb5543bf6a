name: Update Linear issue status

on:
  pull_request:
    types: [closed]
    branches: [main]
  workflow_dispatch:

jobs:
  update-linear:
    if: github.event.pull_request.merged == true
    runs-on: ubuntu-latest
    timeout-minutes: 3
    steps:
      - name: Update all Staging tickets
        env:
          LINEAR_API_KEY: ${{ secrets.LINEAR_API_TOKEN }}
          LINEAR_TEAM_ID: 'f2330941-fbbd-4ff2-a1f1-15b59c22446e' # WEB
          FROM_STATE_ID: '297ee1d9-d882-4628-864c-3d662ee7a235' # staging
          TO_STATE_ID: 'd210e63f-29dd-4dbb-ad38-b778d414b0db' # done
        run: |
          # Query all tickets in "staging"
          QUERY=$(cat <<EOF
          {
            "query": "query { team(id: \"$LINEAR_TEAM_ID\") { issues(filter: { state: { id: { eq: \"$FROM_STATE_ID\" } } }) { nodes { id title } } } }"
          }
          EOF
          )

          RESPONSE=$(curl --request POST \
            --url https://api.linear.app/graphql \
            --header "Authorization: $LINEAR_API_KEY" \
            --header "Content-Type: application/json" \
            --data "$QUERY")

          echo "Response:"
          echo "$RESPONSE"

          # Extract ticket IDs using jq safely
          TICKET_IDS=$(echo "$RESPONSE" | jq -r '
            if .data.team.issues.nodes != null then
              [.data.team.issues.nodes[].id] | map("\""+.+"\"") | join(",")
            else
              empty
            end
          ')

          if [ -z "$TICKET_IDS" ] || [ "$TICKET_IDS" = "null" ]; then
            echo "No tickets found in Staging state"
            exit 0
          fi

          echo "Found tickets to update: $TICKET_IDS"

          # Batch update all tickets to Done state
          MUTATION=$(cat <<EOF
          {
            "query": "mutation { issueBatchUpdate(ids: [$TICKET_IDS], input: { stateId: \"$TO_STATE_ID\" }) { success } }"
          }
          EOF
          )

          # Execute the batch update
          curl --request POST \
            --url https://api.linear.app/graphql \
            --header "Authorization: $LINEAR_API_KEY" \
            --header "Content-Type: application/json" \
            --data "$MUTATION"
          #done
