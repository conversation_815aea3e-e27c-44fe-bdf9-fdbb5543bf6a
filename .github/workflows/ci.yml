name: CI
concurrency:
  group: ${{ github.workflow }}-${{ github.ref }}
  cancel-in-progress: true
on:
  pull_request:
  workflow_dispatch:

# https://github.com/qoomon/actions--parallel-steps
jobs:
  build:
    name: Running build
    if:
      "!contains(toJSON(github.event.commits.*.message), 'chore(release):') &&
      github.event.pull_request.draft != true"
    runs-on: ubuntu-latest
    timeout-minutes: 10
    env:
      CI: true
      GH_TOKEN: ${{ secrets.GH_TOKEN }}
      FONTAWESOME_TOKEN: ${{ secrets.FONTAWESOME_TOKEN }}

    steps:
      - name: 🛒 Checkout code
        uses: actions/checkout@v4
        with:
          fetch-depth: 50 # instead of 0. Should be enough for our needs and improves performance
          token: ${{ secrets.GH_TOKEN }}
          lfs: false # Disable if not using Git LFS

      - name: 🛒 Fetch another branch's history # fetch other branch history required for the ci to run
        run: |
          git fetch --depth=50 origin "${{ github.base_ref || 'develop' }}"

      - name: 💿 Setup Nodejs
        uses: actions/setup-node@v4
        with:
          node-version: 22.11.0
          registry-url: 'https://npm.pkg.github.com'

      - name: 🧬 Nx shas
        uses: nrwl/nx-set-shas@v4
        with:
          main-branch-name: ${{ github.base_ref || 'develop' }}

      - name: 📥 Cache and install
        uses: ./.github/actions/yarn-cache-install
        with:
          enable-corepack: false
          cache-node-modules: true
          cache-install-state: true

      - name: 💾 Restore build artifacts
        uses: actions/cache@v4
        with:
          path: |
            packages/*/*/dist
            packages/*/*/build
          key:
            build-${{ runner.os }}-${{ github.ref_name }}-${{ hashFiles('**/package.json',
            '**/yarn.lock', '**/tsconfig.json') }}
          restore-keys: |
            build-${{ runner.os }}-${{ github.ref_name }}-
            build-${{ runner.os }}-

      - name: 📋 Yarn lock changes
        uses: Simek/yarn-lock-changes@main
        with:
          updateComment: 'true'
          token: ${{ secrets.GITHUB_TOKEN }}

      - name: 🔍 Affected
        id: affected
        run: |
          {
            echo 'AFFECTED<<EOF'
            yarn nx show projects --affected
            echo EOF
          } >> $GITHUB_OUTPUT

      - name: 🔨 Build
        # setup envs for testing
        run: |
          export NODE_OPTIONS="--max-old-space-size=4096"
          mv packages/apps/web-app/.env.ci.test packages/apps/web-app/.env
          yarn build! --parallel=5

    outputs:
      affected: ${{ steps.affected.outputs.AFFECTED }}

  lint:
    needs: build
    runs-on: ubuntu-latest
    timeout-minutes: 8
    env:
      CI: true
      GH_TOKEN: ${{ secrets.GH_TOKEN }}
      FONTAWESOME_TOKEN: ${{ secrets.FONTAWESOME_TOKEN }}
    steps:
      - name: 🛒 Checkout code
        uses: actions/checkout@v4
        with:
          fetch-depth: 50
          token: ${{ secrets.GH_TOKEN}}
      - name: 🛒 Fetch another branch's history # fetch other branch history required for the ci to run
        run: |
          git fetch --depth=50 origin "${{ github.base_ref || 'develop' }}"
      - name: ⚙️ Setup job
        uses: ./.github/actions/setup-job

      - name: ✏️ Lint
        run: |
          yarn nx affected --target=lint --parallel=5
          yarn nx affected --target=lint:ts --parallel=5

  test:
    needs: build
    runs-on: ubuntu-latest
    timeout-minutes: 8
    env:
      CI: true
      GH_TOKEN: ${{ secrets.GH_TOKEN }}
      FONTAWESOME_TOKEN: ${{ secrets.FONTAWESOME_TOKEN }}
    steps:
      - name: 🛒 Checkout code
        uses: actions/checkout@v4
        with:
          fetch-depth: 50
          token: ${{ secrets.GH_TOKEN}}
      - name: 🛒 Fetch another branch's history # fetch other branch history required for the ci to run
        run: |
          git fetch --depth=50 origin "${{ github.base_ref || 'develop' }}"
      - name: ⚙️ Setup job
        uses: ./.github/actions/setup-job

      - name: 🧪 Unit tests
        run: yarn nx affected --target=test:cov --parallel=5

  e2e:
    needs: build
    if: needs.build.outputs.affected != ''
    runs-on: ubuntu-latest
    timeout-minutes: 8
    env:
      CI: true
      GH_TOKEN: ${{ secrets.GH_TOKEN }}
      FONTAWESOME_TOKEN: ${{ secrets.FONTAWESOME_TOKEN }}
    steps:
      - name: 🛒 Checkout code
        uses: actions/checkout@v4
        with:
          fetch-depth: 50
          token: ${{ secrets.GH_TOKEN}}
      - name: 🛒 Fetch another branch's history # fetch other branch history required for the ci to run
        run: |
          git fetch --depth=50 origin "${{ github.base_ref || 'develop' }}"
      - name: ⚙️ Setup job
        uses: ./.github/actions/setup-job

      - name: 🧪 E2E tests
        working-directory: packages/apps/web-app
        run: yarn playwright install --with-deps && yarn test:e2e

      - name: 💾 Upload Artifacts
        uses: actions/upload-artifact@v4
        if: failure()
        with:
          name: playwright-report
          path: playwright-report/
          if-no-files-found: ignore
          retention-days: 7
