name: Release Production
on:
  pull_request:
    branches: [main]
    types:
      - opened
      - synchronize
  workflow_dispatch:

jobs:
  release:
    name: Running Release Production
    if: "!contains(github.event.head_commit.message, '[skip notes]')"
    runs-on: ubuntu-latest
    timeout-minutes: 25
    strategy:
      matrix:
        node-version: [22.11.0]
    env:
      CI: true
      GH_TOKEN: ${{ secrets.GH_TOKEN }}
      FONTAWESOME_TOKEN: ${{ secrets.FONTAWESOME_TOKEN }}
      NX_SKIP_NX_CACHE: true

    steps:
      - name: 🛒 Checkout code
        uses: actions/checkout@v4
        with:
          fetch-depth: 250 # instead of 0. Should be enough for our needs and improves performance
          token: ${{ secrets.GH_TOKEN }}
          lfs: false # Disable if not using Git LFS

      - name: 🛒 Fetch another branch's history # fetch other branch history required for the ci to run
        run: |
          git fetch --depth=250 origin "${{ github.base_ref || 'main' }}"

      - name: 🏷️ Get version
        id: version
        uses: martinbeentjes/npm-get-version-action@v1.3.1
        with:
          path: packages/apps/web-app

      - name: 📄 PR summary
        id: summary
        uses: nejcm/pr-summary-github-action@v1.2.0
        with:
          ghToken: ${{ secrets.GH_TOKEN }}
          anthropicKey: ${{ secrets.ANTHROPIC_KEY }}
          notionKey: ${{ secrets.NOTION_API_KEY }}
          notionDbId: 'cd227b78703e499d81b902b402fcc128'
          linearKey: ${{ secrets.LINEAR_API_TOKEN }}
          linearViewId: '72c7011b3e8b'
          changelog:
            '${{ github.server_url }}/${{ github.repository }}/blob/${{ github.head_ref
            }}/packages/apps/web-app/CHANGELOG.md'
          prLink:
            '${{ github.server_url }}/${{ github.repository }}/pull/${{
            github.event.pull_request.number }}'
          version: 'v${{ steps.version.outputs.current-version }}'
          prompt:
            'Provide a set of Release Notes in Markdown format based on the following list of tasks
            that have been exported from Linear. These notes are for customers, so exclude anything
            technical or reference to internal or backend fixes / features. Make reference to high
            level features rather than specifics. Keep your notes fairly high level.'

      - name: 💬 Post summary comment
        if: steps.summary.outcome == 'success' && steps.summary.outputs.summary != ''
        uses: marocchino/sticky-pull-request-comment@v2
        with:
          GITHUB_TOKEN: ${{ secrets.GH_TOKEN }}
          number: ${{ github.event.pull_request.number }}
          header: 'Release Summary'
          message: |
            ${{ steps.summary.outputs.summary }}
