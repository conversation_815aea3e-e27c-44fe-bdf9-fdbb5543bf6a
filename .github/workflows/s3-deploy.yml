name: Build and deploy app to S3
on:
  push:
    branches: [develop, main]

  workflow_dispatch:
    inputs:
      target:
        description: 'which environment you are wanting to deploy'
        required: true
        default: 'main'
jobs:
  deploy:
    name: Running Deploy
    if:
      "contains(toJSON(github.event.commits.*.message), 'chore(release): @waitroom/web-app') ||
      contains(toJSON(github.event.commits.*.message), 'release:') || github.event_name ==
      'workflow_dispatch'"
    runs-on: ubuntu-latest
    timeout-minutes: 25
    env:
      APP_NAME: ${{ secrets.APP_NAME }}
      GH_TOKEN: ${{ secrets.GH_TOKEN }}
      FONTAWESOME_TOKEN: ${{ secrets.FONTAWESOME_TOKEN }}
      NX_SKIP_NX_CACHE: true

    steps:
      ## Checkout code
      - name: 🛒 Checkout code
        uses: actions/checkout@v4

      ## Notify Slack of Build Status
      - name: ℹ️ Deployment start notification
        uses: edge/simple-slack-notify@master
        with:
          text: '${{ env.APP_NAME }} ${{ github.head_ref || github.ref_name }} Deployment starting'
        env:
          SLACK_WEBHOOK_URL: ${{ secrets.SLACK_WEBHOOK_URL }}

      ## Configure aws credentials (Use AWS_GITLAB_ECR user)
      - name: ⚙️ Configure AWS credentials
        uses: aws-actions/configure-aws-credentials@v1
        with:
          aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID_NEW }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          aws-region: ${{ secrets.AWS_REGION}}

      # Determine the environment context
      - name: 🦺 Set environment context
        id: secrets_ctx
        run: |
          if [[ "${{ github.ref }}" == "refs/heads/main" ]]; then
            echo "VAULT=op://main-web-app" >> $GITHUB_OUTPUT
          else
            echo "VAULT=op://develop-web-app" >> $GITHUB_OUTPUT
          fi

      # Configure Connection to OnePassword Connect Operator (this is not 1pass directly but )
      - name: ⚙️ Configure 1Password Connect
        uses: 1password/load-secrets-action/configure@v1.2.0
        with:
          connect-host: https://op.ops.waitroom.com
          connect-token: ${{ secrets.OP_CONNECT_TOKEN }}

      # Load the environment variables that are required for the webapp to build correctly.
      - name: 🤫 Load 1Password secrets
        id: secrets
        if: ${{ success() }}
        uses: 1password/load-secrets-action@v2.0.0
        env:
          OP_CONNECT_TOKEN: ${{ secrets.OP_CONNECT_TOKEN }}
          NODE_ENV: ${{ steps.secrets_ctx.outputs.VAULT }}/node-env/credential
          VITE_ENVIRONMENT: ${{ steps.secrets_ctx.outputs.VAULT }}/environment/credential
          VITE_LOG_PROD: ${{ steps.secrets_ctx.outputs.VAULT }}/log-prod/credential
          VITE_AMPLITUDE_API_KEY:
            ${{ steps.secrets_ctx.outputs.VAULT }}/react-app-amplitude-api-key/credential
          VITE_APPLE_CLIENT_ID:
            ${{ steps.secrets_ctx.outputs.VAULT }}/react-app-apple-client-id/credential
          VITE_BASE_URL: ${{ steps.secrets_ctx.outputs.VAULT }}/react-app-base-url/credential
          VITE_BRANCH_API_KEY:
            ${{ steps.secrets_ctx.outputs.VAULT }}/react-app-branch-api-key/credential
          VITE_BRANCH_LINK_DOMAIN:
            ${{ steps.secrets_ctx.outputs.VAULT }}/react-app-branch-link-domain/credential
          VITE_BRANCH_STORE_SUFFIX:
            ${{ steps.secrets_ctx.outputs.VAULT }}/react-app-branch-store-suffix/credential
          VITE_PADDLE_ID: ${{ steps.secrets_ctx.outputs.VAULT }}/paddle-id/credential
          VITE_LIVEKIT_SERVER_URL:
            ${{ steps.secrets_ctx.outputs.VAULT }}/livekit-server-url/credential
          VITE_GETSTREAM_KEY: ${{ steps.secrets_ctx.outputs.VAULT }}/getstream-key/credential
          VITE_CDN_DOMAIN: ${{ steps.secrets_ctx.outputs.VAULT }}/react-app-cdn-domain/credential
          VITE_S3_RECORDINGS:
            ${{ steps.secrets_ctx.outputs.VAULT }}/react-app-s3-recordings/credential
          VITE_FEATURE_FLAGS:
            ${{ steps.secrets_ctx.outputs.VAULT }}/react-app-feature-flags/credential
          VITE_GENERATE_SOURCEMAP:
            ${{ steps.secrets_ctx.outputs.VAULT }}/react-app-generate-sourcemap/credential
          VITE_GOOGLE_CLIENT_ID:
            ${{ steps.secrets_ctx.outputs.VAULT }}/react-app-google-client-id/credential
          VITE_ORIGIN_TRIAL_TOKEN:
            ${{ steps.secrets_ctx.outputs.VAULT }}/origin-trial-token/credential
          VITE_SEGMENT_ID: ${{ steps.secrets_ctx.outputs.VAULT }}/react-app-segment-id/credential
          VITE_POSTHOG_KEY: ${{ steps.secrets_ctx.outputs.VAULT }}/react-app-posthog-key/credential
          VITE_GOOGLE_RECAPTCHA_KEY:
            ${{ steps.secrets_ctx.outputs.VAULT }}/react-app-google-recaptcha-key/credential
          VITE_GOOGLE_TAG_MANAGER:
            ${{ steps.secrets_ctx.outputs.VAULT }}/google-tag-manager/credential
          VITE_LUXOR_DOMAIN:
            ${{ steps.secrets_ctx.outputs.VAULT }}/react-app-luxor-domain/credential
          VITE_SENTRY_DSN: ${{ steps.secrets_ctx.outputs.VAULT }}/react-app-sentry-dsn/credential
          VITE_SENTRY_ENABLED:
            ${{ steps.secrets_ctx.outputs.VAULT }}/react-app-sentry-enabled/credential
          VITE_MAINTENANCE: ${{ steps.secrets_ctx.outputs.VAULT }}/react-app-maintenance/credential
          VITE_TYPEFORM_MEETING_ENDED_ID:
            ${{ steps.secrets_ctx.outputs.VAULT }}/react-app-typeform-meeting-ended-id/credential
          VITE_TYPEFORM_IN_MEETING_ID:
            ${{ steps.secrets_ctx.outputs.VAULT }}/react-app-typeform-in-meeting-id/credential
          VITE_WORMHOLE_DOMAIN:
            ${{ steps.secrets_ctx.outputs.VAULT }}/react-app-wormhole-domain/credential
          VITE_PEERMETRICS_KEY: ${{ steps.secrets_ctx.outputs.VAULT }}/peermetrics-key/credential
          VITE_PEERMETRICS_API_ROOT:
            ${{ steps.secrets_ctx.outputs.VAULT }}/peermetrics-api-root/credential
          USER_BRANCH: ${{ steps.secrets_ctx.outputs.VAULT }}/user-branch/credential
          VITE_EFFECTS_SDK_ID: ${{ steps.secrets_ctx.outputs.VAULT }}/effects-sdk-id/credential
          VITE_APP_URL_ORIGIN: ${{ steps.secrets_ctx.outputs.VAULT }}/app-url-origin/credential

      # Setup NPM creds and details to download packages
      - name: ✏️ Write .env to web-app
        run: |
          # Clear existing env file
          echo "" > packages/apps/web-app/.env
          # Loop all env variables
          while IFS='=' read -r key value; do
            if [[ $key == VITE_* || $key == NODE_ENV ]]; then
              echo "$key=$value" >> packages/apps/web-app/.env
            fi
          done < <(env)

      - name: 💿 Setup Nodejs
        uses: actions/setup-node@v4
        with:
          node-version: 22.11.0
          registry-url: 'https://npm.pkg.github.com'

      - name: 📥 Install dependencies
        shell: bash
        env:
          FONTAWESOME_TOKEN: ${{ env.FONTAWESOME_TOKEN }}
          GH_TOKEN: ${{ env.GH_TOKEN }}
        run: yarn install

      - name: 🔨 Build
        shell: bash
        run: |
          export NODE_OPTIONS="--max-old-space-size=4096"
          yarn build!

      - name: 🚀 Upload to develop bucket
        if: ${{ success() && github.head_ref || github.ref_name == 'develop' }}
        shell: bash
        run: aws s3 sync packages/apps/web-app/build s3://rumi-staging-web-app/ --no-progress

      - name: 🗑️ Invalidate cache for develop
        if: ${{ success() && github.head_ref || github.ref_name == 'develop' }}
        shell: bash
        run: aws cloudfront create-invalidation --distribution-id E2F84F55K7ZSRA --paths "/*"

      - name: 🚀 Upload to main bucket
        if: ${{ success() && github.head_ref || github.ref_name == 'main' }}
        shell: bash
        run: aws s3 sync packages/apps/web-app/build s3://rumi-main-web-app/ --no-progress

      - name: 🗑️ Invalidate cache for main
        if: ${{ success() && github.head_ref || github.ref_name == 'main' }}
        shell: bash
        run: aws cloudfront create-invalidation --distribution-id ERD61P7355UA9 --paths "/*"

      - name: ℹ️ Deployment complete notification
        uses: edge/simple-slack-notify@master
        with:
          text:
            '${{ env.APP_NAME }} ${{ github.head_ref || github.ref_name }} Deployment has completed'
        env:
          SLACK_WEBHOOK_URL: ${{ secrets.SLACK_WEBHOOK_URL }}
