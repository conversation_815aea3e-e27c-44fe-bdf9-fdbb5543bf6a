import type { StorybookConfig } from '@storybook/react-vite';
import { type InlineConfig, mergeConfig } from 'vite';

export default {
  stories: [
    '../stories/**/*.mdx',
    '../packages/**/*.mdx',
    '../packages/**/*.stories.@(js|jsx|ts|tsx)',
  ],
  addons: ['@storybook/addon-essentials', '@storybook/addon-interactions', '@storybook/theming'],
  framework: '@storybook/react-vite',
  core: {
    builder: {
      name: '@storybook/builder-vite',
      options: {},
    },
  },
  async viteFinal(config): Promise<InlineConfig> {
    // Merge custom configuration into the default config
    return mergeConfig(config, {
      optimizeDeps: {},
      resolve: {
        alias: [
          {
            find: '@',
            replacement: '/packages/apps/web-app/src',
          },
          {
            find: '@modules',
            replacement: '/packages/apps/web-app/src/modules',
          },
          {
            find: '@core',
            replacement: '/packages/apps/web-app/src/modules/core',
          },
          {
            find: '@ai',
            replacement: '/packages/apps/web-app/src/modules/ai',
          },
          {
            find: '@constants',
            replacement: '/packages/apps/web-app/src/constants',
          },
          {
            find: '@locales',
            replacement: '/packages/apps/web-app/src/modules/locales',
          },
          {
            find: '@sounds',
            replacement: '/packages/apps/web-app/src/sounds',
          },
          {
            find: '@tests',
            replacement: '/packages/apps/web-app/src/tests',
          },
          {
            find: '@auth',
            replacement: '/packages/apps/web-app/src/modules/auth',
          },
          {
            find: '@analytics',
            replacement: '/packages/apps/web-app/src/modules/analytics',
          },
          {
            find: '@session',
            replacement: '/packages/apps/web-app/src/modules/session',
          },
          {
            find: '@dashboard',
            replacement: '/packages/apps/web-app/src/modules/dashboard',
          },
          {
            find: '@xray',
            replacement: '/packages/apps/web-app/src/modules/xray',
          },
        ],
      },
    } as InlineConfig);
  },
  refs: {
    '@chakra-ui/react': { disable: true },
  },
  typescript: {
    check: false,
  },
} as StorybookConfig;
