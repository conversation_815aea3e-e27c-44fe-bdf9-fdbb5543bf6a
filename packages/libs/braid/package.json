{"name": "@waitroom/braid", "version": "0.22.8", "license": "UNLICENSED", "author": "Rumi <<EMAIL>>", "description": "Rumi braid", "type": "module", "source": "src/index.ts", "types": "./dist/index.d.ts", "main": "./dist/index.es.js", "module": "./dist/index.es.js", "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.es.js"}}, "repository": {"type": "git", "url": "git+https://github.com/Waitroom/rumi.ai"}, "bugs": {"url": "https://github.com/Waitroom/rumi.ai/issues"}, "keywords": [], "files": ["dist"], "scripts": {"start": "echo 'Noop'", "build": "rimraf dist && vite build && tsc --emitDeclarationOnly --project tsconfig.build.json", "build:libs": "yarn run build", "test": "vitest run", "test:cov": "vitest run --coverage", "test:nocov": "vitest run", "test:watch": "vitest", "test:related": "cross-env CI=true vitest related --run --passWithNoTests", "lint": "yarn run eslint", "lint:ts": "tsc --noEmit", "format": "prettier . --write", "publish": "yarn npm publish || true"}, "peerDependencies": {"@waitroom/logger": "workspace:*", "@waitroom/models": "workspace:*", "@waitroom/utils": "workspace:*"}, "devDependencies": {"@types/node": "~22.15.14", "@waitroom/logger": "workspace:*", "@waitroom/models": "workspace:*", "@waitroom/utils": "workspace:*", "eslint": "~9.24.0", "fast-json-patch": "~3.1.1", "happy-dom": "~17.5.6", "prettier": "~3.5.3", "typescript": "~5.8.3", "vite": "~6.3.5", "vitest": "~3.1.3", "vitest-dom": "~0.1.1"}, "publishConfig": {"registry": "https://npm.pkg.github.com/"}}