import { useMutation, UseMutationOptions, useQuery, UseQueryResult } from '@tanstack/react-query';
import {
  ApiParams,
  DefaultApiResponse,
  EventSourceMessageCallback,
  FetchEventSourceInit,
  MeetingMemoryApiService,
  meetingMemoryApiService,
} from '@waitroom/common-api';
import { logger } from '@waitroom/logger';
import { LOGGER_MEETING_MEMORY } from '@waitroom/models';
import {
  CACHE_KEY_MEETING_MEMORY_SUGGESTIONS,
  inactiveOptions,
  UseQueryOptionsWithoutQueryKey,
} from '@waitroom/react-query';
import { useCallback, useEffect, useRef } from 'react';
import { usePrivateQueryConfig } from './usePrivateQueryConfig';

export const useAi = (
  key: string,
  onMessage: EventSourceMessageCallback,
  options?: UseMutationOptions<
    MeetingMemoryApiService.Ai['response'],
    DefaultApiResponse,
    { data: MeetingMemoryApiService.Ai['data'] } & FetchEventSourceInit
  >,
  fetchOptions?: FetchEventSourceInit,
) => {
  const abortRef = useRef<AbortController | undefined>(undefined);

  const { mutate, reset, ...rest } = useMutation({
    mutationFn: ({ data, ...opts }) =>
      meetingMemoryApiService.ai(onMessage, { data }, { ...fetchOptions, ...opts }),
    ...options,
  });

  const ask = useCallback(
    async (data: MeetingMemoryApiService.Ai['data']) => {
      abortRef.current = new AbortController();
      return mutate({ data, signal: abortRef.current.signal });
    },
    [mutate],
  );

  // abort previous requests on thread change and reset mutation
  useEffect(() => {
    const abortController = abortRef.current;
    return () => {
      if (!abortController) return;
      reset();
      logger.logService(LOGGER_MEETING_MEMORY, 4, 'Closing request', key);
      abortController.abort('Aborting Meeting Memory request');
    };
  }, [key, reset]);

  return {
    ...rest,
    ask,
    reset,
  };
};

export const useFeedbackMutation = (
  options?: UseMutationOptions<
    MeetingMemoryApiService.Feedback['response'],
    DefaultApiResponse,
    MeetingMemoryApiService.Feedback['data']
  >,
) => {
  return useMutation({
    mutationFn: ({ messageId, feedback }) =>
      meetingMemoryApiService.feedback({
        data: { messageId, feedback },
      }),
    ...options,
  });
};

export const useStopGeneration = (
  options?: UseMutationOptions<
    MeetingMemoryApiService.Stop['response'],
    DefaultApiResponse,
    MeetingMemoryApiService.Stop['data']
  >,
) => {
  return useMutation({
    mutationFn: ({ threadId }) =>
      meetingMemoryApiService.stop({
        data: { threadId },
      }),
    ...options,
  });
};

export const useSuggestions = (
  params?: ApiParams<MeetingMemoryApiService.Suggestions['params']>,
  options?: UseQueryOptionsWithoutQueryKey<
    MeetingMemoryApiService.Suggestions['response'],
    DefaultApiResponse
  >,
): UseQueryResult<MeetingMemoryApiService.Suggestions['response'], DefaultApiResponse> => {
  const [config] = usePrivateQueryConfig([CACHE_KEY_MEETING_MEMORY_SUGGESTIONS, params]);
  return useQuery({
    queryFn: () => meetingMemoryApiService.suggestions(params),
    ...inactiveOptions['30m'],
    ...options,
    ...config,
  });
};
