# Changelog

This file was generated using [@jscutlery/semver](https://github.com/jscutlery/semver).

## [1.72.0](https://github.com/Waitroom/rumi.ai/compare/@waitroom/analytics@1.71.13...@waitroom/analytics@1.72.0) (2025-06-24)


### Features

* WEB-2437 upgrade to react 19 ([#9](https://github.com/Waitroom/rumi.ai/issues/9)) ([465dae5](https://github.com/Waitroom/rumi.ai/commit/465dae527ff8f2ebfe9f3914676cd41e56af982e))

## [1.71.13](https://github.com/Waitroom/rumi.ai/compare/@waitroom/analytics@1.71.12...@waitroom/analytics@1.71.13) (2025-06-14)

## [1.71.12](https://github.com/Waitroom/rumi.ai/compare/@waitroom/analytics@1.71.11...@waitroom/analytics@1.71.12) (2025-06-05)

## [1.71.11](https://github.com/Waitroom/rumi.ai/compare/@waitroom/analytics@1.71.10...@waitroom/analytics@1.71.11) (2025-05-27)

## [1.71.10](https://github.com/Waitroom/rumi.ai/compare/@waitroom/analytics@1.71.9...@waitroom/analytics@1.71.10) (2025-05-27)

## [1.71.9](https://github.com/Waitroom/waitroom/compare/@waitroom/analytics@1.71.8...@waitroom/analytics@1.71.9) (2025-05-14)

## [1.71.8](https://github.com/Waitroom/waitroom/compare/@waitroom/analytics@1.71.7...@waitroom/analytics@1.71.8) (2025-05-14)

## [1.71.7](https://github.com/Waitroom/waitroom/compare/@waitroom/analytics@1.71.6...@waitroom/analytics@1.71.7) (2025-05-14)


### Bug Fixes

* added lobby session started to analytics ([#2691](https://github.com/Waitroom/waitroom/issues/2691)) ([c7007b0](https://github.com/Waitroom/waitroom/commit/c7007b052b7bfa6e8b5dbbc11218dffb27147445))

## [1.71.6](https://github.com/Waitroom/waitroom/compare/@waitroom/analytics@1.71.5...@waitroom/analytics@1.71.6) (2025-05-14)

## [1.71.5](https://github.com/Waitroom/waitroom/compare/@waitroom/analytics@1.71.4...@waitroom/analytics@1.71.5) (2025-05-08)


### Bug Fixes

* restart button ([#2685](https://github.com/Waitroom/waitroom/issues/2685)) ([ef6c05c](https://github.com/Waitroom/waitroom/commit/ef6c05cf547eff2e79d523a4aeef1493b3e00f64))

## [1.71.4](https://github.com/Waitroom/waitroom/compare/@waitroom/analytics@1.71.3...@waitroom/analytics@1.71.4) (2025-05-07)


### Bug Fixes

* mic not being initialized ([#2674](https://github.com/Waitroom/waitroom/issues/2674)) ([42940d9](https://github.com/Waitroom/waitroom/commit/42940d9b811f91dc42276c868fe83c0861e7ec34))

## [1.71.3](https://github.com/Waitroom/waitroom/compare/@waitroom/analytics@1.71.2...@waitroom/analytics@1.71.3) (2025-05-05)

## [1.71.2](https://github.com/Waitroom/waitroom/compare/@waitroom/analytics@1.71.1...@waitroom/analytics@1.71.2) (2025-04-30)


### Bug Fixes

* player credentials, package versions ([8606cf0](https://github.com/Waitroom/waitroom/commit/8606cf0ae3be75b089ac8d155709ff705ba5be99))
* release versions ([3d66654](https://github.com/Waitroom/waitroom/commit/3d66654f6ca27fe13eb1a4fb7afc46f39da0d0fd))

## 1.71.1 (2025-04-30)

### Features

* added analytics for lobby events ([#2650](https://github.com/Waitroom/rumi.ai/issues/2650)) ([2e93215](https://github.com/Waitroom/waitroom/commit/2e9321515256b6ce9c764e9a49c723d70de0455f))
* meeting selector ([#2538](https://github.com/Waitroom/rumi.ai/issues/2538)) ([b7c072b](https://github.com/Waitroom/waitroom/commit/b7c072b2f57c92f6683e5938d33603257ba732eb))

## [1.71.1](https://github.com/Waitroom/waitroom/compare/@waitroom/analytics@1.71.0...@waitroom/analytics@1.71.1) (2025-04-25)

## [1.71.0](https://github.com/Waitroom/waitroom/compare/@waitroom/analytics@1.70.0...@waitroom/analytics@1.71.0) (2025-04-24)


### Features

* added analytics for lobby events ([#2650](https://github.com/Waitroom/rumi.ai/issues/2650)) ([2e93215](https://github.com/Waitroom/waitroom/commit/2e9321515256b6ce9c764e9a49c723d70de0455f))

## [1.70.0](https://github.com/Waitroom/waitroom/compare/@waitroom/analytics@1.69.13...@waitroom/analytics@1.70.0) (2025-03-27)


### Features

* meeting selector ([#2538](https://github.com/Waitroom/rumi.ai/issues/2538)) ([b7c072b](https://github.com/Waitroom/waitroom/commit/b7c072b2f57c92f6683e5938d33603257ba732eb))

## [1.69.13](https://github.com/Waitroom/waitroom/compare/@waitroom/analytics@1.69.12...@waitroom/analytics@1.69.13) (2025-03-27)

## [1.69.12](https://github.com/Waitroom/waitroom/compare/@waitroom/analytics@1.69.11...@waitroom/analytics@1.69.12) (2025-03-24)

## [1.69.11](https://github.com/Waitroom/waitroom/compare/@waitroom/analytics@1.69.10...@waitroom/analytics@1.69.11) (2025-03-19)

## [1.69.10](https://github.com/Waitroom/waitroom/compare/@waitroom/analytics@1.69.9...@waitroom/analytics@1.69.10) (2025-03-18)

## [1.69.9](https://github.com/Waitroom/waitroom/compare/@waitroom/analytics@1.69.8...@waitroom/analytics@1.69.9) (2025-03-06)


### Bug Fixes

* revert posthog warning ([#2541](https://github.com/Waitroom/rumi.ai/issues/2541)) ([#2546](https://github.com/Waitroom/rumi.ai/issues/2546)) ([802266d](https://github.com/Waitroom/waitroom/commit/802266df4dd9e68139fc5a2fe729c61cae192042))

## [1.69.8](https://github.com/Waitroom/waitroom/compare/@waitroom/analytics@1.69.7...@waitroom/analytics@1.69.8) (2025-03-04)


### Bug Fixes

* posthog warning ([#2541](https://github.com/Waitroom/rumi.ai/issues/2541)) ([f918f13](https://github.com/Waitroom/waitroom/commit/f918f13619ce612565fc300a69f85ac107b64dad))

## [1.69.7](https://github.com/Waitroom/waitroom/compare/@waitroom/analytics@1.69.6...@waitroom/analytics@1.69.7) (2025-02-17)

## [1.69.6](https://github.com/Waitroom/waitroom/compare/@waitroom/analytics@1.69.5...@waitroom/analytics@1.69.6) (2025-02-10)


### Bug Fixes

* meta image links ([a55a14a](https://github.com/Waitroom/waitroom/commit/a55a14af74fb316d5026f539f92fc24d608f9c15))

## [1.69.5](https://github.com/Waitroom/waitroom/compare/@waitroom/analytics@1.69.4...@waitroom/analytics@1.69.5) (2025-02-06)


### Bug Fixes

* add analytics events ([0404f86](https://github.com/Waitroom/waitroom/commit/0404f864fec8b926e6119d9f3263e8f7a525cfd5))

## [1.69.4](https://github.com/Waitroom/waitroom/compare/@waitroom/analytics@1.69.3...@waitroom/analytics@1.69.4) (2025-02-03)

## [1.69.3](https://github.com/Waitroom/waitroom/compare/@waitroom/analytics@1.69.2...@waitroom/analytics@1.69.3) (2025-01-27)


### Bug Fixes

* web 2462 presence on prescheduled screen is sometimes failing ([#2448](https://github.com/Waitroom/rumi.ai/issues/2448)) ([162c774](https://github.com/Waitroom/waitroom/commit/162c774e5ff506e701277f69c9ad47540a7f2e22))

## [1.69.2](https://github.com/Waitroom/waitroom/compare/@waitroom/analytics@1.69.1...@waitroom/analytics@1.69.2) (2025-01-21)

## [1.69.1](https://github.com/Waitroom/waitroom/compare/@waitroom/analytics@1.69.0...@waitroom/analytics@1.69.1) (2025-01-20)


### Reverts

* "feat: web 2462 presence on prescheduled screen is sometimes failing ([#2445](https://github.com/Waitroom/rumi.ai/issues/2445))" ([e710c29](https://github.com/Waitroom/waitroom/commit/e710c29851de8231c421af0260f53122906fde8d))

## [1.69.0](https://github.com/Waitroom/waitroom/compare/@waitroom/analytics@1.68.0...@waitroom/analytics@1.69.0) (2025-01-20)


### Features

* web 2462 presence on prescheduled screen is sometimes failing ([#2445](https://github.com/Waitroom/rumi.ai/issues/2445)) ([5a49bb3](https://github.com/Waitroom/waitroom/commit/5a49bb35e62775ff470931dcac0e5fcfd3fa387b))

## [1.68.0](https://github.com/Waitroom/waitroom/compare/@waitroom/analytics@1.67.7...@waitroom/analytics@1.68.0) (2025-01-17)


### Features

* update livekit ([e6bc163](https://github.com/Waitroom/waitroom/commit/e6bc163ef1b5fdae4c3b3f350bb0adcd593c642b))

## [1.67.7](https://github.com/Waitroom/waitroom/compare/@waitroom/analytics@1.67.6...@waitroom/analytics@1.67.7) (2025-01-14)


### Reverts

* Revert "fix: race condition between onPatch and react-query queryFn promise resolve (#2444)" ([32fee7d](https://github.com/Waitroom/waitroom/commit/32fee7ddd6bc27bd3dca037ff96a8500217da07c)), closes [#2444](https://github.com/Waitroom/rumi.ai/issues/2444)

## [1.67.6](https://github.com/Waitroom/waitroom/compare/@waitroom/analytics@1.67.5...@waitroom/analytics@1.67.6) (2025-01-14)


### Bug Fixes

* race condition between onPatch and react-query queryFn promise resolve ([#2444](https://github.com/Waitroom/rumi.ai/issues/2444)) ([d82e57d](https://github.com/Waitroom/waitroom/commit/d82e57db9cb9563df7b2be4aefe3939b1d031d8d))

## [1.67.5](https://github.com/Waitroom/waitroom/compare/@waitroom/analytics@1.67.4...@waitroom/analytics@1.67.5) (2025-01-10)

## [1.67.4](https://github.com/Waitroom/waitroom/compare/@waitroom/analytics@1.67.3...@waitroom/analytics@1.67.4) (2024-12-18)

## [1.67.3](https://github.com/Waitroom/waitroom/compare/@waitroom/analytics@1.67.2...@waitroom/analytics@1.67.3) (2024-12-11)

## [1.67.2](https://github.com/Waitroom/waitroom/compare/@waitroom/analytics@1.67.1...@waitroom/analytics@1.67.2) (2024-12-11)


### Bug Fixes

* braid service retry logic ([2309de2](https://github.com/Waitroom/waitroom/commit/2309de2e22adb8d310816405f2fe73c1af157173))

## [1.67.1](https://github.com/Waitroom/waitroom/compare/@waitroom/analytics@1.67.0...@waitroom/analytics@1.67.1) (2024-12-09)

## [1.67.0](https://github.com/Waitroom/waitroom/compare/@waitroom/analytics@1.66.4...@waitroom/analytics@1.67.0) (2024-12-06)


### Features

* updated effects UI ([#2402](https://github.com/Waitroom/rumi.ai/issues/2402)) ([aa72e48](https://github.com/Waitroom/waitroom/commit/aa72e488ded21b4025f7c8b103d87b3552e09baf))

## [1.66.4](https://github.com/Waitroom/waitroom/compare/@waitroom/analytics@1.66.3...@waitroom/analytics@1.66.4) (2024-12-04)

## [1.66.3](https://github.com/Waitroom/waitroom/compare/@waitroom/analytics@1.66.2...@waitroom/analytics@1.66.3) (2024-12-04)

## [1.66.2](https://github.com/Waitroom/waitroom/compare/@waitroom/analytics@1.66.1...@waitroom/analytics@1.66.2) (2024-12-02)

## [1.66.1](https://github.com/Waitroom/waitroom/compare/@waitroom/analytics@1.66.0...@waitroom/analytics@1.66.1) (2024-12-02)


### Bug Fixes

* undefined track check, refactored cam and mic init, updated packages ([81fb7c1](https://github.com/Waitroom/waitroom/commit/81fb7c1bc7ce6e713272da1632bf86faea0e849c))

## [1.66.0](https://github.com/Waitroom/waitroom/compare/@waitroom/analytics@1.65.2...@waitroom/analytics@1.66.0) (2024-11-28)


### Features

* effects sdk ([#2372](https://github.com/Waitroom/rumi.ai/issues/2372)) ([fe8a8fe](https://github.com/Waitroom/waitroom/commit/fe8a8fe237f79bd663980c6c8fdbb937858e8d1a))

## [1.65.2](https://github.com/Waitroom/waitroom/compare/@waitroom/analytics@1.65.1...@waitroom/analytics@1.65.2) (2024-11-21)


### Bug Fixes

* ai feed subscription issue, eslint rules ([344017a](https://github.com/Waitroom/waitroom/commit/344017a37212f9ab5ce602a1b1129d60004375bd))

## [1.65.1](https://github.com/Waitroom/waitroom/compare/@waitroom/analytics@1.65.0...@waitroom/analytics@1.65.1) (2024-10-31)


### Bug Fixes

* users should be redirect to the stores when selecting to download the app ([f71323e](https://github.com/Waitroom/waitroom/commit/f71323ec83ce5b2df0d8ddc22d13751214b177c7))

## [1.65.0](https://github.com/Waitroom/waitroom/compare/@waitroom/analytics@1.64.0...@waitroom/analytics@1.65.0) (2024-10-22)


### Features

* rerelease packages ([573ab7c](https://github.com/Waitroom/waitroom/commit/573ab7c131b0f5c9dab2872e706ae7d677e46d60))

## [1.64.0](https://github.com/Waitroom/waitroom/compare/@waitroom/analytics@1.63.2...@waitroom/analytics@1.64.0) (2024-10-22)


### Features

* rerelease all packages ([ec0b06c](https://github.com/Waitroom/waitroom/commit/ec0b06cd0e5795a9bfa3f19bc6e1c2975975b346))

## [1.63.2](https://github.com/Waitroom/waitroom/compare/@waitroom/analytics@1.63.1...@waitroom/analytics@1.63.2) (2024-10-22)

## [1.63.1](https://github.com/Waitroom/waitroom/compare/@waitroom/analytics@1.63.0...@waitroom/analytics@1.63.1) (2024-10-22)


### Bug Fixes

* add state and code challenge params to auth requests ([33ce9ec](https://github.com/Waitroom/waitroom/commit/33ce9ecf8d684c0ff32d2cb6deaff59ca347d234))

## [1.63.0](https://github.com/Waitroom/waitroom/compare/@waitroom/analytics@1.62.0...@waitroom/analytics@1.63.0) (2024-10-17)


### Features

* meeting memory threads UI update ([#2284](https://github.com/Waitroom/rumi.ai/issues/2284)) ([07ba86a](https://github.com/Waitroom/waitroom/commit/07ba86afde2a70bfa173bd12a56927109ec21e94))
* reenable personalized suggestions ([#2280](https://github.com/Waitroom/rumi.ai/issues/2280)) ([70884fd](https://github.com/Waitroom/waitroom/commit/70884fd5b13c788bf5ad8fe64f0d50cf210c43bf))

## [1.62.0](https://github.com/Waitroom/waitroom/compare/@waitroom/analytics@1.61.1...@waitroom/analytics@1.62.0) (2024-10-01)


### Features

* project frictionless guest access ([#2233](https://github.com/Waitroom/rumi.ai/issues/2233)) ([948ddca](https://github.com/Waitroom/waitroom/commit/948ddca1047ed6394c9b5744bd4a4e3d4295636d))

## [1.61.1](https://github.com/Waitroom/waitroom/compare/@waitroom/analytics@1.61.0...@waitroom/analytics@1.61.1) (2024-09-10)

## [1.61.0](https://github.com/Waitroom/waitroom/compare/@waitroom/analytics@1.60.1...@waitroom/analytics@1.61.0) (2024-09-04)


### Features

* adds peermetric SDK to Livekit room ([#2197](https://github.com/Waitroom/rumi.ai/issues/2197)) ([5411842](https://github.com/Waitroom/waitroom/commit/541184245d04426e527ba53d2c7f4f6eeb9d0d08))

## [1.60.1](https://github.com/Waitroom/waitroom/compare/@waitroom/analytics@1.60.0...@waitroom/analytics@1.60.1) (2024-09-03)


### Reverts

* braid service change ([a9501d9](https://github.com/Waitroom/waitroom/commit/a9501d9b2fff992c9ec4afb36a97a73310f7b7db))

## [1.60.0](https://github.com/Waitroom/waitroom/compare/@waitroom/analytics@1.59.3...@waitroom/analytics@1.60.0) (2024-09-03)


### Features

* personalized suggestions ([#2201](https://github.com/Waitroom/rumi.ai/issues/2201)) ([10fd13d](https://github.com/Waitroom/waitroom/commit/10fd13dd7afdece1feeb56b0b30f0a75949bb8d9))

## [1.59.3](https://github.com/Waitroom/waitroom/compare/@waitroom/analytics@1.59.2...@waitroom/analytics@1.59.3) (2024-08-20)

## [1.59.2](https://github.com/Waitroom/waitroom/compare/@waitroom/analytics@1.59.1...@waitroom/analytics@1.59.2) (2024-08-15)


### Bug Fixes

* tests ([07ecc97](https://github.com/Waitroom/waitroom/commit/07ecc97c81ab4a999e2caf21227f1febee530dfd))

## [1.59.1](https://github.com/Waitroom/waitroom/compare/@waitroom/analytics@1.59.0...@waitroom/analytics@1.59.1) (2024-08-15)

## [1.59.0](https://github.com/Waitroom/waitroom/compare/@waitroom/analytics@1.58.3...@waitroom/analytics@1.59.0) (2024-08-14)


### Features

* mm streaming ([#2148](https://github.com/Waitroom/rumi.ai/issues/2148)) ([5943da9](https://github.com/Waitroom/waitroom/commit/5943da912d1708a15cf8b45a48913f1d2d952d7d))

## [1.58.3](https://github.com/Waitroom/waitroom/compare/@waitroom/analytics@1.58.2...@waitroom/analytics@1.58.3) (2024-08-02)

## [1.58.2](https://github.com/Waitroom/waitroom/compare/@waitroom/analytics@1.58.1...@waitroom/analytics@1.58.2) (2024-07-25)


### Bug Fixes

* ux meeting memory ([#2122](https://github.com/Waitroom/rumi.ai/issues/2122)) ([6aabf4a](https://github.com/Waitroom/waitroom/commit/6aabf4a585d4c451af78da1fc01e605a2fa316a0))

## [1.58.1](https://github.com/Waitroom/waitroom/compare/@waitroom/analytics@1.58.0...@waitroom/analytics@1.58.1) (2024-07-24)


### Bug Fixes

* import issue ([0f89549](https://github.com/Waitroom/waitroom/commit/0f8954914924db9d1b2ccc7c1cb9649cda2e488b))

## [1.58.0](https://github.com/Waitroom/waitroom/compare/@waitroom/analytics@1.57.1...@waitroom/analytics@1.58.0) (2024-07-24)


### Features

* setTimeout instead of setInterval ([#2128](https://github.com/Waitroom/rumi.ai/issues/2128)) ([9aab675](https://github.com/Waitroom/waitroom/commit/9aab6754587a46bab69dcb95b0a83286c04fc911))

## [1.57.1](https://github.com/Waitroom/waitroom/compare/@waitroom/analytics@1.57.0...@waitroom/analytics@1.57.1) (2024-07-22)

## [1.57.0](https://github.com/Waitroom/waitroom/compare/@waitroom/analytics@1.56.1...@waitroom/analytics@1.57.0) (2024-07-19)


### Features

* braid query cancellation ([fe02cd9](https://github.com/Waitroom/waitroom/commit/fe02cd96621a6880a5375806ae57df7e4c024f94))

## [1.56.1](https://github.com/Waitroom/waitroom/compare/@waitroom/analytics@1.56.0...@waitroom/analytics@1.56.1) (2024-07-18)

## [1.56.0](https://github.com/Waitroom/waitroom/compare/@waitroom/analytics@1.55.1...@waitroom/analytics@1.56.0) (2024-07-09)


### Features

* web-2011 meeting memory landing page ([#2115](https://github.com/Waitroom/rumi.ai/issues/2115)) ([e86833f](https://github.com/Waitroom/waitroom/commit/e86833f075ef0308ef4840d43cea5dfa6bc989d7))

## [1.55.1](https://github.com/Waitroom/waitroom/compare/@waitroom/analytics@1.55.0...@waitroom/analytics@1.55.1) (2024-07-09)

## [1.55.0](https://github.com/Waitroom/waitroom/compare/@waitroom/analytics@1.54.5...@waitroom/analytics@1.55.0) (2024-05-31)


### Features

* request user delete ([#2071](https://github.com/Waitroom/rumi.ai/issues/2071)) ([711e3f0](https://github.com/Waitroom/waitroom/commit/711e3f0e4a27aa6f41bea9c85ef95a695ad3e735))

## [1.54.5](https://github.com/Waitroom/waitroom/compare/@waitroom/analytics@1.54.4...@waitroom/analytics@1.54.5) (2024-05-15)


### Bug Fixes

* host page, refactor browser service ([6426b6d](https://github.com/Waitroom/waitroom/commit/6426b6d9ff10440b0358003f3dadf1eb068dc0ac))

## [1.54.4](https://github.com/Waitroom/waitroom/compare/@waitroom/analytics@1.54.3...@waitroom/analytics@1.54.4) (2024-05-15)

## [1.54.3](https://github.com/Waitroom/waitroom/compare/@waitroom/analytics@1.54.2...@waitroom/analytics@1.54.3) (2024-05-14)

## [1.54.2](https://github.com/Waitroom/waitroom/compare/@waitroom/analytics@1.54.1...@waitroom/analytics@1.54.2) (2024-05-09)

## [1.54.1](https://github.com/Waitroom/waitroom/compare/@waitroom/analytics@1.54.0...@waitroom/analytics@1.54.1) (2024-04-30)

## [1.54.0](https://github.com/Waitroom/waitroom/compare/@waitroom/analytics@1.53.6...@waitroom/analytics@1.54.0) (2024-03-26)


### Features

* WEB-1685 implement the satisfaction survey on the end screen ([#1976](https://github.com/Waitroom/rumi.ai/issues/1976)) ([a16d6a5](https://github.com/Waitroom/waitroom/commit/a16d6a51123eba45e76a7ff666413cea8520806e))

## [1.53.6](https://github.com/Waitroom/waitroom/compare/@waitroom/analytics@1.53.5...@waitroom/analytics@1.53.6) (2024-03-14)


### Bug Fixes

* release tags ([b9c0c0d](https://github.com/Waitroom/waitroom/commit/b9c0c0d8c2958a650fe6b27de55d73de9085b1cd))

## [1.53.5](https://github.com/Waitroom/waitroom/compare/@waitroom/analytics@1.53.4...@waitroom/analytics@1.53.5) (2024-03-14)

## [1.53.4](https://github.com/Waitroom/waitroom/compare/@waitroom/analytics@1.53.3...@waitroom/analytics@1.53.4) (2024-02-29)

## [1.53.3](https://github.com/Waitroom/waitroom/compare/@waitroom/analytics@1.53.2...@waitroom/analytics@1.53.3) (2024-02-28)


### Bug Fixes

* session form ([2222453](https://github.com/Waitroom/waitroom/commit/22224531576aeeb0238dc78792b9331f2e8299ec))

## [1.53.2](https://github.com/Waitroom/waitroom/compare/@waitroom/analytics@1.53.1...@waitroom/analytics@1.53.2) (2024-02-27)

## [1.53.1](https://github.com/Waitroom/waitroom/compare/@waitroom/analytics@1.53.0...@waitroom/analytics@1.53.1) (2024-02-20)

## [1.53.0](https://github.com/Waitroom/waitroom/compare/@waitroom/analytics@1.52.2...@waitroom/analytics@1.53.0) (2024-02-19)


### Features

* WEB-1341 re-render recordings page on auth state change ([#1922](https://github.com/Waitroom/rumi.ai/issues/1922)) ([bbb1685](https://github.com/Waitroom/waitroom/commit/bbb16850a124b8301cb52e0ef96e2fae59d157f6))

## [1.52.2](https://github.com/Waitroom/waitroom/compare/@waitroom/analytics@1.52.1...@waitroom/analytics@1.52.2) (2024-02-16)


### Bug Fixes

* use branch long links rather to honour no attribution flag ([f1c11ac](https://github.com/Waitroom/waitroom/commit/f1c11ace08245e7a373d2f8e079f488e9453cc09))

## [1.52.1](https://github.com/Waitroom/waitroom/compare/@waitroom/analytics@1.52.0...@waitroom/analytics@1.52.1) (2024-02-06)

## [1.52.0](https://github.com/Waitroom/waitroom/compare/@waitroom/analytics@1.51.1...@waitroom/analytics@1.52.0) (2024-02-02)


### Features

* add super clarity beta feature ([ed74ddd](https://github.com/Waitroom/waitroom/commit/ed74dddd175c364dd9aa07aa815a1cceb7d121bb))

## [1.51.1](https://github.com/Waitroom/waitroom/compare/@waitroom/analytics@1.51.0...@waitroom/analytics@1.51.1) (2024-01-29)


### Bug Fixes

* removed punjab ([9f61111](https://github.com/Waitroom/waitroom/commit/9f61111a0bdc3f97b7de8f26825e27694bc07aa4))

## [1.51.0](https://github.com/Waitroom/waitroom/compare/@waitroom/analytics@1.50.1...@waitroom/analytics@1.51.0) (2024-01-29)


### Features

* project monetization ([#1841](https://github.com/Waitroom/rumi.ai/issues/1841)) ([edbaf07](https://github.com/Waitroom/waitroom/commit/edbaf0700bd694d2d195efd484d637dec1c97acb))

## [1.50.1](https://github.com/Waitroom/waitroom/compare/@waitroom/analytics@1.50.0...@waitroom/analytics@1.50.1) (2024-01-02)

## [1.50.0](https://github.com/Waitroom/waitroom/compare/@waitroom/analytics@1.49.0...@waitroom/analytics@1.50.0) (2023-12-22)


### Features

* web 1216 allow for more stream layouts that users can pick from ([#1749](https://github.com/Waitroom/rumi.ai/issues/1749)) ([55762bd](https://github.com/Waitroom/waitroom/commit/55762bdf2263a913602169dd6fbc35e224f0db52))


### Bug Fixes

* release ([606066c](https://github.com/Waitroom/waitroom/commit/606066ce01a7d2c09339d3e624cc452ff034bf4f))

## [1.49.0](https://github.com/Waitroom/waitroom/compare/@waitroom/analytics@1.48.1...@waitroom/analytics@1.49.0) (2023-12-18)


### Features

* add integration analytics ([b709228](https://github.com/Waitroom/waitroom/commit/b709228b3501b24de961e779dca017549d697637))

## [1.48.1](https://github.com/Waitroom/waitroom/compare/@waitroom/analytics@1.48.0...@waitroom/analytics@1.48.1) (2023-11-17)

## [1.48.0](https://github.com/Waitroom/waitroom/compare/@waitroom/analytics@1.47.2...@waitroom/analytics@1.48.0) (2023-11-15)


### Features

* add analytics off the record ([36e879f](https://github.com/Waitroom/waitroom/commit/36e879f011dc09edf0ee6aca07f711dc2646cabf))

## [1.47.2](https://github.com/Waitroom/waitroom/compare/@waitroom/analytics@1.47.1...@waitroom/analytics@1.47.2) (2023-10-23)


### Bug Fixes

* effect initialization ([#1737](https://github.com/Waitroom/rumi.ai/issues/1737)) ([42543a1](https://github.com/Waitroom/waitroom/commit/42543a11dc3e91331b77e49c7b4a7776f8e5ee6c))

## [1.47.1](https://github.com/Waitroom/waitroom/compare/@waitroom/analytics@1.47.0...@waitroom/analytics@1.47.1) (2023-10-23)


### Bug Fixes

* add analytics around feed item editing ([7dfa36e](https://github.com/Waitroom/waitroom/commit/7dfa36e5740be198702a1753706a68e21dee4369))

## [1.47.0](https://github.com/Waitroom/waitroom/compare/@waitroom/analytics@1.46.0...@waitroom/analytics@1.47.0) (2023-10-04)


### Features

* action item editing milestone 2 ([#1614](https://github.com/Waitroom/rumi.ai/issues/1614)) ([67cec4c](https://github.com/Waitroom/waitroom/commit/67cec4c73a26caf64262a139633ee554e59080bf))

## [1.46.0](https://github.com/Waitroom/waitroom/compare/@waitroom/analytics@1.45.0...@waitroom/analytics@1.46.0) (2023-10-04)


### Features

* trixta integration ([d0bd5f2](https://github.com/Waitroom/waitroom/commit/d0bd5f274e3d930719dd00206315d7c55afdd65e))

## [1.45.0](https://github.com/Waitroom/waitroom/compare/@waitroom/analytics@1.44.5...@waitroom/analytics@1.45.0) (2023-09-27)


### Features

* rumi rebrand ([#1672](https://github.com/Waitroom/rumi.ai/issues/1672)) ([f3940a6](https://github.com/Waitroom/waitroom/commit/f3940a6443bbbf1d6df80a6a3f782b720a19a041)), closes [#1677](https://github.com/Waitroom/rumi.ai/issues/1677) [#1678](https://github.com/Waitroom/rumi.ai/issues/1678) [#1688](https://github.com/Waitroom/rumi.ai/issues/1688) [#1690](https://github.com/Waitroom/rumi.ai/issues/1690)

## [1.44.5](https://github.com/Waitroom/waitroom/compare/@waitroom/analytics@1.44.4...@waitroom/analytics@1.44.5) (2023-09-26)

## [1.44.4](https://github.com/Waitroom/waitroom/compare/@waitroom/analytics@1.44.3...@waitroom/analytics@1.44.4) (2023-09-01)

## [1.44.3](https://github.com/Waitroom/waitroom/compare/@waitroom/analytics@1.44.2...@waitroom/analytics@1.44.3) (2023-08-28)


### Bug Fixes

* cleanup the way we track session lifecycle events and added a user joined event ([47252a0](https://github.com/Waitroom/waitroom/commit/47252a0548da38c854ec15ae0952ac4a8fbf7baa))

## [1.44.2](https://github.com/Waitroom/waitroom/compare/@waitroom/analytics@1.44.1...@waitroom/analytics@1.44.2) (2023-08-24)


### Bug Fixes

* add more analytics, change some validations ([5f0ca24](https://github.com/Waitroom/waitroom/commit/5f0ca247b1234f99c1c1051c585f95976edfbf92))

## [1.44.1](https://github.com/Waitroom/waitroom/compare/@waitroom/analytics@1.44.0...@waitroom/analytics@1.44.1) (2023-08-21)

## [1.44.0](https://github.com/Waitroom/waitroom/compare/@waitroom/analytics@1.43.3...@waitroom/analytics@1.44.0) (2023-08-17)


### Features

* add analytics to action items ([491a4dc](https://github.com/Waitroom/waitroom/commit/491a4dc8cdf5fa954aeef02b39f92cdbab51f39c))

## [1.43.3](https://github.com/Waitroom/waitroom/compare/@waitroom/analytics@1.43.2...@waitroom/analytics@1.43.3) (2023-08-16)


### Bug Fixes

* storybook deploy ([5648c62](https://github.com/Waitroom/waitroom/commit/5648c628b9624a5cbc83678b7b80d71fa744f480))

## [1.43.2](https://github.com/Waitroom/waitroom/compare/@waitroom/analytics@1.43.1...@waitroom/analytics@1.43.2) (2023-08-15)


### Bug Fixes

* trixta session channel cleanup ([46c07ba](https://github.com/Waitroom/waitroom/commit/46c07ba540b1e57d073d286c6332a6cba035641e))

## [1.43.1](https://github.com/Waitroom/waitroom/compare/@waitroom/analytics@1.43.0...@waitroom/analytics@1.43.1) (2023-08-14)

## [1.43.0](https://github.com/Waitroom/waitroom/compare/@waitroom/analytics@1.42.0...@waitroom/analytics@1.43.0) (2023-08-14)


### Features

* add multi-language support ([dd1aca6](https://github.com/Waitroom/waitroom/commit/dd1aca68216709f6adac20b34ec8134b15eaac29))

## [1.42.0](https://github.com/Waitroom/waitroom/compare/@waitroom/analytics@1.41.5...@waitroom/analytics@1.42.0) (2023-08-09)


### Features

* show permission fix guide ([#1570](https://github.com/Waitroom/rumi.ai/issues/1570)) ([3297821](https://github.com/Waitroom/waitroom/commit/329782196e7720702ded96f45765e529cb0377da))

## [1.41.5](https://github.com/Waitroom/waitroom/compare/@waitroom/analytics@1.41.4...@waitroom/analytics@1.41.5) (2023-08-08)


### Bug Fixes

* dashboard cache issue ([b7a5962](https://github.com/Waitroom/waitroom/commit/b7a59627ec0726f1db1afe673d63411bbfa4ba95))

## [1.41.4](https://github.com/Waitroom/waitroom/compare/@waitroom/analytics@1.41.3...@waitroom/analytics@1.41.4) (2023-08-08)

## [1.41.3](https://github.com/Waitroom/waitroom/compare/@waitroom/analytics@1.41.2...@waitroom/analytics@1.41.3) (2023-08-02)


### Bug Fixes

* add support for party mode ([09d8b30](https://github.com/Waitroom/waitroom/commit/09d8b30526f6637651a3cb35f2056087306be76e))


### Reverts

* test file ([01ef7d8](https://github.com/Waitroom/waitroom/commit/01ef7d879f32a63e0706101b2dda5bc7ef717df3))

## [1.41.2](https://github.com/Waitroom/waitroom/compare/@waitroom/analytics@1.41.1...@waitroom/analytics@1.41.2) (2023-07-31)

## [1.41.1](https://github.com/Waitroom/waitroom/compare/@waitroom/analytics@1.41.0...@waitroom/analytics@1.41.1) (2023-07-31)

## [1.41.0](https://github.com/Waitroom/waitroom/compare/@waitroom/analytics@1.40.22...@waitroom/analytics@1.41.0) (2023-07-31)


### Features

* updated release setup ([f452e8a](https://github.com/Waitroom/waitroom/commit/f452e8aa98243fbcc24799738c4d4eb1eb2f4fa8))


### Bug Fixes

* build ([d692c0a](https://github.com/Waitroom/waitroom/commit/d692c0a3f93fd15960e847d33bcb6068542f53dd))
* semantic release ([db91e8f](https://github.com/Waitroom/waitroom/commit/db91e8f7e26f3206b80c54b68c0ec4f046d05494))

## @waitroom/analytics [1.40.22](https://github.com/Waitroom/waitroom/compare/@waitroom/analytics@1.40.21...@waitroom/analytics@1.40.22) (2023-07-27)





### Dependencies

* **@waitroom/common-api:** upgraded to 1.15.3
* **@waitroom/http-client:** upgraded to 1.14.20
* **@waitroom/models:** upgraded to 1.100.0
* **@waitroom/tests:** upgraded to 1.21.2
* **@waitroom/utils:** upgraded to 1.38.20

## @waitroom/analytics [1.40.21](https://github.com/Waitroom/waitroom/compare/@waitroom/analytics@1.40.20...@waitroom/analytics@1.40.21) (2023-07-27)

## @waitroom/analytics [1.40.20](https://github.com/Waitroom/waitroom/compare/@waitroom/analytics@1.40.19...@waitroom/analytics@1.40.20) (2023-07-24)





### Dependencies

* **@waitroom/common-api:** upgraded to 1.15.2
* **@waitroom/http-client:** upgraded to 1.14.19
* **@waitroom/models:** upgraded to 1.99.1
* **@waitroom/tests:** upgraded to 1.21.1
* **@waitroom/utils:** upgraded to 1.38.19

## @waitroom/analytics [1.40.19](https://github.com/Waitroom/waitroom/compare/@waitroom/analytics@1.40.18...@waitroom/analytics@1.40.19) (2023-07-21)





### Dependencies

* **@waitroom/common-api:** upgraded to 1.15.1
* **@waitroom/http-client:** upgraded to 1.14.18
* **@waitroom/models:** upgraded to 1.99.0
* **@waitroom/tests:** upgraded to 1.21.0
* **@waitroom/utils:** upgraded to 1.38.18

## @waitroom/analytics [1.40.18](https://github.com/Waitroom/waitroom/compare/@waitroom/analytics@1.40.17...@waitroom/analytics@1.40.18) (2023-07-21)





### Dependencies

* **@waitroom/common-api:** upgraded to 1.15.0

## @waitroom/analytics [1.40.17](https://github.com/Waitroom/waitroom/compare/@waitroom/analytics@1.40.16...@waitroom/analytics@1.40.17) (2023-07-20)





### Dependencies

* **@waitroom/common-api:** upgraded to 1.14.16
* **@waitroom/http-client:** upgraded to 1.14.17
* **@waitroom/models:** upgraded to 1.98.1
* **@waitroom/tests:** upgraded to 1.20.8
* **@waitroom/utils:** upgraded to 1.38.17

## @waitroom/analytics [1.40.16](https://github.com/Waitroom/waitroom/compare/@waitroom/analytics@1.40.15...@waitroom/analytics@1.40.16) (2023-07-17)





### Dependencies

* **@waitroom/common-api:** upgraded to 1.14.15
* **@waitroom/http-client:** upgraded to 1.14.16
* **@waitroom/models:** upgraded to 1.98.0
* **@waitroom/tests:** upgraded to 1.20.7
* **@waitroom/utils:** upgraded to 1.38.16

## @waitroom/analytics [1.40.15](https://github.com/Waitroom/waitroom/compare/@waitroom/analytics@1.40.14...@waitroom/analytics@1.40.15) (2023-07-17)





### Dependencies

* **@waitroom/common-api:** upgraded to 1.14.14
* **@waitroom/http-client:** upgraded to 1.14.15
* **@waitroom/models:** upgraded to 1.97.3
* **@waitroom/tests:** upgraded to 1.20.6
* **@waitroom/utils:** upgraded to 1.38.15

## @waitroom/analytics [1.40.14](https://github.com/Waitroom/waitroom/compare/@waitroom/analytics@1.40.13...@waitroom/analytics@1.40.14) (2023-07-12)





### Dependencies

* **@waitroom/common-api:** upgraded to 1.14.13
* **@waitroom/http-client:** upgraded to 1.14.14
* **@waitroom/models:** upgraded to 1.97.2
* **@waitroom/tests:** upgraded to 1.20.5
* **@waitroom/utils:** upgraded to 1.38.14

## @waitroom/analytics [1.40.13](https://github.com/Waitroom/waitroom/compare/@waitroom/analytics@1.40.12...@waitroom/analytics@1.40.13) (2023-07-11)





### Dependencies

* **@waitroom/common-api:** upgraded to 1.14.12
* **@waitroom/http-client:** upgraded to 1.14.13
* **@waitroom/models:** upgraded to 1.97.1
* **@waitroom/tests:** upgraded to 1.20.4
* **@waitroom/utils:** upgraded to 1.38.13

## @waitroom/analytics [1.40.12](https://github.com/Waitroom/waitroom/compare/@waitroom/analytics@1.40.11...@waitroom/analytics@1.40.12) (2023-07-11)





### Dependencies

* **@waitroom/common-api:** upgraded to 1.14.11
* **@waitroom/http-client:** upgraded to 1.14.12
* **@waitroom/utils:** upgraded to 1.38.12

## @waitroom/analytics [1.40.11](https://github.com/Waitroom/waitroom/compare/@waitroom/analytics@1.40.10...@waitroom/analytics@1.40.11) (2023-07-10)





### Dependencies

* **@waitroom/common-api:** upgraded to 1.14.10
* **@waitroom/http-client:** upgraded to 1.14.11
* **@waitroom/models:** upgraded to 1.97.0
* **@waitroom/tests:** upgraded to 1.20.3
* **@waitroom/utils:** upgraded to 1.38.11

## @waitroom/analytics [1.40.10](https://github.com/Waitroom/waitroom/compare/@waitroom/analytics@1.40.9...@waitroom/analytics@1.40.10) (2023-07-09)





### Dependencies

* **@waitroom/common-api:** upgraded to 1.14.9

## @waitroom/analytics [1.40.9](https://github.com/Waitroom/waitroom/compare/@waitroom/analytics@1.40.8...@waitroom/analytics@1.40.9) (2023-07-08)


### Bug Fixes

* tests ([a05f666](https://github.com/Waitroom/waitroom/commit/a05f66680eb49820e48915a2483aad619bd1de6a))

## @waitroom/analytics [1.40.8](https://github.com/Waitroom/waitroom/compare/@waitroom/analytics@1.40.7...@waitroom/analytics@1.40.8) (2023-07-08)





### Dependencies

* **@waitroom/common-api:** upgraded to 1.14.8
* **@waitroom/http-client:** upgraded to 1.14.10
* **@waitroom/models:** upgraded to 1.96.1
* **@waitroom/tests:** upgraded to 1.20.2
* **@waitroom/utils:** upgraded to 1.38.10

## @waitroom/analytics [1.40.7](https://github.com/Waitroom/waitroom/compare/@waitroom/analytics@1.40.6...@waitroom/analytics@1.40.7) (2023-07-08)





### Dependencies

* **@waitroom/common-api:** upgraded to 1.14.7
* **@waitroom/http-client:** upgraded to 1.14.9
* **@waitroom/models:** upgraded to 1.96.0
* **@waitroom/tests:** upgraded to 1.20.1
* **@waitroom/utils:** upgraded to 1.38.9

## @waitroom/analytics [1.40.6](https://github.com/Waitroom/waitroom/compare/@waitroom/analytics@1.40.5...@waitroom/analytics@1.40.6) (2023-07-07)





### Dependencies

* **@waitroom/common-api:** upgraded to 1.14.6

## @waitroom/analytics [1.40.5](https://github.com/Waitroom/waitroom/compare/@waitroom/analytics@1.40.4...@waitroom/analytics@1.40.5) (2023-07-07)





### Dependencies

* **@waitroom/common-api:** upgraded to 1.14.5
* **@waitroom/http-client:** upgraded to 1.14.8
* **@waitroom/models:** upgraded to 1.95.0
* **@waitroom/tests:** upgraded to 1.20.0
* **@waitroom/utils:** upgraded to 1.38.8

## @waitroom/analytics [1.40.4](https://github.com/Waitroom/waitroom/compare/@waitroom/analytics@1.40.3...@waitroom/analytics@1.40.4) (2023-07-05)





### Dependencies

* **@waitroom/common-api:** upgraded to 1.14.4
* **@waitroom/http-client:** upgraded to 1.14.7
* **@waitroom/models:** upgraded to 1.94.3
* **@waitroom/tests:** upgraded to 1.19.6
* **@waitroom/utils:** upgraded to 1.38.7

## @waitroom/analytics [1.40.3](https://github.com/Waitroom/waitroom/compare/@waitroom/analytics@1.40.2...@waitroom/analytics@1.40.3) (2023-06-22)





### Dependencies

* **@waitroom/common-api:** upgraded to 1.14.3
* **@waitroom/http-client:** upgraded to 1.14.6
* **@waitroom/models:** upgraded to 1.94.2
* **@waitroom/tests:** upgraded to 1.19.5
* **@waitroom/utils:** upgraded to 1.38.6

## @waitroom/analytics [1.40.2](https://github.com/Waitroom/waitroom/compare/@waitroom/analytics@1.40.1...@waitroom/analytics@1.40.2) (2023-06-19)


### Bug Fixes

* sentry errors ([#1498](https://github.com/Waitroom/rumi.ai/issues/1498)) ([5f9f5d6](https://github.com/Waitroom/waitroom/commit/5f9f5d60cce605e732b04bf17db0d808aeaba908))





### Dependencies

* **@waitroom/common-api:** upgraded to 1.14.2
* **@waitroom/http-client:** upgraded to 1.14.5
* **@waitroom/utils:** upgraded to 1.38.5

## @waitroom/analytics [1.40.1](https://github.com/Waitroom/waitroom/compare/@waitroom/analytics@1.40.0...@waitroom/analytics@1.40.1) (2023-06-16)

# @waitroom/analytics [1.40.0](https://github.com/Waitroom/waitroom/compare/@waitroom/analytics@1.39.1...@waitroom/analytics@1.40.0) (2023-06-14)


### Features

* WEB-848 clicking start button on sessions on mobile doesnt open the ([#1493](https://github.com/Waitroom/rumi.ai/issues/1493)) ([ae9e0c3](https://github.com/Waitroom/waitroom/commit/ae9e0c3077746cfbfe974bb2ed169aedfafd67a5))

## @waitroom/analytics [1.39.1](https://github.com/Waitroom/waitroom/compare/@waitroom/analytics@1.39.0...@waitroom/analytics@1.39.1) (2023-06-13)





### Dependencies

* **@waitroom/common-api:** upgraded to 1.14.1
* **@waitroom/error-service:** upgraded to 1.13.2
* **@waitroom/models:** upgraded to 1.94.1
* **@waitroom/tests:** upgraded to 1.19.4
* **@waitroom/utils:** upgraded to 1.38.4

# @waitroom/analytics [1.39.0](https://github.com/Waitroom/waitroom/compare/@waitroom/analytics@1.38.3...@waitroom/analytics@1.39.0) (2023-06-13)


### Features

* use branch link to start session ([#1486](https://github.com/Waitroom/rumi.ai/issues/1486)) ([b28da56](https://github.com/Waitroom/waitroom/commit/b28da568ba723a1d649d32f5c8ac295a350e847c))

## @waitroom/analytics [1.38.3](https://github.com/Waitroom/waitroom/compare/@waitroom/analytics@1.38.2...@waitroom/analytics@1.38.3) (2023-06-08)





### Dependencies

* **@waitroom/common-api:** upgraded to 1.14.0
* **@waitroom/models:** upgraded to 1.94.0
* **@waitroom/tests:** upgraded to 1.19.3
* **@waitroom/utils:** upgraded to 1.38.3

## @waitroom/analytics [1.38.2](https://github.com/Waitroom/waitroom/compare/@waitroom/analytics@1.38.1...@waitroom/analytics@1.38.2) (2023-06-05)





### Dependencies

* **@waitroom/common-api:** upgraded to 1.13.2
* **@waitroom/models:** upgraded to 1.93.0
* **@waitroom/tests:** upgraded to 1.19.2
* **@waitroom/utils:** upgraded to 1.38.2

## @waitroom/analytics [1.38.1](https://github.com/Waitroom/waitroom/compare/@waitroom/analytics@1.38.0...@waitroom/analytics@1.38.1) (2023-05-31)





### Dependencies

* **@waitroom/common-api:** upgraded to 1.13.1
* **@waitroom/models:** upgraded to 1.92.0
* **@waitroom/tests:** upgraded to 1.19.1
* **@waitroom/utils:** upgraded to 1.38.1

# @waitroom/analytics [1.38.0](https://github.com/Waitroom/waitroom/compare/@waitroom/analytics@1.37.0...@waitroom/analytics@1.38.0) (2023-05-30)


### Features

* expand hosting supported browsers ([#1446](https://github.com/Waitroom/rumi.ai/issues/1446)) ([8c930de](https://github.com/Waitroom/waitroom/commit/8c930de5795a7b77d5dd70f8caa5d73041607916))

# @waitroom/analytics [1.37.0](https://github.com/Waitroom/waitroom/compare/@waitroom/analytics@1.36.7...@waitroom/analytics@1.37.0) (2023-05-30)


### Features

* shortcut instructions ([e8424d7](https://github.com/Waitroom/waitroom/commit/e8424d786c97aad53afb609437c204fdf60138bb))





### Dependencies

* **@waitroom/common-api:** upgraded to 1.13.0
* **@waitroom/models:** upgraded to 1.91.0
* **@waitroom/tests:** upgraded to 1.19.0
* **@waitroom/utils:** upgraded to 1.38.0

## @waitroom/analytics [1.36.6](https://github.com/Waitroom/waitroom/compare/@waitroom/analytics@1.36.5...@waitroom/analytics@1.36.6) (2023-05-29)


### Bug Fixes

* package versions ([23fa1c0](https://github.com/Waitroom/waitroom/commit/23fa1c02825b5a8c5ef466cc5dc6a884192ccb5e))





### Dependencies

* **@waitroom/common-api:** upgraded to 1.12.27
* **@waitroom/models:** upgraded to 1.90.0
* **@waitroom/tests:** upgraded to 1.18.16
* **@waitroom/utils:** upgraded to 1.37.0

## @waitroom/analytics [1.36.5](https://github.com/Waitroom/waitroom/compare/@waitroom/analytics@1.36.4...@waitroom/analytics@1.36.5) (2023-05-26)





### Dependencies

* **@waitroom/common-api:** upgraded to 1.12.26
* **@waitroom/models:** upgraded to 1.89.0
* **@waitroom/tests:** upgraded to 1.18.15
* **@waitroom/utils:** upgraded to 1.36.6

## @waitroom/analytics [1.36.4](https://github.com/Waitroom/waitroom/compare/@waitroom/analytics@1.36.3...@waitroom/analytics@1.36.4) (2023-05-25)





### Dependencies

* **@waitroom/common-api:** upgraded to 1.12.25
* **@waitroom/models:** upgraded to 1.88.0
* **@waitroom/tests:** upgraded to 1.18.14
* **@waitroom/utils:** upgraded to 1.36.5

## @waitroom/analytics [1.36.3](https://github.com/Waitroom/waitroom/compare/@waitroom/analytics@1.36.2...@waitroom/analytics@1.36.3) (2023-05-24)


### Reverts

* Revert "feat: WEB-738 block session pages for mobile web (#1399) (#1411)" ([af55108](https://github.com/Waitroom/waitroom/commit/af551084f7973583bebe93beb577daaa68b7d4a8)), closes [#1399](https://github.com/Waitroom/rumi.ai/issues/1399) [#1411](https://github.com/Waitroom/rumi.ai/issues/1411)

## @waitroom/analytics [1.36.2](https://github.com/Waitroom/waitroom/compare/@waitroom/analytics@1.36.1...@waitroom/analytics@1.36.2) (2023-05-23)





### Dependencies

* **@waitroom/common-api:** upgraded to 1.12.24

## @waitroom/analytics [1.36.1](https://github.com/Waitroom/waitroom/compare/@waitroom/analytics@1.36.0...@waitroom/analytics@1.36.1) (2023-05-18)





### Dependencies

* **@waitroom/common-api:** upgraded to 1.12.23
* **@waitroom/models:** upgraded to 1.87.0
* **@waitroom/tests:** upgraded to 1.18.13
* **@waitroom/utils:** upgraded to 1.36.4

# @waitroom/analytics [1.36.0](https://github.com/Waitroom/waitroom/compare/@waitroom/analytics@1.35.20...@waitroom/analytics@1.36.0) (2023-05-10)


### Features

* WEB-738 block session pages for mobile web ([#1399](https://github.com/Waitroom/rumi.ai/issues/1399)) ([#1411](https://github.com/Waitroom/rumi.ai/issues/1411)) ([6e79e0b](https://github.com/Waitroom/waitroom/commit/6e79e0b721caa8d6f1fb4f36e5113ecc02cd973d))

## @waitroom/analytics [1.35.20](https://github.com/Waitroom/waitroom/compare/@waitroom/analytics@1.35.19...@waitroom/analytics@1.35.20) (2023-05-10)





### Dependencies

* **@waitroom/common-api:** upgraded to 1.12.22
* **@waitroom/models:** upgraded to 1.86.1
* **@waitroom/tests:** upgraded to 1.18.12
* **@waitroom/utils:** upgraded to 1.36.3

## @waitroom/analytics [1.35.19](https://github.com/Waitroom/waitroom/compare/@waitroom/analytics@1.35.18...@waitroom/analytics@1.35.19) (2023-05-10)





### Dependencies

* **@waitroom/common-api:** upgraded to 1.12.21
* **@waitroom/models:** upgraded to 1.86.0
* **@waitroom/tests:** upgraded to 1.18.11
* **@waitroom/utils:** upgraded to 1.36.2

## @waitroom/analytics [1.35.18](https://github.com/Waitroom/waitroom/compare/@waitroom/analytics@1.35.17...@waitroom/analytics@1.35.18) (2023-05-09)





### Dependencies

* **@waitroom/common-api:** upgraded to 1.12.20
* **@waitroom/models:** upgraded to 1.85.0
* **@waitroom/tests:** upgraded to 1.18.10
* **@waitroom/utils:** upgraded to 1.36.1

## @waitroom/analytics [1.35.17](https://github.com/Waitroom/waitroom/compare/@waitroom/analytics@1.35.16...@waitroom/analytics@1.35.17) (2023-05-04)





### Dependencies

* **@waitroom/common-api:** upgraded to 1.12.19
* **@waitroom/utils:** upgraded to 1.36.0

## @waitroom/analytics [1.35.16](https://github.com/Waitroom/waitroom/compare/@waitroom/analytics@1.35.15...@waitroom/analytics@1.35.16) (2023-04-25)

## @waitroom/analytics [1.35.15](https://github.com/Waitroom/waitroom/compare/@waitroom/analytics@1.35.14...@waitroom/analytics@1.35.15) (2023-04-25)





### Dependencies

* **@waitroom/common-api:** upgraded to 1.12.18
* **@waitroom/models:** upgraded to 1.84.1
* **@waitroom/tests:** upgraded to 1.18.9
* **@waitroom/utils:** upgraded to 1.35.12

## @waitroom/analytics [1.35.14](https://github.com/Waitroom/waitroom/compare/@waitroom/analytics@1.35.13...@waitroom/analytics@1.35.14) (2023-04-22)





### Dependencies

* **@waitroom/common-api:** upgraded to 1.12.17
* **@waitroom/models:** upgraded to 1.84.0
* **@waitroom/tests:** upgraded to 1.18.8
* **@waitroom/utils:** upgraded to 1.35.11

## @waitroom/analytics [1.35.13](https://github.com/Waitroom/waitroom/compare/@waitroom/analytics@1.35.12...@waitroom/analytics@1.35.13) (2023-04-21)





### Dependencies

* **@waitroom/common-api:** upgraded to 1.12.16
* **@waitroom/models:** upgraded to 1.83.0
* **@waitroom/tests:** upgraded to 1.18.7
* **@waitroom/utils:** upgraded to 1.35.10

## @waitroom/analytics [1.35.12](https://github.com/Waitroom/waitroom/compare/@waitroom/analytics@1.35.11...@waitroom/analytics@1.35.12) (2023-04-20)





### Dependencies

* **@waitroom/common-api:** upgraded to 1.12.15
* **@waitroom/utils:** upgraded to 1.35.9

## @waitroom/analytics [1.35.11](https://github.com/Waitroom/waitroom/compare/@waitroom/analytics@1.35.10...@waitroom/analytics@1.35.11) (2023-04-18)





### Dependencies

* **@waitroom/common-api:** upgraded to 1.12.14
* **@waitroom/models:** upgraded to 1.82.0
* **@waitroom/tests:** upgraded to 1.18.6
* **@waitroom/utils:** upgraded to 1.35.8

## @waitroom/analytics [1.35.10](https://github.com/Waitroom/waitroom/compare/@waitroom/analytics@1.35.9...@waitroom/analytics@1.35.10) (2023-04-14)


### Reverts

* Revert "refactor: trixta sagas (#1385)" ([077783e](https://github.com/Waitroom/waitroom/commit/077783e859fed0e2cf9cdc064979c7335afb0881)), closes [#1385](https://github.com/Waitroom/rumi.ai/issues/1385)
* Revert "chore(release): @waitroom/analytics@v1.35.9 [skip ci]" ([329d370](https://github.com/Waitroom/waitroom/commit/329d370eb0ee6b96bfc86c110d88c2a3474ad557))





### Dependencies

* **@waitroom/common-api:** upgraded to 1.12.13
* **@waitroom/models:** upgraded to 1.81.0
* **@waitroom/tests:** upgraded to 1.18.5
* **@waitroom/utils:** upgraded to 1.35.7

## @waitroom/analytics [1.35.8](https://github.com/Waitroom/waitroom/compare/@waitroom/analytics@1.35.7...@waitroom/analytics@1.35.8) (2023-04-13)





### Dependencies

* **@waitroom/common-api:** upgraded to 1.12.11
* **@waitroom/models:** upgraded to 1.80.0
* **@waitroom/tests:** upgraded to 1.18.4
* **@waitroom/utils:** upgraded to 1.35.6

## @waitroom/analytics [1.35.7](https://github.com/Waitroom/waitroom/compare/@waitroom/analytics@1.35.6...@waitroom/analytics@1.35.7) (2023-04-12)





### Dependencies

* **@waitroom/common-api:** upgraded to 1.12.10

## @waitroom/analytics [1.35.6](https://github.com/Waitroom/waitroom/compare/@waitroom/analytics@1.35.5...@waitroom/analytics@1.35.6) (2023-04-12)





### Dependencies

* **@waitroom/common-api:** upgraded to 1.12.9
* **@waitroom/utils:** upgraded to 1.35.5

## @waitroom/analytics [1.35.5](https://github.com/Waitroom/waitroom/compare/@waitroom/analytics@1.35.4...@waitroom/analytics@1.35.5) (2023-04-10)





### Dependencies

* **@waitroom/common-api:** upgraded to 1.12.8

## @waitroom/analytics [1.35.4](https://github.com/Waitroom/waitroom/compare/@waitroom/analytics@1.35.3...@waitroom/analytics@1.35.4) (2023-04-06)





### Dependencies

* **@waitroom/common-api:** upgraded to 1.12.7

## @waitroom/analytics [1.35.3](https://github.com/Waitroom/waitroom/compare/@waitroom/analytics@1.35.2...@waitroom/analytics@1.35.3) (2023-04-05)





### Dependencies

* **@waitroom/common-api:** upgraded to 1.12.6
* **@waitroom/models:** upgraded to 1.79.3
* **@waitroom/tests:** upgraded to 1.18.3
* **@waitroom/utils:** upgraded to 1.35.4

## @waitroom/analytics [1.35.2](https://github.com/Waitroom/waitroom/compare/@waitroom/analytics@1.35.1...@waitroom/analytics@1.35.2) (2023-04-04)

## @waitroom/analytics [1.35.1](https://github.com/Waitroom/waitroom/compare/@waitroom/analytics@1.35.0...@waitroom/analytics@1.35.1) (2023-03-27)





### Dependencies

* **@waitroom/common-api:** upgraded to 1.12.5
* **@waitroom/models:** upgraded to 1.79.2
* **@waitroom/tests:** upgraded to 1.18.2
* **@waitroom/utils:** upgraded to 1.35.3

# @waitroom/analytics [1.35.0](https://github.com/Waitroom/waitroom/compare/@waitroom/analytics@1.34.14...@waitroom/analytics@1.35.0) (2023-03-24)


### Features

* update analytics for recordings url ([e9f68b3](https://github.com/Waitroom/waitroom/commit/e9f68b374cd02415b1a9d7132d59eb23b2ed5a91))

## @waitroom/analytics [1.34.14](https://github.com/Waitroom/waitroom/compare/@waitroom/analytics@1.34.13...@waitroom/analytics@1.34.14) (2023-03-24)


### Bug Fixes

* update episodes to recordings ([9d28521](https://github.com/Waitroom/waitroom/commit/9d285215f61c72ba7f6fff5a0959f9c9705e0620))

## @waitroom/analytics [1.34.13](https://github.com/Waitroom/waitroom/compare/@waitroom/analytics@1.34.12...@waitroom/analytics@1.34.13) (2023-03-24)





### Dependencies

* **@waitroom/common-api:** upgraded to 1.12.4
* **@waitroom/models:** upgraded to 1.79.1
* **@waitroom/tests:** upgraded to 1.18.1
* **@waitroom/utils:** upgraded to 1.35.2

## @waitroom/analytics [1.34.12](https://github.com/Waitroom/waitroom/compare/@waitroom/analytics@1.34.11...@waitroom/analytics@1.34.12) (2023-03-23)





### Dependencies

* **@waitroom/common-api:** upgraded to 1.12.3

## @waitroom/analytics [1.34.11](https://github.com/Waitroom/waitroom/compare/@waitroom/analytics@1.34.10...@waitroom/analytics@1.34.11) (2023-03-23)





### Dependencies

* **@waitroom/common-api:** upgraded to 1.12.2
* **@waitroom/models:** upgraded to 1.79.0
* **@waitroom/tests:** upgraded to 1.18.0
* **@waitroom/utils:** upgraded to 1.35.1

## @waitroom/analytics [1.34.10](https://github.com/Waitroom/waitroom/compare/@waitroom/analytics@1.34.9...@waitroom/analytics@1.34.10) (2023-03-22)





### Dependencies

* **@waitroom/common-api:** upgraded to 1.12.1

## @waitroom/analytics [1.34.9](https://github.com/Waitroom/waitroom/compare/@waitroom/analytics@1.34.8...@waitroom/analytics@1.34.9) (2023-03-22)





### Dependencies

* **@waitroom/common-api:** upgraded to 1.12.0

## @waitroom/analytics [1.34.8](https://github.com/Waitroom/waitroom/compare/@waitroom/analytics@1.34.7...@waitroom/analytics@1.34.8) (2023-03-21)





### Dependencies

* **@waitroom/common-api:** upgraded to 1.11.4
* **@waitroom/utils:** upgraded to 1.35.0

## @waitroom/analytics [1.34.7](https://github.com/Waitroom/waitroom/compare/@waitroom/analytics@1.34.6...@waitroom/analytics@1.34.7) (2023-03-21)





### Dependencies

* **@waitroom/common-api:** upgraded to 1.11.3
* **@waitroom/models:** upgraded to 1.78.0
* **@waitroom/tests:** upgraded to 1.17.4
* **@waitroom/utils:** upgraded to 1.34.4

## @waitroom/analytics [1.34.6](https://github.com/Waitroom/waitroom/compare/@waitroom/analytics@1.34.5...@waitroom/analytics@1.34.6) (2023-03-21)





### Dependencies

* **@waitroom/common-api:** upgraded to 1.11.2

## @waitroom/analytics [1.34.5](https://github.com/Waitroom/waitroom/compare/@waitroom/analytics@1.34.4...@waitroom/analytics@1.34.5) (2023-03-21)





### Dependencies

* **@waitroom/common-api:** upgraded to 1.11.1
* **@waitroom/models:** upgraded to 1.77.0
* **@waitroom/tests:** upgraded to 1.17.3
* **@waitroom/utils:** upgraded to 1.34.3

## @waitroom/analytics [1.34.4](https://github.com/Waitroom/waitroom/compare/@waitroom/analytics@1.34.3...@waitroom/analytics@1.34.4) (2023-03-20)





### Dependencies

* **@waitroom/common-api:** upgraded to 1.11.0

## @waitroom/analytics [1.34.3](https://github.com/Waitroom/waitroom/compare/@waitroom/analytics@1.34.2...@waitroom/analytics@1.34.3) (2023-03-20)





### Dependencies

* **@waitroom/common-api:** upgraded to 1.10.3

## @waitroom/analytics [1.34.2](https://github.com/Waitroom/waitroom/compare/@waitroom/analytics@1.34.1...@waitroom/analytics@1.34.2) (2023-03-20)





### Dependencies

* **@waitroom/common-api:** upgraded to 1.10.2
* **@waitroom/models:** upgraded to 1.76.1
* **@waitroom/tests:** upgraded to 1.17.2
* **@waitroom/utils:** upgraded to 1.34.2

## @waitroom/analytics [1.34.1](https://github.com/Waitroom/waitroom/compare/@waitroom/analytics@1.34.0...@waitroom/analytics@1.34.1) (2023-03-17)





### Dependencies

* **@waitroom/common-api:** upgraded to 1.10.1
* **@waitroom/models:** upgraded to 1.76.0
* **@waitroom/tests:** upgraded to 1.17.1
* **@waitroom/utils:** upgraded to 1.34.1

# @waitroom/analytics [1.34.0](https://github.com/Waitroom/waitroom/compare/@waitroom/analytics@1.33.4...@waitroom/analytics@1.34.0) (2023-03-17)


### Features

* instant meetings ([#1279](https://github.com/Waitroom/rumi.ai/issues/1279)) ([e5f08eb](https://github.com/Waitroom/waitroom/commit/e5f08eb9a1bd6db6dddd4ec7a6a40f8ccf1dcbf1)), closes [#1284](https://github.com/Waitroom/rumi.ai/issues/1284) [#1289](https://github.com/Waitroom/rumi.ai/issues/1289) [#1287](https://github.com/Waitroom/rumi.ai/issues/1287) [#1295](https://github.com/Waitroom/rumi.ai/issues/1295) [#1296](https://github.com/Waitroom/rumi.ai/issues/1296) [#1298](https://github.com/Waitroom/rumi.ai/issues/1298) [#1300](https://github.com/Waitroom/rumi.ai/issues/1300) [#1303](https://github.com/Waitroom/rumi.ai/issues/1303) [#1302](https://github.com/Waitroom/rumi.ai/issues/1302) [#1304](https://github.com/Waitroom/rumi.ai/issues/1304) [#1305](https://github.com/Waitroom/rumi.ai/issues/1305) [#1306](https://github.com/Waitroom/rumi.ai/issues/1306) [#1310](https://github.com/Waitroom/rumi.ai/issues/1310) [#1309](https://github.com/Waitroom/rumi.ai/issues/1309) [#1315](https://github.com/Waitroom/rumi.ai/issues/1315) [#1317](https://github.com/Waitroom/rumi.ai/issues/1317) [#1320](https://github.com/Waitroom/rumi.ai/issues/1320) [#1319](https://github.com/Waitroom/rumi.ai/issues/1319) [#1321](https://github.com/Waitroom/rumi.ai/issues/1321) [#1318](https://github.com/Waitroom/rumi.ai/issues/1318)





### Dependencies

* **@waitroom/common-api:** upgraded to 1.10.0
* **@waitroom/models:** upgraded to 1.75.0
* **@waitroom/tests:** upgraded to 1.17.0
* **@waitroom/utils:** upgraded to 1.34.0

## @waitroom/analytics [1.33.4](https://github.com/Waitroom/waitroom/compare/@waitroom/analytics@1.33.3...@waitroom/analytics@1.33.4) (2023-03-17)





### Dependencies

* **@waitroom/common-api:** upgraded to 1.9.0

## @waitroom/analytics [1.33.3](https://github.com/Waitroom/waitroom/compare/@waitroom/analytics@1.33.2...@waitroom/analytics@1.33.3) (2023-03-15)





### Dependencies

* **@waitroom/common-api:** upgraded to 1.8.2
* **@waitroom/models:** upgraded to 1.74.0
* **@waitroom/tests:** upgraded to 1.16.3
* **@waitroom/utils:** upgraded to 1.33.3

## @waitroom/analytics [1.33.2](https://github.com/Waitroom/waitroom/compare/@waitroom/analytics@1.33.1...@waitroom/analytics@1.33.2) (2023-03-15)





### Dependencies

* **@waitroom/common-api:** upgraded to 1.8.1
* **@waitroom/models:** upgraded to 1.73.0
* **@waitroom/tests:** upgraded to 1.16.2
* **@waitroom/utils:** upgraded to 1.33.2

## @waitroom/analytics [1.33.1](https://github.com/Waitroom/waitroom/compare/@waitroom/analytics@1.33.0...@waitroom/analytics@1.33.1) (2023-02-23)





### Dependencies

* **@waitroom/common-api:** upgraded to 1.8.0
* **@waitroom/models:** upgraded to 1.72.0
* **@waitroom/tests:** upgraded to 1.16.1
* **@waitroom/utils:** upgraded to 1.33.1

# @waitroom/analytics [1.33.0](https://github.com/Waitroom/waitroom/compare/@waitroom/analytics@1.32.23...@waitroom/analytics@1.33.0) (2023-02-23)


### Features

* one on one conversations ([#1232](https://github.com/Waitroom/rumi.ai/issues/1232)) ([feb0d53](https://github.com/Waitroom/waitroom/commit/feb0d535d4520bb355c8e5dcfaf4a53c86f25aa4)), closes [#1125](https://github.com/Waitroom/rumi.ai/issues/1125) [#1128](https://github.com/Waitroom/rumi.ai/issues/1128) [#1182](https://github.com/Waitroom/rumi.ai/issues/1182) [#1183](https://github.com/Waitroom/rumi.ai/issues/1183) [#1184](https://github.com/Waitroom/rumi.ai/issues/1184) [#1185](https://github.com/Waitroom/rumi.ai/issues/1185) [#1187](https://github.com/Waitroom/rumi.ai/issues/1187) [#1205](https://github.com/Waitroom/rumi.ai/issues/1205) [#1208](https://github.com/Waitroom/rumi.ai/issues/1208) [#1209](https://github.com/Waitroom/rumi.ai/issues/1209) [#1215](https://github.com/Waitroom/rumi.ai/issues/1215) [#1227](https://github.com/Waitroom/rumi.ai/issues/1227) [#1229](https://github.com/Waitroom/rumi.ai/issues/1229) [#1228](https://github.com/Waitroom/rumi.ai/issues/1228)





### Dependencies

* **@waitroom/common-api:** upgraded to 1.7.0
* **@waitroom/models:** upgraded to 1.71.0
* **@waitroom/tests:** upgraded to 1.16.0
* **@waitroom/utils:** upgraded to 1.33.0

## @waitroom/analytics [1.32.23](https://github.com/Waitroom/waitroom/compare/@waitroom/analytics@1.32.22...@waitroom/analytics@1.32.23) (2023-02-23)





### Dependencies

* **@waitroom/common-api:** upgraded to 1.6.2
* **@waitroom/models:** upgraded to 1.70.0
* **@waitroom/tests:** upgraded to 1.15.17
* **@waitroom/utils:** upgraded to 1.32.9

## @waitroom/analytics [1.32.22](https://github.com/Waitroom/waitroom/compare/@waitroom/analytics@1.32.21...@waitroom/analytics@1.32.22) (2023-02-22)





### Dependencies

* **@waitroom/common-api:** upgraded to 1.6.1

## @waitroom/analytics [1.32.21](https://github.com/Waitroom/waitroom/compare/@waitroom/analytics@1.32.20...@waitroom/analytics@1.32.21) (2023-02-22)





### Dependencies

* **@waitroom/common-api:** upgraded to 1.6.0

## @waitroom/analytics [1.32.20](https://github.com/Waitroom/waitroom/compare/@waitroom/analytics@1.32.19...@waitroom/analytics@1.32.20) (2023-02-21)





### Dependencies

* **@waitroom/common-api:** upgraded to 1.5.0

## @waitroom/analytics [1.32.19](https://github.com/Waitroom/waitroom/compare/@waitroom/analytics@1.32.18...@waitroom/analytics@1.32.19) (2023-02-21)





### Dependencies

* **@waitroom/common-api:** upgraded to 1.4.1
* **@waitroom/utils:** upgraded to 1.32.8

## @waitroom/analytics [1.32.18](https://github.com/Waitroom/waitroom/compare/@waitroom/analytics@1.32.17...@waitroom/analytics@1.32.18) (2023-02-20)





### Dependencies

* **@waitroom/common-api:** upgraded to 1.4.0

## @waitroom/analytics [1.32.17](https://github.com/Waitroom/waitroom/compare/@waitroom/analytics@1.32.16...@waitroom/analytics@1.32.17) (2023-02-20)





### Dependencies

* **@waitroom/common-api:** upgraded to 1.3.2
* **@waitroom/models:** upgraded to 1.69.1
* **@waitroom/tests:** upgraded to 1.15.16
* **@waitroom/utils:** upgraded to 1.32.7

## @waitroom/analytics [1.32.16](https://github.com/Waitroom/waitroom/compare/@waitroom/analytics@1.32.15...@waitroom/analytics@1.32.16) (2023-02-17)





### Dependencies

* **@waitroom/common-api:** upgraded to 1.3.1
* **@waitroom/models:** upgraded to 1.69.0
* **@waitroom/tests:** upgraded to 1.15.15
* **@waitroom/utils:** upgraded to 1.32.6

## @waitroom/analytics [1.32.15](https://github.com/Waitroom/waitroom/compare/@waitroom/analytics@1.32.14...@waitroom/analytics@1.32.15) (2023-02-16)





### Dependencies

* **@waitroom/common-api:** upgraded to 1.3.0

## @waitroom/analytics [1.32.14](https://github.com/Waitroom/waitroom/compare/@waitroom/analytics@1.32.13...@waitroom/analytics@1.32.14) (2023-02-15)





### Dependencies

* **@waitroom/common-api:** upgraded to 1.2.7
* **@waitroom/models:** upgraded to 1.68.4
* **@waitroom/tests:** upgraded to 1.15.14
* **@waitroom/utils:** upgraded to 1.32.5

## @waitroom/analytics [1.32.13](https://github.com/Waitroom/waitroom/compare/@waitroom/analytics@1.32.12...@waitroom/analytics@1.32.13) (2023-02-11)





### Dependencies

* **@waitroom/common-api:** upgraded to 1.2.6
* **@waitroom/models:** upgraded to 1.68.3
* **@waitroom/tests:** upgraded to 1.15.13
* **@waitroom/utils:** upgraded to 1.32.4

## @waitroom/analytics [1.32.12](https://github.com/Waitroom/waitroom/compare/@waitroom/analytics@1.32.11...@waitroom/analytics@1.32.12) (2023-02-10)





### Dependencies

* **@waitroom/common-api:** upgraded to 1.2.5
* **@waitroom/models:** upgraded to 1.68.2
* **@waitroom/tests:** upgraded to 1.15.12
* **@waitroom/utils:** upgraded to 1.32.3

## @waitroom/analytics [1.32.11](https://github.com/Waitroom/waitroom/compare/@waitroom/analytics@1.32.10...@waitroom/analytics@1.32.11) (2023-02-09)





### Dependencies

* **@waitroom/common-api:** upgraded to 1.2.4
* **@waitroom/models:** upgraded to 1.68.1
* **@waitroom/tests:** upgraded to 1.15.11
* **@waitroom/utils:** upgraded to 1.32.2

## @waitroom/analytics [1.32.10](https://github.com/Waitroom/waitroom/compare/@waitroom/analytics@1.32.9...@waitroom/analytics@1.32.10) (2023-02-09)





### Dependencies

* **@waitroom/common-api:** upgraded to 1.2.3
* **@waitroom/models:** upgraded to 1.68.0
* **@waitroom/tests:** upgraded to 1.15.10
* **@waitroom/utils:** upgraded to 1.32.1

## @waitroom/analytics [1.32.9](https://github.com/Waitroom/waitroom/compare/@waitroom/analytics@1.32.8...@waitroom/analytics@1.32.9) (2023-02-08)





### Dependencies

* **@waitroom/common-api:** upgraded to 1.2.2
* **@waitroom/models:** upgraded to 1.67.0
* **@waitroom/tests:** upgraded to 1.15.9
* **@waitroom/utils:** upgraded to 1.32.0

## @waitroom/analytics [1.32.8](https://github.com/Waitroom/waitroom/compare/@waitroom/analytics@1.32.7...@waitroom/analytics@1.32.8) (2023-02-08)





### Dependencies

* **@waitroom/common-api:** upgraded to 1.2.1
* **@waitroom/models:** upgraded to 1.66.0
* **@waitroom/tests:** upgraded to 1.15.8
* **@waitroom/utils:** upgraded to 1.31.8

## @waitroom/analytics [1.32.7](https://github.com/Waitroom/waitroom/compare/@waitroom/analytics@1.32.6...@waitroom/analytics@1.32.7) (2023-02-08)





### Dependencies

* **@waitroom/common-api:** upgraded to 1.2.0

## @waitroom/analytics [1.32.6](https://github.com/Waitroom/waitroom/compare/@waitroom/analytics@1.32.5...@waitroom/analytics@1.32.6) (2023-02-06)





### Dependencies

* **@waitroom/common-api:** upgraded to 1.1.8
* **@waitroom/models:** upgraded to 1.65.0
* **@waitroom/tests:** upgraded to 1.15.7
* **@waitroom/utils:** upgraded to 1.31.7

## @waitroom/analytics [1.32.5](https://github.com/Waitroom/waitroom/compare/@waitroom/analytics@1.32.4...@waitroom/analytics@1.32.5) (2023-01-25)





### Dependencies

* **@waitroom/common-api:** upgraded to 1.1.7
* **@waitroom/models:** upgraded to 1.64.1
* **@waitroom/tests:** upgraded to 1.15.6
* **@waitroom/utils:** upgraded to 1.31.6

## @waitroom/analytics [1.32.4](https://github.com/Waitroom/waitroom/compare/@waitroom/analytics@1.32.3...@waitroom/analytics@1.32.4) (2023-01-25)





### Dependencies

* **@waitroom/common-api:** upgraded to 1.1.6
* **@waitroom/models:** upgraded to 1.64.0
* **@waitroom/tests:** upgraded to 1.15.5
* **@waitroom/utils:** upgraded to 1.31.5

## @waitroom/analytics [1.32.3](https://github.com/Waitroom/waitroom/compare/@waitroom/analytics@1.32.2...@waitroom/analytics@1.32.3) (2023-01-24)


### Bug Fixes

* packages, WEB-349 ([1edad47](https://github.com/Waitroom/waitroom/commit/1edad471139b8afbb12721d480e0d5a2acf343e1))





### Dependencies

* **@waitroom/common-api:** upgraded to 1.1.5
* **@waitroom/models:** upgraded to 1.63.3
* **@waitroom/tests:** upgraded to 1.15.4
* **@waitroom/utils:** upgraded to 1.31.4

## @waitroom/analytics [1.32.2](https://github.com/Waitroom/waitroom/compare/@waitroom/analytics@1.32.1...@waitroom/analytics@1.32.2) (2023-01-24)





### Dependencies

* **@waitroom/common-api:** upgraded to 1.1.4
* **@waitroom/models:** upgraded to 1.63.2
* **@waitroom/tests:** upgraded to 1.15.3
* **@waitroom/utils:** upgraded to 1.31.3

## @waitroom/analytics [1.32.1](https://github.com/Waitroom/waitroom/compare/@waitroom/analytics@1.32.0...@waitroom/analytics@1.32.1) (2023-01-20)





### Dependencies

* **@waitroom/common-api:** upgraded to 1.1.3
* **@waitroom/models:** upgraded to 1.63.1
* **@waitroom/tests:** upgraded to 1.15.2
* **@waitroom/utils:** upgraded to 1.31.2

# @waitroom/analytics [1.32.0](https://github.com/Waitroom/waitroom/compare/@waitroom/analytics@1.31.1...@waitroom/analytics@1.32.0) (2023-01-19)


### Features

* update and replace deprecated google package ([d3b2d5d](https://github.com/Waitroom/waitroom/commit/d3b2d5d193a6ca04f733cbfe2275acd59f3480f6))





### Dependencies

* **@waitroom/common-api:** upgraded to 1.1.2

## @waitroom/analytics [1.31.1](https://github.com/Waitroom/waitroom/compare/@waitroom/analytics@1.31.0...@waitroom/analytics@1.31.1) (2023-01-15)





### Dependencies

* **@waitroom/common-api:** upgraded to 1.1.1
* **@waitroom/models:** upgraded to 1.63.0
* **@waitroom/tests:** upgraded to 1.15.1
* **@waitroom/utils:** upgraded to 1.31.1

# @waitroom/analytics [1.31.0](https://github.com/Waitroom/waitroom/compare/@waitroom/analytics@1.30.3...@waitroom/analytics@1.31.0) (2023-01-13)


### Features

* CRE-1235 update auth service ([#1114](https://github.com/Waitroom/rumi.ai/issues/1114)) ([392073b](https://github.com/Waitroom/waitroom/commit/392073b50c4458441ec1b98112ef0de8778d6554)), closes [#1125](https://github.com/Waitroom/rumi.ai/issues/1125) [#1128](https://github.com/Waitroom/rumi.ai/issues/1128) [#1182](https://github.com/Waitroom/rumi.ai/issues/1182) [#1183](https://github.com/Waitroom/rumi.ai/issues/1183) [#1184](https://github.com/Waitroom/rumi.ai/issues/1184) [#1185](https://github.com/Waitroom/rumi.ai/issues/1185) [#1187](https://github.com/Waitroom/rumi.ai/issues/1187) [#1189](https://github.com/Waitroom/rumi.ai/issues/1189)





### Dependencies

* **@waitroom/common-api:** upgraded to 1.1.0
* **@waitroom/models:** upgraded to 1.62.0
* **@waitroom/tests:** upgraded to 1.15.0
* **@waitroom/utils:** upgraded to 1.31.0

## @waitroom/analytics [1.30.3](https://github.com/Waitroom/waitroom/compare/@waitroom/analytics@1.30.2...@waitroom/analytics@1.30.3) (2022-12-14)





### Dependencies

* **@waitroom/models:** upgraded to 1.61.1
* **@waitroom/tests:** upgraded to 1.14.1
* **@waitroom/utils:** upgraded to 1.30.1

## @waitroom/analytics [1.30.2](https://github.com/Waitroom/waitroom/compare/@waitroom/analytics@1.30.1...@waitroom/analytics@1.30.2) (2022-12-14)





### Dependencies

* **@waitroom/models:** upgraded to 1.61.0
* **@waitroom/tests:** upgraded to 1.14.0
* **@waitroom/utils:** upgraded to 1.30.0

## @waitroom/analytics [1.30.1](https://github.com/Waitroom/waitroom/compare/@waitroom/analytics@1.30.0...@waitroom/analytics@1.30.1) (2022-12-08)


### Bug Fixes

* viewer list memo, refactor useCallbacks hook ([b300d9a](https://github.com/Waitroom/waitroom/commit/b300d9ab4c6b01309060d66f4fb5ddc4fdc08276))





### Dependencies

* **@waitroom/models:** upgraded to 1.60.2
* **@waitroom/tests:** upgraded to 1.13.13
* **@waitroom/utils:** upgraded to 1.29.22

# @waitroom/analytics [1.30.0](https://github.com/Waitroom/waitroom/compare/@waitroom/analytics@1.29.10...@waitroom/analytics@1.30.0) (2022-12-05)


### Features

* pass additionalData when joining a role ([3d3ed38](https://github.com/Waitroom/waitroom/commit/3d3ed387652dd4e97336bd3ff8c6ce5e60dc3993))

## @waitroom/analytics [1.29.10](https://github.com/Waitroom/waitroom/compare/@waitroom/analytics@1.29.9...@waitroom/analytics@1.29.10) (2022-12-05)


### Bug Fixes

* possible fix for socket race condition ([92fe879](https://github.com/Waitroom/waitroom/commit/92fe8792747f586796bb34c64b91ea80c47e7b60))

## @waitroom/analytics [1.29.9](https://github.com/Waitroom/waitroom/compare/@waitroom/analytics@1.29.8...@waitroom/analytics@1.29.9) (2022-11-30)





### Dependencies

* **@waitroom/models:** upgraded to 1.60.1
* **@waitroom/tests:** upgraded to 1.13.12
* **@waitroom/utils:** upgraded to 1.29.21

## @waitroom/analytics [1.29.8](https://github.com/Waitroom/waitroom/compare/@waitroom/analytics@1.29.7...@waitroom/analytics@1.29.8) (2022-11-30)





### Dependencies

* **@waitroom/models:** upgraded to 1.60.0
* **@waitroom/tests:** upgraded to 1.13.11
* **@waitroom/utils:** upgraded to 1.29.20

## @waitroom/analytics [1.29.7](https://github.com/Waitroom/waitroom/compare/@waitroom/analytics@1.29.6...@waitroom/analytics@1.29.7) (2022-11-22)





### Dependencies

* **@waitroom/models:** upgraded to 1.59.1
* **@waitroom/tests:** upgraded to 1.13.10
* **@waitroom/utils:** upgraded to 1.29.19

## @waitroom/analytics [1.29.6](https://github.com/Waitroom/waitroom/compare/@waitroom/analytics@1.29.5...@waitroom/analytics@1.29.6) (2022-11-11)


### Bug Fixes

* conflicting trixta types ([b4f29a2](https://github.com/Waitroom/waitroom/commit/b4f29a27c14e15f85916c0b8c004ea03f9d84c0e))

## @waitroom/analytics [1.29.5](https://github.com/Waitroom/waitroom/compare/@waitroom/analytics@1.29.4...@waitroom/analytics@1.29.5) (2022-11-08)





### Dependencies

* **@waitroom/tests:** upgraded to 1.13.9
* **@waitroom/models:** upgraded to 1.59.0
* **@waitroom/utils:** upgraded to 1.29.18

## @waitroom/analytics [1.29.4](https://github.com/Waitroom/waitroom/compare/@waitroom/analytics@1.29.3...@waitroom/analytics@1.29.4) (2022-11-02)





### Dependencies

* **@waitroom/utils:** upgraded to 1.29.17

## @waitroom/analytics [1.29.3](https://github.com/Waitroom/waitroom/compare/@waitroom/analytics@1.29.2...@waitroom/analytics@1.29.3) (2022-10-28)





### Dependencies

* **@waitroom/tests:** upgraded to 1.13.8
* **@waitroom/models:** upgraded to 1.58.1
* **@waitroom/utils:** upgraded to 1.29.16

## @waitroom/analytics [1.29.2](https://github.com/Waitroom/waitroom/compare/@waitroom/analytics@1.29.1...@waitroom/analytics@1.29.2) (2022-10-13)





### Dependencies

* **@waitroom/tests:** upgraded to 1.13.7
* **@waitroom/models:** upgraded to 1.58.0
* **@waitroom/utils:** upgraded to 1.29.15

## @waitroom/analytics [1.29.1](https://github.com/Waitroom/waitroom/compare/@waitroom/analytics@1.29.0...@waitroom/analytics@1.29.1) (2022-10-06)





### Dependencies

* **@waitroom/tests:** upgraded to 1.13.6
* **@waitroom/models:** upgraded to 1.57.1
* **@waitroom/utils:** upgraded to 1.29.14

# @waitroom/analytics [1.29.0](https://github.com/Waitroom/waitroom/compare/@waitroom/analytics@1.28.28...@waitroom/analytics@1.29.0) (2022-10-03)


### Features

* CRE=875 screen sharing ([#1020](https://github.com/Waitroom/rumi.ai/issues/1020)) ([5f2ed1d](https://github.com/Waitroom/waitroom/commit/5f2ed1d1df06b7bbb812e503389ac522932d9932)), closes [#1016](https://github.com/Waitroom/rumi.ai/issues/1016) [#1010](https://github.com/Waitroom/rumi.ai/issues/1010) [#1019](https://github.com/Waitroom/rumi.ai/issues/1019) [#1028](https://github.com/Waitroom/rumi.ai/issues/1028) [#1027](https://github.com/Waitroom/rumi.ai/issues/1027) [#1029](https://github.com/Waitroom/rumi.ai/issues/1029) [#1030](https://github.com/Waitroom/rumi.ai/issues/1030) [#1032](https://github.com/Waitroom/rumi.ai/issues/1032) [#1035](https://github.com/Waitroom/rumi.ai/issues/1035) [#1036](https://github.com/Waitroom/rumi.ai/issues/1036) [#1039](https://github.com/Waitroom/rumi.ai/issues/1039) [#1041](https://github.com/Waitroom/rumi.ai/issues/1041) [#1042](https://github.com/Waitroom/rumi.ai/issues/1042) [#1044](https://github.com/Waitroom/rumi.ai/issues/1044) [#1049](https://github.com/Waitroom/rumi.ai/issues/1049) [#1051](https://github.com/Waitroom/rumi.ai/issues/1051) [#1055](https://github.com/Waitroom/rumi.ai/issues/1055) [#1057](https://github.com/Waitroom/rumi.ai/issues/1057)





### Dependencies

* **@waitroom/tests:** upgraded to 1.13.5
* **@waitroom/models:** upgraded to 1.57.0
* **@waitroom/utils:** upgraded to 1.29.13

## @waitroom/analytics [1.28.28](https://github.com/Waitroom/waitroom/compare/@waitroom/analytics@1.28.27...@waitroom/analytics@1.28.28) (2022-09-29)

## @waitroom/analytics [1.28.27](https://github.com/Waitroom/waitroom/compare/@waitroom/analytics@1.28.26...@waitroom/analytics@1.28.27) (2022-09-29)


### Bug Fixes

* tests ([84a1b93](https://github.com/Waitroom/waitroom/commit/84a1b93675e2243dc7eb6fe6cff0955dce246698))





### Dependencies

* **@waitroom/tests:** upgraded to 1.13.4
* **@waitroom/models:** upgraded to 1.56.1
* **@waitroom/utils:** upgraded to 1.29.12

## @waitroom/analytics [1.28.26](https://github.com/Waitroom/waitroom/compare/@waitroom/analytics@1.28.25...@waitroom/analytics@1.28.26) (2022-09-27)





### Dependencies

* **@waitroom/tests:** upgraded to 1.13.3
* **@waitroom/models:** upgraded to 1.56.0
* **@waitroom/utils:** upgraded to 1.29.11

## @waitroom/analytics [1.28.25](https://github.com/Waitroom/waitroom/compare/@waitroom/analytics@1.28.24...@waitroom/analytics@1.28.25) (2022-09-21)





### Dependencies

* **@waitroom/tests:** upgraded to 1.13.2
* **@waitroom/models:** upgraded to 1.55.0
* **@waitroom/utils:** upgraded to 1.29.10

## @waitroom/analytics [1.28.24](https://github.com/Waitroom/waitroom/compare/@waitroom/analytics@1.28.23...@waitroom/analytics@1.28.24) (2022-09-16)





### Dependencies

* **@waitroom/tests:** upgraded to 1.13.1
* **@waitroom/models:** upgraded to 1.54.0
* **@waitroom/utils:** upgraded to 1.29.9

## @waitroom/analytics [1.28.23](https://github.com/Waitroom/waitroom/compare/@waitroom/analytics@1.28.22...@waitroom/analytics@1.28.23) (2022-09-13)





### Dependencies

* **@waitroom/utils:** upgraded to 1.29.8

## @waitroom/analytics [1.28.22](https://github.com/Waitroom/waitroom/compare/@waitroom/analytics@1.28.21...@waitroom/analytics@1.28.22) (2022-09-08)





### Dependencies

* **@waitroom/tests:** upgraded to 1.13.0
* **@waitroom/models:** upgraded to 1.53.0
* **@waitroom/utils:** upgraded to 1.29.7

## @waitroom/analytics [1.28.21](https://github.com/Waitroom/waitroom/compare/@waitroom/analytics@1.28.20...@waitroom/analytics@1.28.21) (2022-09-06)





### Dependencies

* **@waitroom/tests:** upgraded to 1.12.4
* **@waitroom/models:** upgraded to 1.52.0
* **@waitroom/utils:** upgraded to 1.29.6

## @waitroom/analytics [1.28.20](https://github.com/Waitroom/waitroom/compare/@waitroom/analytics@1.28.19...@waitroom/analytics@1.28.20) (2022-09-06)





### Dependencies

* **@waitroom/tests:** upgraded to 1.12.3
* **@waitroom/models:** upgraded to 1.51.0
* **@waitroom/utils:** upgraded to 1.29.5

## @waitroom/analytics [1.28.19](https://github.com/Waitroom/waitroom/compare/@waitroom/analytics@1.28.18...@waitroom/analytics@1.28.19) (2022-09-05)





### Dependencies

* **@waitroom/tests:** upgraded to 1.12.2
* **@waitroom/models:** upgraded to 1.50.0
* **@waitroom/utils:** upgraded to 1.29.4

## @waitroom/analytics [1.28.18](https://github.com/Waitroom/waitroom/compare/@waitroom/analytics@1.28.17...@waitroom/analytics@1.28.18) (2022-09-05)





### Dependencies

* **@waitroom/tests:** upgraded to 1.12.1
* **@waitroom/models:** upgraded to 1.49.0
* **@waitroom/utils:** upgraded to 1.29.3

## @waitroom/analytics [1.28.17](https://github.com/Waitroom/waitroom/compare/@waitroom/analytics@1.28.16...@waitroom/analytics@1.28.17) (2022-08-29)





### Dependencies

* **@waitroom/tests:** upgraded to 1.12.0

## @waitroom/analytics [1.28.16](https://github.com/Waitroom/waitroom/compare/@waitroom/analytics@1.28.15...@waitroom/analytics@1.28.16) (2022-08-26)





### Dependencies

* **@waitroom/tests:** upgraded to 1.11.11
* **@waitroom/models:** upgraded to 1.48.0
* **@waitroom/utils:** upgraded to 1.29.2

## @waitroom/analytics [1.28.15](https://github.com/Waitroom/waitroom/compare/@waitroom/analytics@1.28.14...@waitroom/analytics@1.28.15) (2022-08-23)





### Dependencies

* **@waitroom/tests:** upgraded to 1.11.10
* **@waitroom/models:** upgraded to 1.47.1
* **@waitroom/utils:** upgraded to 1.29.1

## @waitroom/analytics [1.28.14](https://github.com/Waitroom/waitroom/compare/@waitroom/analytics@1.28.13...@waitroom/analytics@1.28.14) (2022-08-18)


### Bug Fixes

* resize observer fix ([#978](https://github.com/Waitroom/rumi.ai/issues/978)) ([2bbd562](https://github.com/Waitroom/waitroom/commit/2bbd5624210e5ed129370da592efe2a14c6fc170))





### Dependencies

* **@waitroom/tests:** upgraded to 1.11.9
* **@waitroom/models:** upgraded to 1.47.0
* **@waitroom/utils:** upgraded to 1.29.0

## @waitroom/analytics [1.28.13](https://github.com/Waitroom/waitroom/compare/@waitroom/analytics@1.28.12...@waitroom/analytics@1.28.13) (2022-08-16)





### Dependencies

* **@waitroom/tests:** upgraded to 1.11.8
* **@waitroom/models:** upgraded to 1.46.0
* **@waitroom/utils:** upgraded to 1.28.8

## @waitroom/analytics [1.28.12](https://github.com/Waitroom/waitroom/compare/@waitroom/analytics@1.28.11...@waitroom/analytics@1.28.12) (2022-08-16)





### Dependencies

* **@waitroom/tests:** upgraded to 1.11.7
* **@waitroom/models:** upgraded to 1.45.1
* **@waitroom/utils:** upgraded to 1.28.7

## @waitroom/analytics [1.28.11](https://github.com/Waitroom/waitroom/compare/@waitroom/analytics@1.28.10...@waitroom/analytics@1.28.11) (2022-08-10)


### Bug Fixes

* private session branch links ([#968](https://github.com/Waitroom/rumi.ai/issues/968)) ([9b86642](https://github.com/Waitroom/waitroom/commit/9b86642d2f31742cee058bcb7fcc792a89c39a34))

## @waitroom/analytics [1.28.10](https://github.com/Waitroom/waitroom/compare/@waitroom/analytics@1.28.9...@waitroom/analytics@1.28.10) (2022-08-08)


### Bug Fixes

* avoid jumping to first clip in a play list when clicking random clip ([#958](https://github.com/Waitroom/rumi.ai/issues/958)) ([51a9427](https://github.com/Waitroom/waitroom/commit/51a94271f8b746b9618bcd061aa4132ea13cab3f))

## @waitroom/analytics [1.28.9](https://github.com/Waitroom/waitroom/compare/@waitroom/analytics@1.28.8...@waitroom/analytics@1.28.9) (2022-08-03)


### Bug Fixes

* recurrenceID for the player analytics ([48383c1](https://github.com/Waitroom/waitroom/commit/48383c109419243e0150c39b4727aa4e002fbfdf))

## @waitroom/analytics [1.28.8](https://github.com/Waitroom/waitroom/compare/@waitroom/analytics@1.28.7...@waitroom/analytics@1.28.8) (2022-08-03)





### Dependencies

* **@waitroom/tests:** upgraded to 1.11.6
* **@waitroom/models:** upgraded to 1.45.0
* **@waitroom/utils:** upgraded to 1.28.6

## @waitroom/analytics [1.28.7](https://github.com/Waitroom/waitroom/compare/@waitroom/analytics@1.28.6...@waitroom/analytics@1.28.7) (2022-08-01)





### Dependencies

* **@waitroom/tests:** upgraded to 1.11.5
* **@waitroom/models:** upgraded to 1.44.0
* **@waitroom/utils:** upgraded to 1.28.5

## @waitroom/analytics [1.28.6](https://github.com/Waitroom/waitroom/compare/@waitroom/analytics@1.28.5...@waitroom/analytics@1.28.6) (2022-07-25)





### Dependencies

* **@waitroom/tests:** upgraded to 1.11.4
* **@waitroom/models:** upgraded to 1.43.2
* **@waitroom/utils:** upgraded to 1.28.4

## @waitroom/analytics [1.28.5](https://github.com/Waitroom/waitroom/compare/@waitroom/analytics@1.28.4...@waitroom/analytics@1.28.5) (2022-07-19)





### Dependencies

* **@waitroom/tests:** upgraded to 1.11.3

## @waitroom/analytics [1.28.4](https://github.com/Waitroom/waitroom/compare/@waitroom/analytics@1.28.3...@waitroom/analytics@1.28.4) (2022-07-18)





### Dependencies

* **@waitroom/tests:** upgraded to 1.11.2
* **@waitroom/models:** upgraded to 1.43.1
* **@waitroom/utils:** upgraded to 1.28.3

## @waitroom/analytics [1.28.3](https://github.com/Waitroom/waitroom/compare/@waitroom/analytics@1.28.2...@waitroom/analytics@1.28.3) (2022-07-06)





### Dependencies

* **@waitroom/tests:** upgraded to 1.11.1
* **@waitroom/models:** upgraded to 1.43.0
* **@waitroom/utils:** upgraded to 1.28.2

## @waitroom/analytics [1.28.2](https://github.com/Waitroom/waitroom/compare/@waitroom/analytics@1.28.1...@waitroom/analytics@1.28.2) (2022-07-04)


### Bug Fixes

* trixta auth ([808a6b8](https://github.com/Waitroom/waitroom/commit/808a6b829c4ce7898fd46064217d58de60946739))

## @waitroom/analytics [1.28.1](https://github.com/Waitroom/waitroom/compare/@waitroom/analytics@1.28.0...@waitroom/analytics@1.28.1) (2022-07-01)





### Dependencies

* **@waitroom/utils:** upgraded to 1.28.1

# @waitroom/analytics [1.28.0](https://github.com/Waitroom/waitroom/compare/@waitroom/analytics@1.27.2...@waitroom/analytics@1.28.0) (2022-06-30)


### Features

* request access live session ([#881](https://github.com/Waitroom/rumi.ai/issues/881)) ([13b0415](https://github.com/Waitroom/waitroom/commit/13b0415b2ae8ca1cddc993e17894b2fea679e4cb))





### Dependencies

* **@waitroom/tests:** upgraded to 1.11.0
* **@waitroom/models:** upgraded to 1.42.0
* **@waitroom/utils:** upgraded to 1.28.0

## @waitroom/analytics [1.27.2](https://github.com/Waitroom/waitroom/compare/@waitroom/analytics@1.27.1...@waitroom/analytics@1.27.2) (2022-06-29)


### Bug Fixes

* mobile clashing events with common ([0d12154](https://github.com/Waitroom/waitroom/commit/0d12154be71d6a5086847d2a57bb517ce5876914))


### Reverts

* reverted agora package ([2f91a5d](https://github.com/Waitroom/waitroom/commit/2f91a5db782c5846053f99c2bc6c727d8c5be22b))

## @waitroom/analytics [1.27.1](https://github.com/Waitroom/waitroom/compare/@waitroom/analytics@1.27.0...@waitroom/analytics@1.27.1) (2022-06-22)


### Bug Fixes

* react query version miss match ([10af79c](https://github.com/Waitroom/waitroom/commit/10af79cf084d867c4807b86d3a072e1179faf2ef))

# @waitroom/analytics [1.27.0](https://github.com/Waitroom/waitroom/compare/@waitroom/analytics@1.26.10...@waitroom/analytics@1.27.0) (2022-06-22)


### Features

* improve useSessionBranchLink hook ([7677c80](https://github.com/Waitroom/waitroom/commit/7677c808e837e01468330af0e480868df2830bde))

## @waitroom/analytics [1.26.10](https://github.com/Waitroom/waitroom/compare/@waitroom/analytics@1.26.9...@waitroom/analytics@1.26.10) (2022-06-21)





### Dependencies

* **@waitroom/tests:** upgraded to 1.10.0

## @waitroom/analytics [1.26.9](https://github.com/Waitroom/waitroom/compare/@waitroom/analytics@1.26.8...@waitroom/analytics@1.26.9) (2022-06-20)





### Dependencies

* **@waitroom/tests:** upgraded to 1.9.0
* **@waitroom/models:** upgraded to 1.41.0
* **@waitroom/utils:** upgraded to 1.27.0

## @waitroom/analytics [1.26.8](https://github.com/Waitroom/waitroom/compare/@waitroom/analytics@1.26.7...@waitroom/analytics@1.26.8) (2022-06-20)





### Dependencies

* **@waitroom/utils:** upgraded to 1.26.3

## @waitroom/analytics [1.26.7](https://github.com/Waitroom/waitroom/compare/@waitroom/analytics@1.26.6...@waitroom/analytics@1.26.7) (2022-06-17)


### Bug Fixes

* added --passWithNoTests as pre commit hook starts breaking ([#858](https://github.com/Waitroom/rumi.ai/issues/858)) ([9ab4329](https://github.com/Waitroom/waitroom/commit/9ab43299a3a105d83235f767be139914ad683321))





### Dependencies

* **@waitroom/tests:** upgraded to 1.8.2
* **@waitroom/models:** upgraded to 1.40.2
* **@waitroom/utils:** upgraded to 1.26.2

## @waitroom/analytics [1.26.6](https://github.com/Waitroom/waitroom/compare/@waitroom/analytics@1.26.5...@waitroom/analytics@1.26.6) (2022-06-17)





### Dependencies

* **@waitroom/tests:** upgraded to 1.8.1
* **@waitroom/models:** upgraded to 1.40.1
* **@waitroom/utils:** upgraded to 1.26.1

## @waitroom/analytics [1.26.5](https://github.com/Waitroom/waitroom/compare/@waitroom/analytics@1.26.4...@waitroom/analytics@1.26.5) (2022-06-15)





### Dependencies

* **@waitroom/tests:** upgraded to 1.8.0
* **@waitroom/models:** upgraded to 1.40.0
* **@waitroom/utils:** upgraded to 1.26.0

## @waitroom/analytics [1.26.4](https://github.com/Waitroom/waitroom/compare/@waitroom/analytics@1.26.3...@waitroom/analytics@1.26.4) (2022-06-08)





### Dependencies

* **@waitroom/tests:** upgraded to 1.7.0
* **@waitroom/models:** upgraded to 1.39.0
* **@waitroom/utils:** upgraded to 1.25.22

## @waitroom/analytics [1.26.3](https://github.com/Waitroom/waitroom/compare/@waitroom/analytics@1.26.2...@waitroom/analytics@1.26.3) (2022-06-07)


### Bug Fixes

* models package version ([291e856](https://github.com/Waitroom/waitroom/commit/291e8561dbba78bf49a2d445134c6381b33ea77d))
* package versions ([05c81d9](https://github.com/Waitroom/waitroom/commit/05c81d9d96aafd8d2a7b25dbeccb28cfec9bf039))





### Dependencies

* **@waitroom/tests:** upgraded to 1.6.3
* **@waitroom/models:** upgraded to 1.38.1
* **@waitroom/utils:** upgraded to 1.25.21

## @waitroom/analytics [1.26.2](https://github.com/Waitroom/waitroom/compare/@waitroom/analytics@1.26.1...@waitroom/analytics@1.26.2) (2022-06-07)





### Dependencies

* **@waitroom/tests:** upgraded to 1.6.3
* **@waitroom/models:** upgraded to 1.38.1
* **@waitroom/utils:** upgraded to 1.25.21

## @waitroom/analytics [1.26.1](https://github.com/Waitroom/waitroom/compare/@waitroom/analytics@1.26.0...@waitroom/analytics@1.26.1) (2022-06-06)





### Dependencies

* **@waitroom/tests:** upgraded to 1.6.2
* **@waitroom/models:** upgraded to 1.38.0
* **@waitroom/utils:** upgraded to 1.25.20

# @waitroom/analytics [1.26.0](https://github.com/Waitroom/waitroom/compare/@waitroom/analytics@1.25.0...@waitroom/analytics@1.26.0) (2022-06-01)


### Features

* STATUS_VIEWER role is being replaced by SESSION_INFO_VIEWER ([7f9e02c](https://github.com/Waitroom/waitroom/commit/7f9e02cd4c9f30056492502f548b2c189647a962))





### Dependencies

* **@waitroom/tests:** upgraded to 1.6.1
* **@waitroom/models:** upgraded to 1.37.0
* **@waitroom/utils:** upgraded to 1.25.19

# @waitroom/analytics [1.25.0](https://github.com/Waitroom/waitroom/compare/@waitroom/analytics@1.24.0...@waitroom/analytics@1.25.0) (2022-05-24)


### Features

* cre 404 create invite form ([#811](https://github.com/Waitroom/rumi.ai/issues/811)) ([efd673d](https://github.com/Waitroom/waitroom/commit/efd673d3c4d6b0a96e78751909f9064f4141c1ac))





### Dependencies

* **@waitroom/tests:** upgraded to 1.6.0
* **@waitroom/models:** upgraded to 1.36.0
* **@waitroom/utils:** upgraded to 1.25.18

# @waitroom/analytics [1.24.0](https://github.com/Waitroom/waitroom/compare/@waitroom/analytics@1.23.0...@waitroom/analytics@1.24.0) (2022-05-23)


### Features

* Feature/cre 346 update the session form UI new ([#812](https://github.com/Waitroom/rumi.ai/issues/812)) ([2e38151](https://github.com/Waitroom/waitroom/commit/2e38151fff6b62ccd96864f3beeb398ff344721e)), closes [#806](https://github.com/Waitroom/rumi.ai/issues/806) [#810](https://github.com/Waitroom/rumi.ai/issues/810)





### Dependencies

* **@waitroom/tests:** upgraded to 1.5.5
* **@waitroom/models:** upgraded to 1.35.0
* **@waitroom/utils:** upgraded to 1.25.17

# @waitroom/analytics [1.23.0](https://github.com/Waitroom/waitroom/compare/@waitroom/analytics@1.22.7...@waitroom/analytics@1.23.0) (2022-05-20)


### Features

* wr 1162 implement refresh token ([#791](https://github.com/Waitroom/rumi.ai/issues/791)) ([c75366e](https://github.com/Waitroom/waitroom/commit/c75366e4412d308270096b2c25ba7572bc03c3a7))





### Dependencies

* **@waitroom/tests:** upgraded to 1.5.4
* **@waitroom/models:** upgraded to 1.34.0
* **@waitroom/utils:** upgraded to 1.25.16

## @waitroom/analytics [1.22.7](https://github.com/Waitroom/waitroom/compare/@waitroom/analytics@1.22.6...@waitroom/analytics@1.22.7) (2022-05-19)





### Dependencies

* **@waitroom/tests:** upgraded to 1.5.3
* **@waitroom/models:** upgraded to 1.33.0
* **@waitroom/utils:** upgraded to 1.25.15

## @waitroom/analytics [1.22.6](https://github.com/Waitroom/waitroom/compare/@waitroom/analytics@1.22.5...@waitroom/analytics@1.22.6) (2022-05-18)





### Dependencies

* **@waitroom/tests:** upgraded to 1.5.2
* **@waitroom/models:** upgraded to 1.32.0
* **@waitroom/utils:** upgraded to 1.25.14

## @waitroom/analytics [1.22.5](https://github.com/Waitroom/waitroom/compare/@waitroom/analytics@1.22.4...@waitroom/analytics@1.22.5) (2022-05-18)





### Dependencies

* **@waitroom/tests:** upgraded to 1.5.1
* **@waitroom/models:** upgraded to 1.31.0
* **@waitroom/utils:** upgraded to 1.25.13

## @waitroom/analytics [1.22.4](https://github.com/Waitroom/waitroom/compare/@waitroom/analytics@1.22.3...@waitroom/analytics@1.22.4) (2022-05-17)





### Dependencies

* **@waitroom/tests:** upgraded to 1.5.0
* **@waitroom/models:** upgraded to 1.30.0
* **@waitroom/utils:** upgraded to 1.25.12

## @waitroom/analytics [1.22.3](https://github.com/Waitroom/waitroom/compare/@waitroom/analytics@1.22.2...@waitroom/analytics@1.22.3) (2022-05-12)





### Dependencies

* **@waitroom/tests:** upgraded to 1.4.4
* **@waitroom/models:** upgraded to 1.29.1
* **@waitroom/utils:** upgraded to 1.25.11

## @waitroom/analytics [1.22.2](https://github.com/Waitroom/waitroom/compare/@waitroom/analytics@1.22.1...@waitroom/analytics@1.22.2) (2022-05-09)


### Bug Fixes

* common package ([a0bc6df](https://github.com/Waitroom/waitroom/commit/a0bc6df7cbc04c281714dc8f104bf5ee05b87920))
* fixed tests ([7ce91a4](https://github.com/Waitroom/waitroom/commit/7ce91a4952afbb14d7a8b289b48ab086b1888a62))





### Dependencies

* **@waitroom/tests:** upgraded to 1.4.3
* **@waitroom/models:** upgraded to 1.29.0
* **@waitroom/utils:** upgraded to 1.25.10

## @waitroom/analytics [1.22.1](https://github.com/Waitroom/waitroom/compare/@waitroom/analytics@1.22.0...@waitroom/analytics@1.22.1) (2022-05-06)





### Dependencies

* **@waitroom/tests:** upgraded to 1.4.3
* **@waitroom/models:** upgraded to 1.29.0
* **@waitroom/utils:** upgraded to 1.25.10

# @waitroom/analytics [1.22.0](https://github.com/Waitroom/waitroom/compare/@waitroom/analytics@1.21.8...@waitroom/analytics@1.22.0) (2022-05-05)


### Features

* implement trixta types ([09eb5fd](https://github.com/Waitroom/waitroom/commit/09eb5fd84a09d2d1556891dfbc5f286dec1304a1))





### Dependencies

* **@waitroom/tests:** upgraded to 1.4.2
* **@waitroom/models:** upgraded to 1.28.0
* **@waitroom/utils:** upgraded to 1.25.9

## @waitroom/analytics [1.21.8](https://github.com/Waitroom/waitroom/compare/@waitroom/analytics@1.21.7...@waitroom/analytics@1.21.8) (2022-05-04)





### Dependencies

* **@waitroom/tests:** upgraded to 1.4.1
* **@waitroom/models:** upgraded to 1.27.0
* **@waitroom/utils:** upgraded to 1.25.8

## @waitroom/analytics [1.21.7](https://github.com/Waitroom/waitroom/compare/@waitroom/analytics@1.21.6...@waitroom/analytics@1.21.7) (2022-05-03)





### Dependencies

* **@waitroom/tests:** upgraded to 1.4.0
* **@waitroom/models:** upgraded to 1.26.0
* **@waitroom/utils:** upgraded to 1.25.7

## @waitroom/analytics [1.21.6](https://github.com/Waitroom/waitroom/compare/@waitroom/analytics@1.21.5...@waitroom/analytics@1.21.6) (2022-05-03)





### Dependencies

* **@waitroom/tests:** upgraded to 1.3.6
* **@waitroom/models:** upgraded to 1.25.1
* **@waitroom/utils:** upgraded to 1.25.6

## @waitroom/analytics [1.21.5](https://github.com/Waitroom/waitroom/compare/@waitroom/analytics@1.21.4...@waitroom/analytics@1.21.5) (2022-05-03)





### Dependencies

* **@waitroom/tests:** upgraded to 1.3.5
* **@waitroom/models:** upgraded to 1.25.0
* **@waitroom/utils:** upgraded to 1.25.5

## @waitroom/analytics [1.21.4](https://github.com/Waitroom/waitroom/compare/@waitroom/analytics@1.21.3...@waitroom/analytics@1.21.4) (2022-04-20)





### Dependencies

* **@waitroom/tests:** upgraded to 1.3.4
* **@waitroom/models:** upgraded to 1.24.0
* **@waitroom/utils:** upgraded to 1.25.4

## @waitroom/analytics [1.21.3](https://github.com/Waitroom/waitroom/compare/@waitroom/analytics@1.21.2...@waitroom/analytics@1.21.3) (2022-04-20)





### Dependencies

* **@waitroom/tests:** upgraded to 1.3.3
* **@waitroom/models:** upgraded to 1.23.2
* **@waitroom/utils:** upgraded to 1.25.3

## @waitroom/analytics [1.21.2](https://github.com/Waitroom/waitroom/compare/@waitroom/analytics@1.21.1...@waitroom/analytics@1.21.2) (2022-04-15)





### Dependencies

* **@waitroom/tests:** upgraded to 1.3.2
* **@waitroom/utils:** upgraded to 1.25.2

## @waitroom/analytics [1.21.1](https://github.com/Waitroom/waitroom/compare/@waitroom/analytics@1.21.0...@waitroom/analytics@1.21.1) (2022-04-14)





### Dependencies

* **@waitroom/tests:** upgraded to 1.3.1
* **@waitroom/models:** upgraded to 1.23.1
* **@waitroom/utils:** upgraded to 1.25.1

# @waitroom/analytics [1.21.0](https://github.com/Waitroom/waitroom/compare/@waitroom/analytics@1.20.2...@waitroom/analytics@1.21.0) (2022-04-13)


### Features

* wr 748 develop and improve trixta tools for data fetching ([#755](https://github.com/Waitroom/rumi.ai/issues/755)) ([8d0a6d9](https://github.com/Waitroom/waitroom/commit/8d0a6d9da4205149abf518a688913c4e331af4e0))





### Dependencies

* **@waitroom/tests:** upgraded to 1.3.0
* **@waitroom/models:** upgraded to 1.23.0
* **@waitroom/utils:** upgraded to 1.25.0

## @waitroom/analytics [1.20.2](https://github.com/Waitroom/waitroom/compare/@waitroom/analytics@1.20.1...@waitroom/analytics@1.20.2) (2022-04-11)





### Dependencies

* **@waitroom/utils:** upgraded to 1.24.5
* **@waitroom/models:** upgraded to 1.22.0
* **@waitroom/tests:** upgraded to 1.2.6

## @waitroom/analytics [1.20.1](https://github.com/Waitroom/waitroom/compare/@waitroom/analytics@1.20.0...@waitroom/analytics@1.20.1) (2022-04-11)


### Bug Fixes

* updated checkpoint interval to 5sec ([#742](https://github.com/Waitroom/rumi.ai/issues/742)) ([021d900](https://github.com/Waitroom/waitroom/commit/021d900ab9bd726b8727584e9f2665a3777477f6))

# @waitroom/analytics [1.20.0](https://github.com/Waitroom/waitroom/compare/@waitroom/analytics@1.19.0...@waitroom/analytics@1.20.0) (2022-04-08)


### Bug Fixes

* fix common dependency change ([#741](https://github.com/Waitroom/rumi.ai/issues/741)) ([b7632ec](https://github.com/Waitroom/waitroom/commit/b7632ecd30aaf2eb2a15bdbf8efaafa623b7e9d1))


### Features

* implement trixta recording tracking events ([#738](https://github.com/Waitroom/rumi.ai/issues/738)) ([2aca224](https://github.com/Waitroom/waitroom/commit/2aca22485b25949f9747506487be65f2170ec305))
* update packages ([#740](https://github.com/Waitroom/rumi.ai/issues/740)) ([b3fc7ca](https://github.com/Waitroom/waitroom/commit/b3fc7ca467efe4b83aa3d936a4f8e57646a238b1))





### Dependencies

* **@waitroom/utils:** upgraded to 1.24.4
* **@waitroom/models:** upgraded to 1.21.0
* **@waitroom/tests:** upgraded to 1.2.5

# @waitroom/analytics [1.19.0](https://github.com/Waitroom/waitroom/compare/@waitroom/analytics@1.18.20...@waitroom/analytics@1.19.0) (2022-04-07)


### Features

* dummy commit for version bump ([#737](https://github.com/Waitroom/rumi.ai/issues/737)) ([d14c7ee](https://github.com/Waitroom/waitroom/commit/d14c7eef9fb21574182d6f07ce9704a1ec6eedff))

## @waitroom/analytics [1.18.20](https://github.com/Waitroom/waitroom/compare/@waitroom/analytics@1.18.19...@waitroom/analytics@1.18.20) (2022-04-04)





### Dependencies

* **@waitroom/utils:** upgraded to 1.24.3
* **@waitroom/models:** upgraded to 1.20.0
* **@waitroom/tests:** upgraded to 1.2.4

## @waitroom/analytics [1.18.19](https://github.com/Waitroom/waitroom/compare/@waitroom/analytics@1.18.18...@waitroom/analytics@1.18.19) (2022-03-29)

## @waitroom/analytics [1.18.18](https://github.com/Waitroom/waitroom/compare/@waitroom/analytics@1.18.17...@waitroom/analytics@1.18.18) (2022-03-29)


### Bug Fixes

* updated apple sign in package ([#726](https://github.com/Waitroom/rumi.ai/issues/726)) ([9bc3962](https://github.com/Waitroom/waitroom/commit/9bc396259005c6da1bf586a9f9d7d81b93013234))

## @waitroom/analytics [1.18.17](https://github.com/Waitroom/waitroom/compare/@waitroom/analytics@1.18.16...@waitroom/analytics@1.18.17) (2022-03-28)


### Bug Fixes

* when logging out should resubmit to trixta for sessions ([#720](https://github.com/Waitroom/rumi.ai/issues/720)) ([8cbd720](https://github.com/Waitroom/waitroom/commit/8cbd720a3a744d49de0ce521ec23e51ec8f14ff7))

## @waitroom/analytics [1.18.16](https://github.com/Waitroom/waitroom/compare/@waitroom/analytics@1.18.15...@waitroom/analytics@1.18.16) (2022-03-25)





### Dependencies

* **@waitroom/utils:** upgraded to 1.24.2
* **@waitroom/models:** upgraded to 1.19.3
* **@waitroom/tests:** upgraded to 1.2.3

## @waitroom/analytics [1.18.15](https://github.com/Waitroom/waitroom/compare/@waitroom/analytics@1.18.14...@waitroom/analytics@1.18.15) (2022-03-25)





### Dependencies

* **@waitroom/utils:** upgraded to 1.24.1
* **@waitroom/models:** upgraded to 1.19.2
* **@waitroom/tests:** upgraded to 1.2.2

## @waitroom/analytics [1.18.14](https://github.com/Waitroom/waitroom/compare/@waitroom/analytics@1.18.13...@waitroom/analytics@1.18.14) (2022-03-22)


### Bug Fixes

* down grade utils ([#707](https://github.com/Waitroom/rumi.ai/issues/707)) ([51a9104](https://github.com/Waitroom/waitroom/commit/51a9104df6df4b6abed465a78acab23c7f93a0f6))





### Dependencies

* **@waitroom/utils:** upgraded to 1.24.0
* **@waitroom/models:** upgraded to 1.19.1
* **@waitroom/tests:** upgraded to 1.2.1

## @waitroom/analytics [1.18.13](https://github.com/Waitroom/waitroom/compare/@waitroom/analytics@1.18.12...@waitroom/analytics@1.18.13) (2022-03-22)





### Dependencies

* **@waitroom/utils:** upgraded to 1.24.0

## @waitroom/analytics [1.18.12](https://github.com/Waitroom/waitroom/compare/@waitroom/analytics@1.18.11...@waitroom/analytics@1.18.12) (2022-03-22)





### Dependencies

* **@waitroom/utils:** upgraded to 1.23.0
* **@waitroom/models:** upgraded to 1.19.0
* **@waitroom/tests:** upgraded to 1.2.0

## @waitroom/analytics [1.18.11](https://github.com/Waitroom/waitroom/compare/@waitroom/analytics@1.18.10...@waitroom/analytics@1.18.11) (2022-03-21)





### Dependencies

* **@waitroom/utils:** upgraded to 1.22.3
* **@waitroom/models:** upgraded to 1.18.0
* **@waitroom/tests:** upgraded to 1.1.3

## @waitroom/analytics [1.18.10](https://github.com/Waitroom/waitroom/compare/@waitroom/analytics@1.18.9...@waitroom/analytics@1.18.10) (2022-03-21)





### Dependencies

* **@waitroom/utils:** upgraded to 1.22.2
* **@waitroom/models:** upgraded to 1.17.0
* **@waitroom/tests:** upgraded to 1.1.2

## @waitroom/analytics [1.18.9](https://github.com/Waitroom/waitroom/compare/@waitroom/analytics@1.18.8...@waitroom/analytics@1.18.9) (2022-03-18)





### Dependencies

* **@waitroom/utils:** upgraded to 1.22.1
* **@waitroom/models:** upgraded to 1.16.0
* **@waitroom/tests:** upgraded to 1.1.1

## @waitroom/analytics [1.18.8](https://github.com/Waitroom/waitroom/compare/@waitroom/analytics@1.18.7...@waitroom/analytics@1.18.8) (2022-03-18)





### Dependencies

* **@waitroom/utils:** upgraded to 1.22.0
* **@waitroom/models:** upgraded to 1.15.0
* **@waitroom/tests:** upgraded to 1.1.0

## @waitroom/analytics [1.18.7](https://github.com/Waitroom/waitroom/compare/@waitroom/analytics@1.18.6...@waitroom/analytics@1.18.7) (2022-03-14)


### Bug Fixes

* fixed assets images ([b29b075](https://github.com/Waitroom/waitroom/commit/b29b075326e7c3ac4713377c2adda46a0454d29d))

## @waitroom/analytics [1.18.6](https://github.com/Waitroom/waitroom/compare/@waitroom/analytics@1.18.5...@waitroom/analytics@1.18.6) (2022-03-14)


### Bug Fixes

* fixed tests ([48f6a5d](https://github.com/Waitroom/waitroom/commit/48f6a5da715704bf69b5232cac71aed70a7c78d2))
* package versions ([2a84db3](https://github.com/Waitroom/waitroom/commit/2a84db30a81c3590575a965c7dd4f2dd584aed2e))





### Dependencies

* **@waitroom/utils:** upgraded to 1.21.1
* **@waitroom/models:** upgraded to 1.14.1
* **@waitroom/tests:** upgraded to 1.0.0

## @waitroom/analytics [1.18.5](https://github.com/Waitroom/waitroom/compare/@waitroom/analytics@1.18.4...@waitroom/analytics@1.18.5) (2022-03-11)





### Dependencies

* **@waitroom/utils:** upgraded to 1.21.0

## @waitroom/analytics [1.18.4](https://github.com/Waitroom/waitroom/compare/@waitroom/analytics@1.18.3...@waitroom/analytics@1.18.4) (2022-03-10)





### Dependencies

* **@waitroom/utils:** upgraded to 1.20.3
* **@waitroom/models:** upgraded to 1.14.0

## @waitroom/analytics [1.18.3](https://github.com/Waitroom/waitroom/compare/@waitroom/analytics@1.18.2...@waitroom/analytics@1.18.3) (2022-03-09)


### Bug Fixes

* trixta response callback types are correct ([#666](https://github.com/Waitroom/rumi.ai/issues/666)) ([fb5b20f](https://github.com/Waitroom/waitroom/commit/fb5b20f6ff1643d5664bc4c69ac37d17745b6392))

## @waitroom/analytics [1.18.2](https://github.com/Waitroom/waitroom/compare/@waitroom/analytics@1.18.1...@waitroom/analytics@1.18.2) (2022-03-07)





### Dependencies

* **@waitroom/utils:** upgraded to 1.20.2
* **@waitroom/models:** upgraded to 1.13.2

## @waitroom/analytics [1.18.1](https://github.com/Waitroom/waitroom/compare/@waitroom/analytics@1.18.0...@waitroom/analytics@1.18.1) (2022-03-06)





### Dependencies

* **@waitroom/utils:** upgraded to 1.20.1
* **@waitroom/models:** upgraded to 1.13.1

# @waitroom/analytics [1.18.0](https://github.com/Waitroom/waitroom/compare/@waitroom/analytics@1.17.0...@waitroom/analytics@1.18.0) (2022-03-03)


### Features

* clip branch analytics has changed to use new tags and marketing title ([#647](https://github.com/Waitroom/rumi.ai/issues/647)) ([fbcff41](https://github.com/Waitroom/waitroom/commit/fbcff410c9d04537bfc6b03df268b50f18e63f93))

# @waitroom/analytics [1.17.0](https://github.com/Waitroom/waitroom/compare/@waitroom/analytics@1.16.4...@waitroom/analytics@1.17.0) (2022-03-01)


### Features

* session producer ([3f5675d](https://github.com/Waitroom/waitroom/commit/3f5675d7490bdb794eb30afefb18d195f9cce625))





### Dependencies

* **@waitroom/utils:** upgraded to 1.20.0
* **@waitroom/models:** upgraded to 1.13.0

## @waitroom/analytics [1.16.4](https://github.com/Waitroom/waitroom/compare/@waitroom/analytics@1.16.3...@waitroom/analytics@1.16.4) (2022-02-22)





### Dependencies

* **@waitroom/utils:** upgraded to 1.19.5
* **@waitroom/models:** upgraded to 1.12.4

## @waitroom/analytics [1.16.3](https://github.com/Waitroom/waitroom/compare/@waitroom/analytics@1.16.2...@waitroom/analytics@1.16.3) (2022-02-16)


### Bug Fixes

* now branch url generation can fallback ([#599](https://github.com/Waitroom/rumi.ai/issues/599)) ([62f33ee](https://github.com/Waitroom/waitroom/commit/62f33ee2e3781de217e211d2f04705cf03c60b6b))

## @waitroom/analytics [1.16.2](https://github.com/Waitroom/waitroom/compare/@waitroom/analytics@1.16.1...@waitroom/analytics@1.16.2) (2022-02-11)





### Dependencies

* **@waitroom/utils:** upgraded to 1.19.4

## @waitroom/analytics [1.16.1](https://github.com/Waitroom/waitroom/compare/@waitroom/analytics@1.16.0...@waitroom/analytics@1.16.1) (2022-02-11)





### Dependencies

* **@waitroom/utils:** upgraded to 1.19.3
* **@waitroom/models:** upgraded to 1.12.3

# @waitroom/analytics [1.16.0](https://github.com/Waitroom/waitroom/compare/@waitroom/analytics@1.15.6...@waitroom/analytics@1.16.0) (2022-02-07)


### Features

* split attribution and behaviour analytics ([#564](https://github.com/Waitroom/rumi.ai/issues/564)) ([3bd8840](https://github.com/Waitroom/waitroom/commit/3bd8840811c7684d6ee65f6e4f5f8dd14e27fa58))

## @waitroom/analytics [1.15.6](https://github.com/Waitroom/waitroom/compare/@waitroom/analytics@1.15.5...@waitroom/analytics@1.15.6) (2022-02-01)


### Bug Fixes

* branch analytics ([#550](https://github.com/Waitroom/rumi.ai/issues/550)) ([a11ea01](https://github.com/Waitroom/waitroom/commit/a11ea0113b471797f1077c2f9d6de45578a1b9c4))

## @waitroom/analytics [1.15.5](https://github.com/Waitroom/waitroom/compare/@waitroom/analytics@1.15.4...@waitroom/analytics@1.15.5) (2022-01-31)


### Bug Fixes

* loose option build fix ([fa09e7b](https://github.com/Waitroom/waitroom/commit/fa09e7bb49d063e7ab908784a539a834233dc336))





### Dependencies

* **@waitroom/utils:** upgraded to 1.19.2
* **@waitroom/models:** upgraded to 1.12.2

## @waitroom/analytics [1.15.4](https://github.com/Waitroom/waitroom/compare/@waitroom/analytics@1.15.3...@waitroom/analytics@1.15.4) (2022-01-26)





### Dependencies

* **@waitroom/utils:** upgraded to 1.19.1
* **@waitroom/models:** upgraded to 1.12.1

## @waitroom/analytics [1.15.3](https://github.com/Waitroom/waitroom/compare/@waitroom/analytics@1.15.2...@waitroom/analytics@1.15.3) (2022-01-26)


### Bug Fixes

* analytics package config ([#540](https://github.com/Waitroom/rumi.ai/issues/540)) ([986bb6f](https://github.com/Waitroom/waitroom/commit/986bb6f6c3d9da9b99304f1af659b38f8d161eeb))

## @waitroom/analytics [1.15.2](https://github.com/Waitroom/waitroom/compare/@waitroom/analytics@1.15.1...@waitroom/analytics@1.15.2) (2022-01-25)


### Bug Fixes

* use branch url for sharing include episode or clip ([#535](https://github.com/Waitroom/rumi.ai/issues/535)) ([c27f37b](https://github.com/Waitroom/waitroom/commit/c27f37b336404d7acbf392a0da53d41134d74d2c))

## @waitroom/analytics [1.15.1](https://github.com/Waitroom/waitroom/compare/@waitroom/analytics@1.15.0...@waitroom/analytics@1.15.1) (2022-01-25)

# @waitroom/analytics [1.15.0](https://github.com/Waitroom/waitroom/compare/@waitroom/analytics@1.14.0...@waitroom/analytics@1.15.0) (2022-01-20)


### Features

* added user cookie consent to amplitude ([#527](https://github.com/Waitroom/rumi.ai/issues/527)) ([47a23c0](https://github.com/Waitroom/waitroom/commit/47a23c0cf0d388ab3cb2e1ff882e0fc64858bc70))

# @waitroom/analytics [1.14.0](https://github.com/Waitroom/waitroom/compare/@waitroom/analytics@1.13.1...@waitroom/analytics@1.14.0) (2022-01-18)


### Features

* added redux store for analytics ([5bf19a7](https://github.com/Waitroom/waitroom/commit/5bf19a719dabec33f41f14a8091dd878af92ac01))





### Dependencies

* **@waitroom/utils:** upgraded to 1.19.0

## @waitroom/analytics [1.13.1](https://github.com/Waitroom/waitroom/compare/@waitroom/analytics@1.13.0...@waitroom/analytics@1.13.1) (2022-01-14)





### Dependencies

* **@waitroom/utils:** upgraded to 1.18.1
* **@waitroom/models:** upgraded to 1.12.0

# @waitroom/analytics [1.13.0](https://github.com/Waitroom/waitroom/compare/@waitroom/analytics@1.12.0...@waitroom/analytics@1.13.0) (2022-01-14)


### Features

* playback ([04438e0](https://github.com/Waitroom/waitroom/commit/04438e0f1e7d9de824f049c44c60dd410deba450))





### Dependencies

* **@waitroom/utils:** upgraded to 1.18.0
* **@waitroom/models:** upgraded to 1.11.0

# @waitroom/analytics [1.12.0](https://github.com/Waitroom/waitroom/compare/@waitroom/analytics@1.11.0...@waitroom/analytics@1.12.0) (2022-01-03)


### Features

* trixtajs updated ([#507](https://github.com/Waitroom/rumi.ai/issues/507)) ([76a4fc3](https://github.com/Waitroom/waitroom/commit/76a4fc3fc3f58956c3fd01a83096a1f5058fa9fa))

# @waitroom/analytics [1.11.0](https://github.com/Waitroom/waitroom/compare/@waitroom/analytics@1.10.3...@waitroom/analytics@1.11.0) (2021-12-22)


### Features

* release trixta ([#505](https://github.com/Waitroom/rumi.ai/issues/505)) ([5b3136c](https://github.com/Waitroom/waitroom/commit/5b3136c079a125258d174fce5ce740a775aee752))

## @waitroom/analytics [1.10.3](https://github.com/Waitroom/waitroom/compare/@waitroom/analytics@1.10.2...@waitroom/analytics@1.10.3) (2021-12-22)





### Dependencies

* **@waitroom/utils:** upgraded to 1.17.3
* **@waitroom/models:** upgraded to 1.10.0

## @waitroom/analytics [1.10.2](https://github.com/Waitroom/waitroom/compare/@waitroom/analytics@1.10.1...@waitroom/analytics@1.10.2) (2021-12-16)





### Dependencies

* **@waitroom/utils:** upgraded to 1.17.2
* **@waitroom/models:** upgraded to 1.9.2

## @waitroom/analytics [1.10.1](https://github.com/Waitroom/waitroom/compare/@waitroom/analytics@1.10.0...@waitroom/analytics@1.10.1) (2021-12-14)





### Dependencies

* **@waitroom/utils:** upgraded to 1.17.1
* **@waitroom/models:** upgraded to 1.9.1

# @waitroom/analytics [1.10.0](https://github.com/Waitroom/waitroom/compare/@waitroom/analytics@1.9.3...@waitroom/analytics@1.10.0) (2021-12-14)


### Features

* playback components, folder refactor ([c8399c4](https://github.com/Waitroom/waitroom/commit/c8399c464778221f5b4b0b04b045235d0fa429b1))





### Dependencies

* **@waitroom/utils:** upgraded to 1.17.0
* **@waitroom/models:** upgraded to 1.9.0

## @waitroom/analytics [1.9.3](https://github.com/Waitroom/waitroom/compare/@waitroom/analytics@1.9.2...@waitroom/analytics@1.9.3) (2021-12-08)





### Dependencies

* **@waitroom/utils:** upgraded to 1.16.3

## @waitroom/analytics [1.9.2](https://github.com/Waitroom/waitroom/compare/@waitroom/analytics@1.9.1...@waitroom/analytics@1.9.2) (2021-12-08)





### Dependencies

* **@waitroom/utils:** upgraded to 1.16.2
* **@waitroom/models:** upgraded to 1.8.1

## @waitroom/analytics [1.9.1](https://github.com/Waitroom/waitroom/compare/@waitroom/analytics@1.9.0...@waitroom/analytics@1.9.1) (2021-12-07)





### Dependencies

* **@waitroom/utils:** upgraded to 1.16.1

# @waitroom/analytics [1.9.0](https://github.com/Waitroom/waitroom/compare/@waitroom/analytics@1.8.1...@waitroom/analytics@1.9.0) (2021-12-07)


### Features

* refactor component folder structure, updated font weight, playback components ([de57f54](https://github.com/Waitroom/waitroom/commit/de57f547f9eceb3a66313687fd5f4ecec1cfd43a))





### Dependencies

* **@waitroom/utils:** upgraded to 1.16.0
* **@waitroom/models:** upgraded to 1.8.0

## @waitroom/analytics [1.8.1](https://github.com/Waitroom/waitroom/compare/@waitroom/analytics@1.8.0...@waitroom/analytics@1.8.1) (2021-12-06)





### Dependencies

* **@waitroom/models:** upgraded to 1.7.1

# @waitroom/analytics [1.8.0](https://github.com/Waitroom/waitroom/compare/@waitroom/analytics@1.7.0...@waitroom/analytics@1.8.0) (2021-12-02)


### Bug Fixes

* on end session clear host related reactions ([#478](https://github.com/Waitroom/rumi.ai/issues/478)) ([b936ef2](https://github.com/Waitroom/waitroom/commit/b936ef2d802182614d4b50d98e8f5b3ecc827d15))


### Features

* host pause and resume single reaction ([#474](https://github.com/Waitroom/rumi.ai/issues/474)) ([07b53fc](https://github.com/Waitroom/waitroom/commit/07b53fc58c426291a0c9cef738304bc95286542c))
* nfts ([7639545](https://github.com/Waitroom/waitroom/commit/7639545b6a38c9e4fd6a503e75bc8cf5570df1a5))





### Dependencies

* **@waitroom/utils:** upgraded to 1.15.0
* **@waitroom/models:** upgraded to 1.7.0

# @waitroom/analytics [1.7.0](https://github.com/Waitroom/waitroom/compare/@waitroom/analytics@1.6.5...@waitroom/analytics@1.7.0) (2021-11-18)


### Features

* common middlewares ([#464](https://github.com/Waitroom/rumi.ai/issues/464)) ([721d64c](https://github.com/Waitroom/waitroom/commit/721d64c56d0ab04d3bd68fc64996af722ddf383d))

## @waitroom/analytics [1.6.5](https://github.com/Waitroom/waitroom/compare/@waitroom/analytics@1.6.4...@waitroom/analytics@1.6.5) (2021-11-16)





### Dependencies

* **@waitroom/utils:** upgraded to 1.14.0

## @waitroom/analytics [1.6.4](https://github.com/Waitroom/waitroom/compare/@waitroom/analytics@1.6.3...@waitroom/analytics@1.6.4) (2021-11-16)





### Dependencies

* **@waitroom/utils:** upgraded to 1.13.0

## @waitroom/analytics [1.6.3](https://github.com/Waitroom/waitroom/compare/@waitroom/analytics@1.6.2...@waitroom/analytics@1.6.3) (2021-11-11)





### Dependencies

* **@waitroom/utils:** upgraded to 1.12.0

## @waitroom/analytics [1.6.2](https://github.com/Waitroom/waitroom/compare/@waitroom/analytics@1.6.1...@waitroom/analytics@1.6.2) (2021-11-09)





### Dependencies

* **@waitroom/models:** upgraded to 1.6.1

## @waitroom/analytics [1.6.1](https://github.com/Waitroom/waitroom/compare/@waitroom/analytics@1.6.0...@waitroom/analytics@1.6.1) (2021-10-27)

# @waitroom/analytics [1.6.0](https://github.com/Waitroom/waitroom/compare/@waitroom/analytics@1.5.0...@waitroom/analytics@1.6.0) (2021-10-06)


### Features

* agora update, fixed some stream issue ([63035d7](https://github.com/Waitroom/waitroom/commit/63035d7e3d787310b3c7e5d93fd73c64358b6ca5))





### Dependencies

* **@waitroom/utils:** upgraded to 1.11.0

# @waitroom/analytics [1.5.0](https://github.com/Waitroom/waitroom/compare/@waitroom/analytics@1.4.1...@waitroom/analytics@1.5.0) (2021-10-01)


### Features

* multiple session blocking ([#418](https://github.com/Waitroom/rumi.ai/issues/418)) ([40fa711](https://github.com/Waitroom/waitroom/commit/40fa711e5b601df490c0c3e6ce4cb3c7bc0b7df8))

## @waitroom/analytics [1.4.1](https://github.com/Waitroom/waitroom/compare/@waitroom/analytics@1.4.0...@waitroom/analytics@1.4.1) (2021-09-23)





### Dependencies

* **@waitroom/utils:** upgraded to 1.10.0

# @waitroom/analytics [1.4.0](https://github.com/Waitroom/waitroom/compare/@waitroom/analytics@1.3.0...@waitroom/analytics@1.4.0) (2021-09-20)


### Features

* layout-engine and config packages with tests ([#410](https://github.com/Waitroom/rumi.ai/issues/410)) ([815c604](https://github.com/Waitroom/waitroom/commit/815c604a443a657e8f4db16e3a27ba71553f9c01))

# @waitroom/analytics [1.3.0](https://github.com/Waitroom/waitroom/compare/@waitroom/analytics@1.2.1...@waitroom/analytics@1.3.0) (2021-09-17)


### Features

* user settings selectors, fixed publish command ([ed8ca64](https://github.com/Waitroom/waitroom/commit/ed8ca648e0e7fc60617cf2b18d971ff768280a88))

## @waitroom/analytics [1.2.1](https://github.com/Waitroom/waitroom/compare/@waitroom/analytics@1.2.0...@waitroom/analytics@1.2.1) (2021-09-15)


### Bug Fixes

* fixed publish ([7ef8fe6](https://github.com/Waitroom/waitroom/commit/7ef8fe680ceec57191f2f7faadc3cd50aeb8e49c))

# @waitroom/analytics [1.2.0](https://github.com/Waitroom/waitroom/compare/@waitroom/analytics@1.1.0...@waitroom/analytics@1.2.0) (2021-09-15)


### Bug Fixes

* fixed test coverage ([91d7692](https://github.com/Waitroom/waitroom/commit/91d7692c29a2ff8ff4ab2a17325a00a4e18cbf17))


### Features

* split common package ([#405](https://github.com/Waitroom/rumi.ai/issues/405)) ([19222cc](https://github.com/Waitroom/waitroom/commit/19222ccf9c7c5d425ee7201c272738a12c659ac3))

# @waitroom/analytics [1.1.0](https://github.com/Waitroom/waitroom/compare/@waitroom/analytics@1.0.0...@waitroom/analytics@1.1.0) (2021-09-10)


### Features

* implement analytics package ([#396](https://github.com/Waitroom/rumi.ai/issues/396)) ([6322f20](https://github.com/Waitroom/waitroom/commit/6322f20229a9f13b8b7e1640b7974ace6680c59a))

# @waitroom/analytics 1.0.0 (2021-09-08)


### Features

* create analytics package ([#394](https://github.com/Waitroom/rumi.ai/issues/394)) ([17f93ee](https://github.com/Waitroom/waitroom/commit/17f93ee469e2c6592006a499c0ef03feac127284))
* create analytics package ([#395](https://github.com/Waitroom/rumi.ai/issues/395)) ([ba5f728](https://github.com/Waitroom/waitroom/commit/ba5f728a5f6c29ce703c29d4fe1db602a105e5bf))
