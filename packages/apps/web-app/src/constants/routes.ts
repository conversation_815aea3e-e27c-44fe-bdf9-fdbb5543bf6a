export const routes = {
  HOME: '/',
  HOME_EXPLICIT: '/home',
  SHOWS: '/shows',
  PRICING: '/pricing',
  CHECKOUT: '/pricing/checkout',
  SESSION: {
    route: '/sessions/:id/:recurrenceId?',
    link: '/sessions',
  },
  /** @deprecated: use SESSION instead */
  RECORDINGS: {
    route: '/recordings/:sessionId/:recurrenceId?',
    link: '/recordings',
  },
  LOBBY: { route: '/lobby/:slug', link: '/lobby' },
  ABOUT: '/about',
  NOT_FOUND: '/404',
  ERROR: {
    route: '/error/:type?',
    link: '/error',
  },
  AUTH: {
    DEFAULT: { route: '/auth/*', link: '/auth' },
    LOGIN: { route: '/login', link: '/auth/login' },
    REGISTER: { route: '/register', link: '/auth/register' },
  },
  HOST: '/host',
  PROFILE: {
    DEFAULT: { route: '/profile/*', link: '/profile' },
    DELETE: { route: '/delete', link: '/profile/delete' },
  },
  DASHBOARD: {
    DEFAULT: { route: '/dashboard/*', link: '/dashboard' },
    SESSIONS: { route: '/sessions', link: '/dashboard/sessions' },
    SESSION: { route: '/sessions/:id', link: '/dashboard/sessions' },
    SESSION_SHARE: {
      route: '/sessions/:id/:view',
      link: '/dashboard/sessions',
    },
    ADD_SESSION: { route: '/sessions/add', link: '/dashboard/sessions/add' },
    PROFILE: {
      DEFAULT: { route: '/profile/*', link: '/dashboard/profile' },
      DELETE: { route: '/delete', link: '/dashboard/profile/delete' },
    },
    INTEGRATIONS: { route: '/integrations', link: '/dashboard/integrations' },
    LOBBY: { route: '/lobby', link: '/dashboard/lobby' },
    SETTINGS: { route: 'settings', link: '/dashboard/settings' },
    SUBSCRIPTION: { route: '/subscription', link: '/dashboard/subscription' },
    XRAY: {
      DEFAULT: { route: '/xray/*', link: '/dashboard/xray' },
      SETUP: {
        fullRoute: '/dashboard/xray/setup/:id?',
        route: '/setup/:id?',
        link: '/dashboard/xray/setup',
      },
      EDIT: {
        fullRoute: '/dashboard/xray/edit/:id',
        route: '/edit/:id',
        link: '/dashboard/xray/edit',
      },
    },
    MEETING_MEMORY: {
      fullRoute: '/dashboard/meeting-memory/:id?',
      route: '/meeting-memory/:id?',
      link: '/dashboard/meeting-memory',
    },
  },
  REQUEST_ACCESS: '/request-access',
  SESSION_IS_FULL: '/session-is-full',
  PRIVATE_SESSIONS: '/private-sessions',
  EXTERNAL: '/external',
  /** DEPRECATED: use PAGE instead */
  LANDING: {
    route: '/landing/:slug',
    link: '/landing',
  },
  PAGE: {
    route: '/page/:slug',
    link: '/page',
  },
  PRODUCT: {
    route: '/product/:slug',
    link: '/product',
  },
  RESOURCES: {
    route: '/resources/:slug',
    link: '/resources',
  },
  USE_CASES: {
    route: '/use-cases/:slug',
    link: '/use-cases',
  },
  TURN_BASED_MEETINGS: {
    route: '/turn-based-meetings/:slug',
    link: '/turn-based-meetings',
  },
};
