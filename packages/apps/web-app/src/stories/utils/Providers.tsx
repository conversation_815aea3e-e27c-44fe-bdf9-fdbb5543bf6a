/** setup default storybook config */
import './config';
/** setup configs */
import '../../modules/core/config/init';
import '../../modules/locales/services/i18n';
/** other imports */
import { Box, Button, ChakraProvider, DarkMode, Input, useBoolean } from '@chakra-ui/react';
import { faLock } from '@fortawesome/pro-regular-svg-icons';
import { Decorator } from '@storybook/react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { authService, Tokens } from '@waitroom/auth';
import { initConfig as initCommon } from '@waitroom/common';
import { initCommonApi } from '@waitroom/common-api';
import { ClientDetails, STORAGE_AUTH_TOKENS } from '@waitroom/models';
import i18n from 'i18next';
import { ReactNode, useRef, useState } from 'react';
import { I18nextProvider } from 'react-i18next';
import { MemoryRouter } from 'react-router-dom';
import { Icon } from '../../modules/core/components/Icon/Icon';
import Modal from '../../modules/core/components/Modal/Modal';
import { LUXOR_URL, WORMHOLE_URL } from '../../modules/core/config';
import { storageService } from '../../modules/core/services/storage';
import tDashEn from '../../modules/locales/res/en/dashboard.json';
import tSessionEn from '../../modules/locales/res/en/session.json';
import tDashIt from '../../modules/locales/res/it/dashboard.json';
import tSessionIt from '../../modules/locales/res/it/session.json';
import theme from '../../modules/theme';

const trans = {
  en: {
    res: {
      ...tDashEn,
      ...tSessionEn,
    },
  },
  it: {
    res: {
      ...tDashIt,
      ...tSessionIt,
    },
  },
};
i18n.addResourceBundle('en', 'translation', trans, true);

initCommonApi({
  baseURL: WORMHOLE_URL,
  version: 'v1.0',
  getAuthToken: Promise.resolve(''),
  getFingerPrint: Promise.resolve(''),
  getClientDetails: Promise.resolve({} as ClientDetails),
  fetchEventSource: () => undefined as any,
  sanityUrl: '',
});
initCommon({
  domains: {
    luxor: LUXOR_URL,
    wormhole: WORMHOLE_URL,
  },
  featureFlags: {},
  storage: storageService,
  dateTime: {
    timezone: {},
  } as any,
});
export const storiesQueryClient = new QueryClient();

export const StorybookProviders = ({ children }: { children: ReactNode }) => {
  return (
    <>
      <QueryClientProvider client={storiesQueryClient}>
        <MemoryRouter>
          <I18nextProvider i18n={i18n}>
            <ChakraProvider resetCSS theme={theme}>
              {children}
            </ChakraProvider>
          </I18nextProvider>
        </MemoryRouter>
      </QueryClientProvider>
    </>
  );
};

export const ChakraDecorator: Decorator = (Story) => (
  <ChakraProvider resetCSS theme={theme}>
    <Story />
  </ChakraProvider>
);

export const QueryDecorator: Decorator = (Story) => (
  <QueryClientProvider client={storiesQueryClient}>
    <Story />
  </QueryClientProvider>
);

export const StorybookProvidersDecorator: Decorator = (Story) => (
  <StorybookProviders>
    <Story />
  </StorybookProviders>
);

export const StorybookDarkModeDecorator: Decorator = (Story) => (
  <DarkMode>
    <Box color="white">
      <Story />
    </Box>
  </DarkMode>
);

export const AuthDecorator: Decorator = (Story) => {
  const inputRef = useRef<HTMLInputElement>(null);
  const [isOpen, { toggle, off }] = useBoolean();
  const [token, setToken] = useState(
    storageService.getParsed<Tokens>(STORAGE_AUTH_TOKENS)?.accessToken,
  );

  return (
    <ChakraProvider resetCSS theme={theme}>
      <Box textAlign="right">
        <Button
          size="3xs"
          colorScheme="gray.200"
          onClick={toggle}
          leftIcon={<Icon icon={faLock} />}
          mb={1}
        >
          Auth
        </Button>
      </Box>
      <Modal isOpen={isOpen} onClose={off}>
        <Modal.Header />
        <Modal.Body mt={4}>
          <Input
            ref={inputRef}
            type="text"
            size="xs"
            placeholder="Auth token"
            defaultValue={token}
          />
        </Modal.Body>
        <Modal.Footer justifyContent="flex-end" gap={2}>
          <Button colorScheme="gray.900" size="xs" onClick={off}>
            Close
          </Button>
          <Button
            colorScheme="green"
            size="xs"
            onClick={() => {
              const value = inputRef.current?.value;
              if (!value?.length) return;
              setToken(value);
              authService.saveStorageTokens({
                accessToken: value,
              });
              initCommonApi({
                getAuthToken: Promise.resolve(value),
              });
              off();
            }}
          >
            Save
          </Button>
        </Modal.Footer>
      </Modal>
      <Story />
    </ChakraProvider>
  );
};
