import { Meta } from '@storybook/blocks';

<Meta title="web-app/Introduction" />

# Web-app

### Folder structure

[Folder structure](./?path=/docs/folder-structure--docs)

### Path aliases

For file imports, we use path aliases to simplify the imports. Take a look at the tsconfig.json file
to see the aliases.

Some examples:  
`@/*` => `./src/*`  
`@modules/*` => `./src/modules/*`  
`@core/*` => `./src/modules/core/*`  
`@locales/*` => `./src/modules/locales/*`  
`@sounds/*` => `./src/sounds/*`

Example: `import Button from '@modules/core/components/Button/Button';`
