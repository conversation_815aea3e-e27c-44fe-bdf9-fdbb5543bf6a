import { tabsAnatomy as parts } from '@chakra-ui/anatomy';
import { cssVar } from '@chakra-ui/react';
import {
  getColor,
  mode,
  PartsStyleFunction,
  PartsStyleInterpolation,
  PartsStyleObject,
} from '@chakra-ui/theme-tools';
import shadows from './shadows';

const $fg = cssVar('tabs-color');
const $bg = cssVar('tabs-bg');

const baseStyleTab = () => {
  return {
    _focus: {
      boxShadow: 'none',
    },
  };
};

const baseStyle: PartsStyleFunction<typeof parts> = () => ({
  tab: baseStyleTab(),
});

const sizes: Record<string, PartsStyleObject<typeof parts>> = {
  xs: {
    tab: {
      py: '3px',
      px: 3,
      fontSize: 'xs',
    },
  },
  sm: {
    tab: {
      py: 2,
    },
  },
  md: {
    tab: {
      py: 4,
    },
  },
  lg: {
    tab: {
      py: 5,
      px: [5, 6],
    },
  },
};

const variantEnclosed: PartsStyleFunction<typeof parts> = (props) => {
  const { colorScheme: c, theme, _selected } = props;
  return {
    tab: {
      borderRadius: 'full',
      border: '2px solid transparent',
      fontWeight: 'bold',
      color: getColor(theme, `${c}.600`),
      boxShadow: 'none',
      outline: 'none',
      mb: 0,
      _selected: {
        color: getColor(theme, `${c}.900`),
        bg: getColor(theme, `${c}.50`),
        boxShadow: shadows['card-sm'],
        ..._selected,
      },
    },
    tablist: {
      mb: 0,
      p: 1,
      borderBottom: 'none',
      rounded: 'full',
      display: 'inline-flex',
    },
  };
};

const variantSoftRounded: PartsStyleFunction<typeof parts> = (props) => {
  const { colorScheme: c = 'gray', theme } = props;
  return {
    tab: {
      py: [2, 2, 3],
      px: { base: 2, sm: 3, md: 5, xl: 6 },
      color: 'gray.500',
      rounded: 'md',
      fontWeight: 'bold',
      fontSize: ['sm', 'sm', 'md'],
      _selected: {
        color: getColor(theme, `${c}.900`),
        bg: getColor(theme, `${c}.200`),
      },
    },
  };
};

const variantLine: PartsStyleFunction<typeof parts> = (props) => {
  const { colorScheme: c, orientation } = props;
  const isVertical = orientation === 'vertical';
  const borderProp = isVertical ? 'borderStart' : 'borderBottom';
  const marginProp = isVertical ? 'marginStart' : 'marginBottom';

  return {
    tablist: {
      [borderProp]: '2px solid',
      borderColor: 'inherit',
    },
    tab: {
      position: 'relative',
      fontWeight: 'semibold',
      [borderProp]: '2px solid',
      borderColor: 'transparent',
      [marginProp]: '0',
      color: mode(`gray.600`, `gray.300`)(props),
      bg: $bg.reference,
      [marginProp]: '-2px',
      _selected: {
        color: 'inherit',
        bgColor: 'transparent',
        [$fg.variable]: `colors.${c}.600`,
        _dark: {
          [$fg.variable]: `colors.${c}.500`,
        },
        borderColor: `${c}.600`,
      },
      _active: {
        opacity: 0.9,
        [$bg.variable]: 'colors.gray.200',
        _dark: {
          [$bg.variable]: 'colors.whiteAlpha.300',
        },
      },
      _disabled: {
        _active: { bg: 'none' },
      },
    },
  };
};

const variants: Record<string, PartsStyleInterpolation<typeof parts>> = {
  line: variantLine,
  enclosed: variantEnclosed,
  'soft-rounded': variantSoftRounded,
};

export default {
  baseStyle,
  sizes,
  variants,
};
