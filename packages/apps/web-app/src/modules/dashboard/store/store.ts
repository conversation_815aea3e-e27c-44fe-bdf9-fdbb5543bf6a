import { createSelectors } from '@waitroom/state';
import { ComponentType } from 'react';
import { create } from 'zustand';

export type DashboardState = {
  // lobby access requested
  isRequested: boolean;
  layout: {
    leftMenuOpen?: boolean;
    rightMenuOpen?: boolean;
    rightMenuContent?: ComponentType;
  };
};

const initialState: DashboardState = {
  isRequested: false,
  layout: {},
};

const useStore = create<DashboardState>()(() => initialState);
export const useDashboardStore = createSelectors(useStore);

export const updateDashboardState = (
  update: Partial<DashboardState> | ((prev: DashboardState) => DashboardState),
) => {
  useStore.setState((prev) =>
    typeof update === 'function' ? update(prev) : { ...prev, ...update },
  );
};

export const updateDashboardLayout = (
  update:
    | Partial<DashboardState['layout']>
    | ((prev: DashboardState['layout']) => DashboardState['layout']),
) =>
  updateDashboardState((s) => ({
    ...s,
    layout: { ...s.layout, ...(typeof update === 'function' ? update(s.layout) : update) },
  }));

export const setIsRequested = () => updateDashboardState({ isRequested: true });
export const revokeRequest = () => updateDashboardState({ isRequested: false });

export const openRightSideMenu = () => updateDashboardLayout({ rightMenuOpen: true });
export const closeRightSideMenu = () => updateDashboardLayout({ rightMenuOpen: false });
export const toggleRightSideMenu = () =>
  updateDashboardLayout((s) => ({ rightMenuOpen: !s.rightMenuOpen }));

export const openLeftSideMenu = () => updateDashboardLayout({ leftMenuOpen: true });
export const closeLeftSideMenu = () => updateDashboardLayout({ leftMenuOpen: false });
export const toggleLeftSideMenu = () =>
  updateDashboardLayout((s) => ({ leftMenuOpen: !s.leftMenuOpen }));
