import { ComponentType, useEffect } from 'react';
import { updateDashboardLayout } from '../store/store';

// TODO: find a better way to handle the right sidebar
export const useRightSidebar = (content: ComponentType) => {
  useEffect(() => {
    updateDashboardLayout({ rightMenuContent: content });
    // Clean up on unmount
    return () => updateDashboardLayout({ rightMenuContent: undefined, rightMenuOpen: false });
  }, [content]);
};
