import { Box } from '@chakra-ui/react';
import { analyticsService } from '@modules/analytics/services';
import { Profile as ProfileComponent } from '@modules/user/components/Profile/Profile';
import { selectCurrentUser, useAuthStore } from '@waitroom/auth';
import { useMemo } from 'react';
import { Navigate } from 'react-router-dom';
import { routes } from '../../../../constants/routes';
import { defaultBodyPadding } from '../Layout/Default/constants';

const Profile = (): React.ReactElement | null => {
  const currentUser = useAuthStore(selectCurrentUser);

  useMemo(() => {
    analyticsService.page('PROFILE');
  }, []);

  if (!currentUser) return <Navigate to={routes.HOME} />;
  return (
    <Box {...defaultBodyPadding}>
      <ProfileComponent
        currentUser={currentUser}
        deleteRoute={routes.DASHBOARD.PROFILE.DELETE.route}
      />
    </Box>
  );
};

export default Profile;
