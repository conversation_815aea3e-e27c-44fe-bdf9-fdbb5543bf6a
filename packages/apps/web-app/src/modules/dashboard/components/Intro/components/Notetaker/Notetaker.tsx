import { routes } from '@/constants/routes';
import { Button, Flex, Image, Text } from '@chakra-ui/react';
import { PostHogFeatureFlagGate } from '@core/components/FeatureFlagGate/FeatureFlag';
import { PostHogFeatureFlag } from '@waitroom/models';
import { ReactElement } from 'react';
import { useTranslation } from 'react-i18next';
import { Link } from 'react-router-dom';
import { providers } from '@core/components/Integrations/Notetaker.constants';

export type NotetakerProps = {
  showButton?: boolean;
};

export const Notetaker = ({ showButton = true }: NotetakerProps): ReactElement | null => {
  const { t } = useTranslation();
  return (
    <PostHogFeatureFlagGate flag={PostHogFeatureFlag.RecallCalendarIntegration}>
      <Flex direction={'column'} gap={10} w={'full'}>
        <Flex align={'center'} gap={4}>
          <Text fontWeight={'bold'}>{t('global.supports')}:</Text>
          {Object.values(providers).map((support) => (
            <Image key={support} src={support} alt={support} w={`${48}px`} h={'auto'} />
          ))}
        </Flex>
        {showButton && (
          <Button
            as={Link}
            to={`${routes.DASHBOARD.INTEGRATIONS.link}#notetaker`}
            variant={'outline'}
            colorScheme={'gray.300'}
            color={'gray.900!'}
            w={'full'}
          >
            {t('dashboard.onboarding.intro.notetakerBtn')}
          </Button>
        )}
      </Flex>
    </PostHogFeatureFlagGate>
  );
};
