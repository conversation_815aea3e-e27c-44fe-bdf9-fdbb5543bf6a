import { analyticsService } from '@modules/analytics/services';
import { EventType } from '@waitroom/analytics';
import { useMeetingMemory, UseMeetingMemoryResponse } from '@waitroom/common';
import { MeetingMemory } from '@waitroom/models';
import { useCallback, useEffect } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import { routes } from '../../../../constants/routes';
import { useMeetingMemorySubmissionWithUrl } from '../../../core/hooks/useMeetingMemorySubmissionWithUrl';
import { useMemoizedFn } from 'ahooks';

export const useMeetingMemoryPage = (): Omit<
  UseMeetingMemoryResponse,
  'submitQuery' | 'onNewThread'
> & {
  onSubmit: (query: string, sessions?: MeetingMemory.AskAIRequestBodySessions) => void;
  onNewThread: () => void;
} => {
  const { id } = useParams<{ id?: string }>();
  const navigate = useNavigate();

  const meetingMemory = useMeetingMemory({
    threadId: id,
  });
  const {
    isNew,
    askMutation: { isPending },
  } = meetingMemory;

  const submitRef = useMemoizedFn(meetingMemory.submitQuery);
  const submitQuery = useCallback(
    (query: string, sessions?: MeetingMemory.AskAIRequestBodySessions) => {
      if (query) {
        // Determine if this is a new thread based on current state or logic
        analyticsService.track(
          isNew ? EventType.MeetingMemoryThreadCreated : EventType.MeetingMemoryThreadReply,
          {},
        );
        submitRef(query, isNew, sessions);
      }
    },
    [isNew, submitRef],
  );

  useMeetingMemorySubmissionWithUrl({
    submitFunction: meetingMemory.submitQuery,
  });

  useEffect(() => {
    analyticsService.page('MEETING_MEMORY');
  }, []);

  useEffect(() => {
    if (!!meetingMemory.threadId && !isPending) {
      analyticsService.track(EventType.MeetingMemoryThreadFetch, {});
    }
  }, [isPending, meetingMemory.threadId]);

  return {
    onSubmit: submitQuery,
    ...meetingMemory,
    onNewThread: () => {
      meetingMemory.onNewThread();
      navigate(routes.DASHBOARD.MEETING_MEMORY.link);
      analyticsService.track(EventType.MeetingMemoryThreadCreated, {});
    },
  };
};
