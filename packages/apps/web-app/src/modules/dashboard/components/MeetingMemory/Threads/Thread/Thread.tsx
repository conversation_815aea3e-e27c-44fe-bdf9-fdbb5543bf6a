import {
  Box,
  Flex,
  BoxProps as FlexProps,
  IconButton,
  SystemStyleObject,
  Text,
  useDisclosure,
} from '@chakra-ui/react';
import { Icon } from '@core/components/Icon/Icon';
import Confirm from '@core/components/Modal/Confirm/Confirm';
import { faTrashAlt } from '@fortawesome/pro-regular-svg-icons';
import { faArrowLeft } from '@fortawesome/pro-solid-svg-icons';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import { EventType } from '@waitroom/analytics';
import { selectCurrentUserId, useAuthStore } from '@waitroom/auth';
import { MeetingMemory } from '@waitroom/models';
import {
  meetingMemoryCacheService,
  meetingMemoryDeleteThreadMutation,
} from '@waitroom/react-query';
import { format, fromUnixTime } from 'date-fns';
import { ReactElement } from 'react';
import { useTranslation } from 'react-i18next';
import { generatePath } from 'react-router';
import { Link } from 'react-router-dom';
import { routes } from '../../../../../../constants/routes';
import { analyticsService } from '../../../../../analytics/services';

export type ThreadProps = {
  data: MeetingMemory.Thread;
  selected: boolean;
} & FlexProps;

const wrapperSx: SystemStyleObject = {
  _hover: {
    '.delete': {
      opacity: 1,
      ':hover': {
        color: 'red.800',
      },
    },
  },
};

const linkSx = (selected: boolean): SystemStyleObject => ({
  flexDirection: 'row',
  justifyContent: 'flex-start',
  gap: 3,
  fontWeight: 'bold',
  whiteSpace: 'normal',
  rounded: 'md',
  fontSize: 'sm',
  w: 'full',
  h: 'auto',
  color: 'gray.400',
  py: 2,
  pl: 1,
  pr: 6,
  transition: 'background-color 0.2s ease-out',
  [selected ? '&' : '_hover']: {
    bgColor: 'gray.800',
    svg: {
      color: 'red.500',
    },
  },
});

const Thread = ({ data, selected, ...rest }: ThreadProps): ReactElement | null => {
  const { t } = useTranslation('meetingMemory');
  const disclosure = useDisclosure();
  const client = useQueryClient();
  const userId = useAuthStore(selectCurrentUserId) as string;
  const { mutate, isPending } = useMutation(
    meetingMemoryDeleteThreadMutation({
      id: data.id,
      options: {
        onSuccess: () => {
          meetingMemoryCacheService.removeThread({
            client,
            id: data.id,
            userId,
          });
          analyticsService.track(EventType.MeetingMemoryThreadDelete, {});
        },
      },
    }),
  );
  return (
    <Box position={'relative'} sx={wrapperSx}>
      {disclosure && (
        <Confirm
          header={t('confirmDeleteThreadTitle')}
          body={t('confirmDeleteThreadDesc')}
          onConfirm={mutate}
          {...disclosure}
        />
      )}
      <Flex
        key={data.id}
        as={Link}
        className={'none'}
        to={generatePath(routes.DASHBOARD.MEETING_MEMORY.fullRoute, {
          id: data.id,
        })}
        sx={linkSx(selected)}
        title={data.title}
        {...rest}
      >
        <Icon icon={faArrowLeft} size={'lg'} pt={1} />
        <div>
          <Text as={'span'} color={'gray.200'} noOfLines={3}>
            {data.title}
          </Text>
          <Text as={'span'} fontSize={'xs'} fontWeight={'normal'}>
            ({format(fromUnixTime(data.updatedAt), 'MMM dd, yyyy - hh:mm a')})
          </Text>
        </div>
      </Flex>
      <IconButton
        onClick={disclosure.onOpen}
        className={'delete'}
        size={'2xs'}
        variant={'simple'}
        color={'gray.500'}
        rounded={'full'}
        aria-label={t('deleteThread')}
        isLoading={isPending}
        position={'absolute'}
        boxShadow={'none'}
        right={1}
        top={'50%'}
        transform={'translateY(-50%)'}
        zIndex={2}
        opacity={0}
      >
        <Icon icon={faTrashAlt} size={'lg'} />
      </IconButton>
    </Box>
  );
};
export default Thread;
