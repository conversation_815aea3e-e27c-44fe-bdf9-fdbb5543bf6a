import {
  Box,
  Center,
  Container,
  Flex,
  Spinner,
  SystemStyleObject,
  useBoolean,
} from '@chakra-ui/react';
import { spinnerProps } from '@core/components/Common/styles';
import ContentLoader from '@core/components/Loader/Content/ContentLoader';
import { Conversation } from '@modules/ai/components/MeetingMemory/Conversation/Conversation';
import MeetingMemoryError, {
  FetchError,
} from '@modules/ai/components/MeetingMemory/Error/MeetingMemoryError';
import {
  PromptContainer,
  PromptContainerProps,
} from '@modules/ai/components/MeetingMemory/PromptContainer/PromptContainer';
import { memo } from 'react';
import { useTranslation } from 'react-i18next';
import {
  SIDE_MENU_BREAKPOINT,
  SIDE_MENU_NARROW_BREAKPOINT,
  SIDE_MENU_NARROW_WIDTH,
  SIDE_MENU_WIDTH,
} from '../Layout/Default/LeftSide';
import ThreadsCard from './Threads/Card/ThreadsCard';
import { useMeetingMemoryPage } from './useMeetingMemory';

const fixedLayout: SystemStyleObject = {
  position: 'fixed',
  w: 'auto',
  bottom: 0,
  left: 0,
  right: 4,
  h: 'auto',
  zIndex: 'docked',
  pr: 4,
  pl: {
    base: 4,
    [SIDE_MENU_BREAKPOINT]: SIDE_MENU_NARROW_WIDTH + 16,
    [SIDE_MENU_NARROW_BREAKPOINT]: SIDE_MENU_WIDTH + 16,
  },
};
const defaultLayout = { h: 'full' };

const smallProps: Partial<PromptContainerProps> = {
  heading: false,
  spacing: 2,
  autoFocus: false,
  zIndex: 'sticky',
  suggestionsProps: {
    shownLimit: 0,
    hideOnSmall: true,
  },
};

const MeetingMemoryPage = memo(() => {
  const { t } = useTranslation('meetingMemory');
  const state = useMeetingMemoryPage();
  const [isOpen, { off, toggle }] = useBoolean();
  const areMessagesLoading = state.messagesQuery.isLoading && state.isQueryEnabled;
  const isFixed = state.conversationStarted || areMessagesLoading;
  const layoutSx = isFixed ? fixedLayout : defaultLayout;
  const maxW = { base: 'full', xl: isOpen ? 'container.xl' : 'container.lg' };

  return (
    <>
      <Container
        display={'flex'}
        flexDirection={{ base: 'column', xl: 'row' }}
        maxW={maxW}
        alignItems={isFixed ? 'flex-start' : 'center'}
        justifyContent={isFixed ? 'flex-start' : 'center'}
        pt={{ base: 4, lg: 12 }}
        pb={{ base: 36, lg: 40 }}
        px={{ base: 4, xl: 0 }}
        minH={{
          base: `calc(100vh - 50px)`,
        }}
      >
        {state.conversationStarted && (
          <ThreadsCard top={28} isOpen={isOpen} onClose={off} selected={state.threadId} />
        )}
        <Box
          w={'full'}
          h={'full'}
          maxW={{ xl: 'container.lg' }}
          position={'relative'}
          zIndex={'docked'}
          minW={0}
        >
          {areMessagesLoading && !state.conversationStarted && <ContentLoader avatar={false} />}
          {state.conversationStarted && (
            <>
              <Conversation
                cacheId={state.cacheId}
                threadId={state.threadId || ''}
                messages={state.messages}
                onNewThread={state.onNewThread}
                onPastThreads={toggle}
              />
              {state.messagesQuery.isError && (
                <FetchError
                  error={state.messagesQuery.error}
                  onRetry={state.messagesQuery.refetch}
                />
              )}
              {state.askMutation.isError && <MeetingMemoryError onRetry={state.onRetry} />}
              <Flex
                minH={'36px'}
                align={'center'}
                justify={'center'}
                gap={2}
                color={'gray.600'}
                fontSize={'sm'}
              >
                {state.messagesQuery.isRefetching && (
                  <>
                    <Spinner size={'sm'} sx={spinnerProps} />
                    {t('refreshing')}
                  </>
                )}
              </Flex>
            </>
          )}
          <Center h={'full'} w={'full'} mx={'auto'} sx={layoutSx}>
            <Box
              bgColor={'gray.900'}
              p={isFixed ? 4 : { base: 6, lg: 8 }}
              rounded={'2xl'}
              roundedBottom={isFixed ? 0 : '2xl'}
              w={'full'}
              maxW={maxW}
            >
              <PromptContainer
                onSubmit={state.onSubmit}
                pastThreads={!isFixed}
                disabled={state.askMutation.isPending || state.messagesQuery.isLoading}
                {...(isFixed ? smallProps : undefined)}
                placeholder={isFixed ? t('askFollowUp') : undefined}
                selectedThread={state.threadId}
              />
            </Box>
          </Center>
        </Box>
      </Container>
    </>
  );
});

export default MeetingMemoryPage;
