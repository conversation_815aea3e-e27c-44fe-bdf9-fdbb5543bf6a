import {
  Box,
  Card,
  Flex,
  FlexProps,
  Heading,
  IconButton,
  useBreakpointValue,
} from '@chakra-ui/react';
import { Icon } from '@core/components/Icon/Icon';
import { faRectangleVerticalHistory } from '@fortawesome/pro-regular-svg-icons';
import { faClose } from '@fortawesome/pro-solid-svg-icons';
import CardLoader from '@modules/core/components/Loader/Card/CardLoader';
import Modal from '@modules/core/components/Modal/Modal';
import { MeetingMemory } from '@waitroom/models';
import { getRequestData } from '@waitroom/react-query';
import { ReactElement } from 'react';
import { useTranslation } from 'react-i18next';
import { ThreadsList } from '../Threads';
import { useThreads } from '../useThreads';

const SIDEBAR_WIDTH = 400;
const bpValue = { base: true, xl: false };

export type ThreadsCardProps = {
  isOpen: boolean;
  selected?: MeetingMemory.Thread['id'];
  onClose: () => void;
} & FlexProps;

const ThreadsCard = ({
  isOpen,
  onClose,
  selected,
  ...rest
}: ThreadsCardProps): ReactElement | null => {
  const { t } = useTranslation('meetingMemory');
  const isSmall = useBreakpointValue(bpValue);
  const { data, isLoading } = useThreads({ params: { limit: 30 } });
  const { threads } = getRequestData(data) || {};

  if (isLoading && !isSmall && isOpen) {
    return (
      <Flex align={'center'} w={{ base: 'full', xl: SIDEBAR_WIDTH }} flexShrink={0} pr={6}>
        <CardLoader w={'full'} header lines={5} />
      </Flex>
    );
  }
  const contentUi = (
    <>
      <Heading as={'h4'} size={'xl'} mb={4}>
        <Icon icon={faRectangleVerticalHistory} mr={3} />
        {t('pastThreads')}
      </Heading>
      <Box maxH={'380px'} overflow={'auto'}>
        <ThreadsList data={threads} selected={selected} onClick={onClose} />
      </Box>
    </>
  );
  return isSmall ? (
    <Modal isOpen={isOpen} onClose={onClose} isCentered>
      <Modal.Header />
      <Modal.Body my={2}>{contentUi}</Modal.Body>
    </Modal>
  ) : isOpen ? (
    <Flex
      direction={'column'}
      align={'center'}
      justify={'center'}
      w={SIDEBAR_WIDTH}
      flexShrink={0}
      maxW={'container.lg'}
      pr={{ xl: 6 }}
      position={'sticky'}
      top={4}
      zIndex={2}
      {...rest}
    >
      <Card rounded="xl" bgColor={'gray.900'} w={'full'} py={5} pl={4} pr={3}>
        <IconButton
          pos="absolute"
          top={2}
          right={2}
          colorScheme="white"
          onClick={onClose}
          size="2xs"
          zIndex={2}
          aria-label="Close"
          boxShadow={'none'}
        >
          <Icon icon={faClose} size={'lg'} />
        </IconButton>
        {contentUi}
      </Card>
    </Flex>
  ) : null;
};
export default ThreadsCard;
