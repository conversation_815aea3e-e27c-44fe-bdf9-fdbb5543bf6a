import { AvatarGroup, Button, Flex } from '@chakra-ui/react';
import { Icon } from '@core/components/Icon/Icon';
import { faArrowRight } from '@fortawesome/pro-solid-svg-icons';
import { LobbyParticipant } from '@waitroom/models';
import { ReactElement } from 'react';
import { useTranslation } from 'react-i18next';
import { Link } from 'react-router-dom';
import { routes } from '../../../../../../constants/routes';
import UserAvatar from '../../../../../user/components/Avatar/User/UserAvatar';

const countAvatarSx = {
  '>span': {
    bgColor: 'gray.800',
    color: 'white',
    border: '2px solid',
  },
};
type LobbyCardUsersProps = {
  users: LobbyParticipant[];
};

const LobbyCardUsers = ({ users }: LobbyCardUsersProps): ReactElement | null => {
  const { t } = useTranslation();

  return (
    <Flex align={'center'} gap={3}>
      <AvatarGroup size="2xs" spacing={-1.5} borderColor={'gray.50'} max={3} sx={countAvatarSx}>
        {users.map((user) => (
          <UserAvatar key={user.id} size="2xs" user={user} />
        ))}
      </AvatarGroup>
      <Button
        as={Link}
        to={routes.DASHBOARD.LOBBY.link}
        variant={'link'}
        color={'red.800'}
        size={'sm'}
        rightIcon={<Icon icon={faArrowRight} />}
        textDecoration={'underline'}
      >
        {t(`dashboard.lobby.card.lobbyLink`)}
      </Button>
    </Flex>
  );
};
export default LobbyCardUsers;
