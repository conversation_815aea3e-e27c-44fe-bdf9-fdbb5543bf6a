import { routes } from '@/constants/routes';
import {
  Box,
  Button,
  Card,
  CardProps,
  Link as CLink,
  Flex,
  Heading,
  Spinner,
  Switch,
  Text,
  Tooltip,
} from '@chakra-ui/react';
import { ModalType } from '@core/components/App/Modals/types';
import { stripesSx } from '@core/components/Common/styles';
import ShareLink from '@core/components/FormControl/ShareLink/ShareLink';
import { Icon, IconBox } from '@core/components/Icon/Icon';
import { useLobby } from '@core/hooks/useLobby';
import { domainUrl, fullUrl } from '@core/utils/url';
import {
  faArrowUpRightFromSquare,
  faInfo,
  faPencil,
  faShare,
} from '@fortawesome/pro-solid-svg-icons';
import { setModal, useLobbyParticipants } from '@waitroom/common';
import { config } from '@waitroom/config';
import { LobbyBasic } from '@waitroom/models';
import { ReactElement, ReactNode } from 'react';
import { Trans, useTranslation } from 'react-i18next';
import { generatePath } from 'react-router-dom';
import LobbyCardUsers from './Users/<USER>';

export type LobbyCardProps = {
  heading?: ReactNode;
  body?: ReactNode;
  showUsers?: boolean;
  editText?: boolean;
  toggleable?: boolean;
  isToggling?: boolean;
  lobby: LobbyBasic;
} & CardProps;

export const ns = 'dashboard.lobby.card';

const LobbyCard = ({
  showUsers = true,
  heading,
  body,
  editText,
  toggleable,
  lobby,
  ...rest
}: LobbyCardProps): ReactElement | null => {
  const { t } = useTranslation();
  const participants = useLobbyParticipants(lobby.lobbyID, lobby.slug);
  const isDisabled = !lobby.isActive;

  const { toggleLobby, isPendingToggle } = useLobby();

  const lobbyLink = fullUrl(
    generatePath(routes.LOBBY.route, {
      slug: lobby.slug,
    }),
  );
  const lobbyLinkText = (
    <Box whiteSpace="wrap">
      {domainUrl(routes.LOBBY.link)}/<wbr />
      <Text as={'strong'} fontWeight="bold">
        {lobby.slug || t('global.lobbyIdPlaceholder')}
      </Text>
    </Box>
  );

  return (
    <Card
      w={['auto', 'full']}
      px={[4, 5, 6]}
      py={[5, 6, 7]}
      mx={[-4, 0]}
      gap={3}
      bgColor={'gray.900'}
      rounded={['none', 'xl']}
      boxShadow={'none'}
      {...rest}
    >
      <Heading
        display={'flex'}
        size={'lg'}
        alignItems={'center'}
        gap={2}
        flexWrap={'wrap'}
        fontWeight={'bold'}
      >
        {heading || (
          <>
            <IconBox size={'2xs'} bg={'gray.800'} rounded={'full'}>
              <Icon icon={faShare} color={'white'} fontSize={'xl'} />
            </IconBox>
            {t(`${ns}.heading`)}
          </>
        )}
        <Button
          size={'2xs'}
          variant={'ghost'}
          onClick={() => {
            setModal({
              type: ModalType.LOBBY_SLUG_FORM,
              props: lobby,
            });
          }}
          color={'gray.500'}
          data-testid="lobby-card-btn-edit"
          px="auto"
        >
          <Icon icon={faPencil} />
          {!!editText && (
            <Text as={'span'} ml={2} textDecoration={'underline'}>
              {t(`${ns}.editText`)}
            </Text>
          )}
        </Button>
      </Heading>
      <Text>{body || t(`${ns}.desc`)}</Text>
      {showUsers && participants.length > 0 ? <LobbyCardUsers users={participants} /> : <></>}
      <Box mt={2} position={'relative'}>
        <ShareLink
          size={'sm'}
          fontSize={'md'}
          color={'white'}
          borderColor={'gray.500'}
          link={lobbyLink}
          text={lobbyLinkText}
          buttonText={t('global.copy')}
          buttonProps={{ colorScheme: 'red.200' }}
          multiline
          disabled={isDisabled}
          pr={2}
          py={1}
        >
          {isDisabled && (
            <Tooltip label={t(`${ns}.lobbyDisabled`)} placement={'top'}>
              <Box
                data-testid="lobby-card-disabled"
                layerStyle={'overlay'}
                sx={stripesSx()}
                zIndex={2}
              />
            </Tooltip>
          )}
        </ShareLink>
      </Box>
      <Flex fontSize={'sm'} fontWeight={'bold'} gap={2} align={'center'} color={'gray.500'}>
        <Icon icon={faArrowUpRightFromSquare} size={'sm'} />
        <p>
          <Trans
            i18nKey={`${ns}.calendarLinks`}
            components={{
              calendly: (
                <CLink
                  isExternal
                  href={config.links.knowledgebase.useWithCalendly}
                  className={'u'}
                />
              ),
            }}
          />
        </p>
      </Flex>
      {toggleable && (
        <Flex flexWrap={'wrap'} gap={2} align={'center'} justify={'space-between'}>
          <Text fontSize={'sm'}>{t(`${ns}.toggleLobby`)}</Text>
          <Flex gap={2} align={'center'}>
            {isPendingToggle ? (
              <Spinner size={'sm'} my={1.5} />
            ) : (
              <Switch
                data-testid="lobby-card-toggle"
                size={'xl'}
                colorScheme={'green'}
                defaultChecked={!isDisabled}
                isChecked={!isDisabled}
                onChange={(ev) => toggleLobby(ev.currentTarget.checked, lobby.slug, lobby.lobbyID)}
              />
            )}
            <Tooltip
              label={t(
                `${ns}.${isDisabled ? 'toggleLobbyTooltipDisabled' : 'toggleLobbyTooltipEnabled'}`,
              )}
            >
              <div>
                <IconBox rounded={'full'} size={'5xs'} bg={'gray.300'} color={'white'}>
                  <Icon icon={faInfo} fontSize={'xs'} />
                </IconBox>
              </div>
            </Tooltip>
          </Flex>
        </Flex>
      )}
    </Card>
  );
};
export default LobbyCard;
