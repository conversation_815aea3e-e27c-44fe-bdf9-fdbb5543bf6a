import { Box, Button, Container, Flex, Heading, Text } from '@chakra-ui/react';
import { Icon } from '@core/components/Icon/Icon';
import { useLobby } from '@core/hooks/useLobby';
import { faChevronRight } from '@fortawesome/pro-solid-svg-icons';
import { selectCurrentUserLobby, useAuthStore } from '@waitroom/auth';
import { useLobbyParticipants } from '@waitroom/common';
import { ReactElement, useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import { defaultBodyPadding } from '../Layout/Default/constants';
import LobbyCard from './LobbyCard/LobbyCard';
import { useLobbySession } from './useLobbySession';
import UsersForm from './UsersForm/UsersForm';

const ns = 'dashboard.lobby';
const breakpoint = 'lg';

const Lobby = (): ReactElement | null => {
  const { t } = useTranslation();
  const lobby = useAuthStore(selectCurrentUserLobby);
  const participants = useLobbyParticipants(lobby?.lobbyID || '', lobby?.slug || '');

  const session = useLobbySession();
  const initialValues = useMemo(
    () => ({
      participants: participants.map((user) => user.participantID),
      session,
      lobbyID: lobby?.lobbyID || '',
    }),
    [lobby?.lobbyID, participants, session],
  );
  const { startLobbyMutation, toggleLobby, isPendingToggle } = useLobby();

  const onMeetingStart = () => {
    startLobbyMutation.mutate(initialValues);
  };

  return (
    <Container maxW={'container.xl'} {...defaultBodyPadding}>
      <Heading as={'h1'} size={'4xl'} mb={[6, 8, 10]}>
        {t('dashboard.yourLobby')}
      </Heading>
      <Flex
        display={'flex'}
        flexDirection={{ base: 'column', [breakpoint]: 'row' }}
        gap={[10, 10, 12]}
      >
        <Box w={{ [breakpoint]: '350px' }} flexShrink={0}>
          {lobby && <LobbyCard lobby={lobby} showUsers={false} toggleable />}
        </Box>
        <Box flexGrow={1}>
          {!lobby?.isActive ? (
            <Flex direction={'column'} gap={8}>
              <Heading as={'h2'} size={'2xl'}>
                {t(`${ns}.disabledHeading`)}
              </Heading>
              <Text>{t(`${ns}.disabledDesc`)}</Text>
              <div>
                <Button
                  type="button"
                  colorScheme={'red'}
                  size={['sm', 'sm', 'def']}
                  rightIcon={<Icon icon={faChevronRight} />}
                  isLoading={isPendingToggle}
                  onClick={() => {
                    if (lobby) toggleLobby(true, lobby.slug, lobby.lobbyID);
                  }}
                >
                  {t(`dashboard.lobby.enableBtn`)}
                </Button>
              </div>
            </Flex>
          ) : participants.length ? (
            <Flex direction={'column'} gap={8}>
              <Heading as={'h2'} size={'2xl'}>
                {t(`${ns}.usersHeading`)}
              </Heading>
              <div>
                <UsersForm users={participants} initialValues={initialValues} lobby={lobby} />
              </div>
            </Flex>
          ) : (
            <>
              <Heading as={'h2'} size={'2xl'} mb={10}>
                {t(`${ns}.noUsersHeading`)}
              </Heading>
              <Button
                colorScheme={'red'}
                size={['sm', 'sm', 'def']}
                w={{ base: 'full', md: 'auto' }}
                rightIcon={<Icon icon={faChevronRight} />}
                onClick={onMeetingStart}
                isLoading={startLobbyMutation.isPending}
              >
                {t(`${ns}.noUsersBtn`)}
              </Button>
            </>
          )}
        </Box>
      </Flex>
    </Container>
  );
};
export default Lobby;
