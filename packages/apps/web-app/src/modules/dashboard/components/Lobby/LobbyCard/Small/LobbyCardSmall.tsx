import { routes } from '@/constants/routes';
import { Box, Button, Link as CLink, Flex, Heading, Text, Tooltip } from '@chakra-ui/react';
import { domainUrl, fullUrl } from '@core/utils/url';
import { faShare } from '@fortawesome/pro-regular-svg-icons';
import { faArrowUpRightFromSquare, faPencil } from '@fortawesome/pro-solid-svg-icons';
import { setModal } from '@waitroom/common';
import { config } from '@waitroom/config';
import { LobbyBasic } from '@waitroom/models';
import { ReactElement, ReactNode } from 'react';
import { Trans, useTranslation } from 'react-i18next';
import { generatePath } from 'react-router';
import { ModalType } from '../../../../../core/components/App/Modals/types';
import { stripesSx } from '../../../../../core/components/Common/styles';
import ShareLink from '../../../../../core/components/FormControl/ShareLink/ShareLink';
import { Icon, IconBox } from '../../../../../core/components/Icon/Icon';

export type LobbyCardSmallProps = {
  lobby: LobbyBasic;
  subtext?: ReactNode;
};

const ns = 'dashboard.lobby.card';

// TODO: reuse components from parent
const LobbyCardSmall = ({ lobby, subtext }: LobbyCardSmallProps): ReactElement | null => {
  const { t } = useTranslation();
  const isDisabled = !lobby.isActive;

  const lobbyLink = fullUrl(
    generatePath(routes.LOBBY.route, {
      slug: lobby.slug,
    }),
  );
  const lobbyLinkText = (
    <Box whiteSpace="wrap">
      {domainUrl(routes.LOBBY.link)}/<wbr />
      <Text as={'strong'} color={'gray.900'} _dark={{ color: 'white' }} fontWeight="bold">
        {lobby.slug || t('global.lobbyIdPlaceholder')}
      </Text>
    </Box>
  );

  return (
    <Flex gap={3} direction={'column'}>
      <Heading
        display={'flex'}
        size={'md'}
        alignItems={'center'}
        flexWrap={'wrap'}
        fontWeight={'bold'}
        color={'white'}
        gap={2}
      >
        <IconBox size={'3xs'} rounded={'full'} color={'gray.500'}>
          <Icon icon={faShare} size="lg" />
        </IconBox>
        {t(`${ns}.heading`)}
        <Button
          size={'3xs'}
          variant={'ghost'}
          onClick={() => {
            setModal({
              type: ModalType.LOBBY_SLUG_FORM,
              props: lobby,
            });
          }}
          color={'gray.500'}
          px="auto"
        >
          <Icon icon={faPencil} />
        </Button>
      </Heading>
      {subtext}
      <Box position={'relative'}>
        <ShareLink
          size={'2xs'}
          fontSize={'sm'}
          color={'white'}
          borderColor={'gray.500'}
          link={lobbyLink}
          text={lobbyLinkText}
          buttonText={null}
          buttonProps={{ colorScheme: 'red.200', minW: 0, px: 3 }}
          multiline
          disabled={isDisabled}
          pr={2}
          py={1}
        >
          {isDisabled && (
            <Tooltip label={t(`${ns}.lobbyDisabled`)} placement={'top'}>
              <Box
                data-testid="lobby-card-disabled"
                layerStyle={'overlay'}
                sx={stripesSx()}
                zIndex={2}
              />
            </Tooltip>
          )}
        </ShareLink>
      </Box>
      <Flex fontSize={'xs'} fontWeight={'bold'} gap={2} align={'center'} color={'gray.500'}>
        <Icon icon={faArrowUpRightFromSquare} size={'sm'} />
        <p>
          <Trans
            i18nKey={`${ns}.calendarLinks`}
            components={{
              calendly: (
                <CLink
                  isExternal
                  href={config.links.knowledgebase.useWithCalendly}
                  className={'u'}
                />
              ),
            }}
          />
        </p>
      </Flex>
    </Flex>
  );
};
export default LobbyCardSmall;
