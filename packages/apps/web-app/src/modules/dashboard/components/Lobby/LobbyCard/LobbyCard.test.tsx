import { usePostHogFeatureFlag } from '@core/components/FeatureFlagGate/FeatureFlag';
import { render } from '@testing-library/react';
import { useLobbyParticipants } from '@waitroom/common';
import { faker } from '@waitroom/tests';
import { beforeEach, describe, expect, it, vi } from 'vitest';
import { withAll } from '../../../../../tests/helpers/storeProviders';
import LobbyCard from './LobbyCard';

// Mock required dependencies
vi.mock('react-i18next', async (importOriginal) => {
  const mod = await importOriginal<typeof import('react-i18next')>();
  return {
    ...mod,
    useTranslation: () => ({
      t: (key: string) => key,
    }),
    Trans: ({ i18nKey }: { i18nKey: string }) => i18nKey,
  };
});

vi.mock('react-router-dom', async (importOriginal) => {
  const mod = await importOriginal<typeof import('react-router-dom')>();
  return {
    ...mod,
    generatePath: vi.fn((path, params) => `${path}/${params.id}`),
  };
});

vi.mock('@core/utils/url', async (importOriginal) => {
  const mod = await importOriginal<typeof import('@core/utils/url')>();
  return {
    ...mod,
    domainUrl: (path: string) => `https://example.com${path}`,
  };
});

vi.mock('@core/components/FeatureFlagGate/FeatureFlag', async (importOriginal) => {
  const mod = await importOriginal<typeof import('@core/components/FeatureFlagGate/FeatureFlag')>();
  return {
    ...mod,
    usePostHogFeatureFlag: vi.fn(),
  };
});

vi.mock('./Users/<USER>', async (importOriginal) => {
  const mod = await importOriginal<typeof import('./Users/<USER>')>();
  return {
    ...mod,
    default: () => <div data-testid="lobby-card-users">Users Component</div>,
  };
});

vi.mock('@waitroom/common', async (importOriginal) => {
  const mod = await importOriginal<typeof import('@waitroom/common')>();
  return {
    ...mod,
    useLobbyParticipants: vi.fn(() =>
      Array.from({ length: 5 }, () => faker.user.summaryWithEmail()),
    ),
  };
});

const lobbyEnterData = (noParticipants = 0) =>
  Array.from({ length: noParticipants }, () => faker.lobby.participant());

const activeLobby = faker.lobby.basic({ isActive: true });

describe('<LobbyCard>', () => {
  beforeEach(() => {
    vi.clearAllMocks();
    vi.mocked(usePostHogFeatureFlag).mockReturnValue(true);
  });

  it('enders with default props', () => {
    const lobby = activeLobby;
    vi.mocked(useLobbyParticipants).mockImplementation(
      () => lobbyEnterData(2) as ReturnType<typeof useLobbyParticipants>,
    );
    const { getByText, getByTestId, queryByTestId } = render(withAll(<LobbyCard lobby={lobby} />));

    expect(getByText('dashboard.lobby.card.heading')).toBeInTheDocument();
    expect(getByText('dashboard.lobby.card.desc')).toBeInTheDocument();
    expect(getByTestId('lobby-card-users')).toBeInTheDocument();
    expect(queryByTestId('lobby-card-disabled')).not.toBeInTheDocument();
  });

  it('renders with custom heading and body', () => {
    const customHeading = 'Custom Heading';
    const customBody = 'Custom Body';
    const lobby = activeLobby;

    const { getByText } = render(
      withAll(<LobbyCard heading={customHeading} body={customBody} lobby={lobby} />),
    );

    expect(getByText(customHeading)).toBeInTheDocument();
    expect(getByText(customBody)).toBeInTheDocument();
  });

  it('does not show users when showUsers is false', () => {
    const lobby = activeLobby;

    const { queryByTestId } = render(withAll(<LobbyCard showUsers={false} lobby={lobby} />));

    expect(queryByTestId('lobby-card-users')).not.toBeInTheDocument();
  });

  it('does not show users when showUsers is true but no members', () => {
    const lobby = activeLobby;
    vi.mocked(useLobbyParticipants).mockImplementation(
      () => lobbyEnterData() as ReturnType<typeof useLobbyParticipants>,
    );

    const { queryByTestId } = render(withAll(<LobbyCard showUsers lobby={lobby} />));

    expect(queryByTestId('lobby-card-users')).not.toBeInTheDocument();
  });
  it('does not show users when showUsers is false', () => {
    const lobby = activeLobby;

    const { queryByTestId } = render(withAll(<LobbyCard showUsers={false} lobby={lobby} />));

    expect(queryByTestId('lobby-card-users')).not.toBeInTheDocument();
  });

  it('renders edit text when editText prop is true', () => {
    const lobby = activeLobby;

    const { getByText } = render(withAll(<LobbyCard editText={true} lobby={lobby} />));

    expect(getByText('dashboard.lobby.card.editText')).toBeInTheDocument();
  });

  it('renders toggle lobby switch when toggleable prop is provided', () => {
    const lobby = activeLobby;

    const { getByTestId } = render(withAll(<LobbyCard toggleable lobby={lobby} />));

    const switchElement = getByTestId('lobby-card-toggle');
    expect(switchElement).toBeInTheDocument();
  });

  it('renders share link with correct URL', () => {
    const lobby = activeLobby;

    const { getByText } = render(withAll(<LobbyCard lobby={lobby} />));

    expect(getByText('global.copy')).toBeInTheDocument();
  });

  it('opens modal when edit button is clicked', () => {
    const lobby = activeLobby;

    const { getByTestId } = render(withAll(<LobbyCard lobby={lobby} />));

    const editButton = getByTestId('lobby-card-btn-edit');
    expect(editButton).toBeInTheDocument();
  });

  it('renders calendar links section', () => {
    const lobby = activeLobby;

    const { getByText } = render(withAll(<LobbyCard lobby={lobby} />));

    expect(getByText('dashboard.lobby.card.calendarLinks')).toBeInTheDocument();
  });
});
