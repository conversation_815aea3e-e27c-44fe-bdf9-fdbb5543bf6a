import { Box } from '@chakra-ui/react';
import {
  UserBasic,
  userOnboarding,
  userOnboardingChecklistValues,
  UserOnboardingKeys,
} from '@waitroom/models';
import { ComponentType, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { Accordion } from './Accordion';
import { ns } from './config';
import ConnectCalendar from './ConnectCalendar';
import Notetaker from './Notetaker';
import TryMeetingMemory from './TryMeetingMemory';
import { DefaultComponentProps } from './types';
import WatchDemo from './WatchDemo';

const checklistComponents: Partial<
  Record<UserOnboardingKeys, ComponentType<DefaultComponentProps>>
> = {
  [userOnboarding.connectCalendar]: ConnectCalendar,
  [userOnboarding.watchDemo]: WatchDemo,
  [userOnboarding.noteTaker]: Notetaker,
  [userOnboarding.tryMeetingMemory]: TryMeetingMemory,
};

export type ChecklistProps = {
  isLoading: Partial<UserBasic['onboarding']>;
  checklist: typeof userOnboardingChecklistValues;
  userChecklist: UserBasic['onboarding'];
} & DefaultComponentProps;

export const Checklist = ({
  onCheck,
  isLoading,
  checklist,
  userChecklist,
  popover,
  modal,
}: ChecklistProps) => {
  const { t } = useTranslation();
  const [defaultIndex] = useState(
    () => checklist.findIndex((val) => !userChecklist?.[val]) ?? undefined,
  );

  return (
    <Box px={[0, 0, 2]} py={2}>
      <Accordion.Container allowToggle borderColor="transparent" defaultIndex={defaultIndex}>
        {checklist.map((key) => {
          const Component = checklistComponents[key];
          if (!Component) return null;
          return (
            <Accordion.Item key={key}>
              <Accordion.Button
                title={t(`${ns}${key}`)}
                isChecked={!!userChecklist?.[key]}
                onCheck={(val) => onCheck({ [key]: val })}
                isLoading={!!isLoading?.[key]}
              />
              <Accordion.Panel>
                <Component popover={popover} modal={modal} onCheck={onCheck} />
              </Accordion.Panel>
            </Accordion.Item>
          );
        })}
      </Accordion.Container>
    </Box>
  );
};
