import { Box, VStack } from '@chakra-ui/react';
import { CalendarButtons } from '@core/components/Button/Calendar/CalendarButtons';
import { userOnboarding } from '@waitroom/models';
import { ReactElement } from 'react';
import { useTranslation } from 'react-i18next';
import { checklistVideos, ns } from '../config';

const key = userOnboarding.connectCalendar;
const ConnectCalendar = (): ReactElement | null => {
  const { t } = useTranslation();
  return (
    <VStack gap={3}>
      <p>{t(`${ns}${key}-desc`)}</p>
      <Box
        as="video"
        w="full"
        h="auto"
        rounded="2xl"
        src={checklistVideos[key]}
        playsInline
        autoPlay
        muted
        loop
      />
      <CalendarButtons mt={3} gap={2} size={'sm'} />
    </VStack>
  );
};
export default ConnectCalendar;
