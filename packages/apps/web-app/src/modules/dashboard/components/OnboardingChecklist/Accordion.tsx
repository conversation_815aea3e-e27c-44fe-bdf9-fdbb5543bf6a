import {
  AccordionButton,
  AccordionIcon,
  AccordionItem,
  AccordionPanel,
  Accordion as CA<PERSON><PERSON>on,
  Checkbox,
  <PERSON>lex,
  Spinner,
} from '@chakra-ui/react';
import { ReactNode } from 'react';

type AccordionButtonProps = {
  title: string;
  isChecked: boolean;
  onCheck: (checked: boolean) => void;
  isLoading?: boolean;
};

const Button = ({ title, isChecked, onCheck, isLoading }: AccordionButtonProps) => {
  return (
    <h2>
      <AccordionButton px={4} color={'gray.100'} _expanded={{ color: 'gray.200' }}>
        <Flex
          flex="1"
          textAlign="left"
          fontWeight={'semibold'}
          fontSize={['md', 'lg']}
          lineHeight={'short'}
          gap={4}
          alignItems="center"
        >
          {isLoading ? (
            <Spinner size="sm" w={5} h={5} />
          ) : (
            <Checkbox
              isChecked={isChecked}
              colorScheme="green"
              variant="simple"
              onChange={(e) => {
                onCheck(e.target.checked);
              }}
            />
          )}
          <p>{title}</p>
        </Flex>
        <AccordionIcon viewBox="5 6 13 10" w={4} h={4} color="gray.100" />
      </AccordionButton>
    </h2>
  );
};

const Panel = ({ children }: { children: ReactNode }) => (
  <AccordionPanel ml={[0, 9]} pt={0} fontWeight={'initial'} fontSize={'sm'}>
    {children}
  </AccordionPanel>
);

const Item = ({ children }: { children: ReactNode }) => (
  <AccordionItem my={2}>{children}</AccordionItem>
);

export const Accordion = {
  Container: CAccordion,
  Button,
  Panel,
  Item,
};
