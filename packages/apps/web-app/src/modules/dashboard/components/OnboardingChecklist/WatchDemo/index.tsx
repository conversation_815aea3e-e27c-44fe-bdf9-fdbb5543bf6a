import { Box, Button, Center, Image, VStack } from '@chakra-ui/react';
import { CDN_IMAGES_URL } from '@core/config';
import { faPlay } from '@fortawesome/pro-solid-svg-icons';
import { Icon } from '@modules/core/components/Icon/Icon';
import { userOnboarding } from '@waitroom/models';
import { useTranslation } from 'react-i18next';
import { ns } from '../config';
import { DefaultComponentProps } from '../types';

const key = userOnboarding.watchDemo;
const WatchDemo = ({ modal }: DefaultComponentProps) => {
  const { t } = useTranslation();

  return (
    <VStack gap={3}>
      <p>{t(`${ns}${key}-desc`)}</p>
      <Box
        position="relative"
        onClick={() => {
          modal.onOpen();
        }}
        cursor="pointer"
        rounded="xl"
        overflow="hidden"
      >
        <Center position="absolute" top={0} left={0} right={0} bottom={0} bgColor="t.gray-800-50">
          <Icon icon={faPlay} color="white" w={16} h="auto" />
        </Center>
        <Image src={`${CDN_IMAGES_URL}/start-a-meeting.jpg`} w="full" h="auto" />
      </Box>
      <Button
        size="sm"
        w="full"
        colorScheme="green"
        leftIcon={<Icon icon={faPlay} size={'lg'} />}
        onClick={() => {
          modal.onOpen();
        }}
      >
        {t(`${ns}${key}-btn`)}
      </Button>
    </VStack>
  );
};

export default WatchDemo;
