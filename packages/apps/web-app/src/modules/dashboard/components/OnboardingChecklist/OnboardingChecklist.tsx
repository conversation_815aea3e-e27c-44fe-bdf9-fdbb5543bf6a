import {
  Badge,
  Box,
  BoxProps,
  Button,
  ButtonProps,
  Center,
  Flex,
  Heading,
  Modal,
  ModalBody,
  ModalCloseButton,
  ModalContent,
  ModalOverlay,
  Popover,
  PopoverCloseButton,
  PopoverContent,
  PopoverTrigger,
  Progress,
  Text,
  VStack,
} from '@chakra-ui/react';
import { CDN_IMAGES_URL } from '@core/config';
import { faRocket } from '@fortawesome/pro-solid-svg-icons';
import { Icon } from '@modules/core/components/Icon/Icon';
import { userOnboarding } from '@waitroom/models';
import ConfettiExplosion from 'react-confetti-explosion';
import { useTranslation } from 'react-i18next';
import { SIDE_MENU_BREAKPOINT, SIDE_MENU_NARROW_BREAKPOINT } from '../Layout/Default/LeftSide';
import { Checklist } from './Checklist';
import { checklistVideos } from './config';
import { useComponent } from './useComponent';

const ns = 'dashboard.onboarding.checklist.';

export type OnboardingChecklistProps = BoxProps & {
  buttonProps?: ButtonProps;
};

export const OnboardingChecklist = ({ buttonProps, ...props }: OnboardingChecklistProps) => {
  const { t } = useTranslation();
  const {
    popover,
    modal,
    onCheck,
    progress,
    isLoading,
    showChecklist,
    uncompletedCount,
    isCompleted,
    checklist,
    userChecklist,
    confetti,
  } = useComponent();

  if (isCompleted) {
    return (
      <Box {...props}>
        {confetti && (
          <ConfettiExplosion
            duration={4000}
            force={0.6}
            particleCount={500}
            width={3000}
            zIndex={100000}
            colors={['#FFAA64', '#FF6464', '#FFF5A5', '#FF8264']}
          />
        )}
      </Box>
    );
  }
  return (
    <Flex w={'full'} align={'center'} justify={'center'} {...props}>
      <Popover
        isOpen={popover.isOpen}
        onClose={popover.onClose}
        strategy={'fixed'}
        placement="right-start"
        closeOnEsc
        closeOnBlur={false}
        gutter={8}
      >
        <PopoverTrigger>
          <Button
            colorScheme="gray.200"
            w={{
              base: 'full',
              [SIDE_MENU_BREAKPOINT]: '40px',
              [SIDE_MENU_NARROW_BREAKPOINT]: 'full',
            }}
            minW={0}
            size={'xs'}
            shadow={'none'}
            onClick={popover.onToggle}
            lineHeight="shorter"
            whiteSpace="normal"
            gap={3}
            px={0}
            {...buttonProps}
          >
            <Icon icon={faRocket} size={'lg'} />
            <Text
              as={'span'}
              display={{
                [SIDE_MENU_BREAKPOINT]: 'none',
                [SIDE_MENU_NARROW_BREAKPOINT]: 'inline-flex',
              }}
            >
              {t(`${ns}gettingStarted`)}
            </Text>
            {uncompletedCount > 0 && (
              <Badge
                colorScheme={'red'}
                bgColor={'red.700'}
                color={'white'}
                fontSize={{ base: 'xs', lg: 'sm' }}
                fontWeight={'bold'}
                rounded={'full'}
                w={{ base: 5, lg: 6 }}
                h={{ base: 5, lg: 6 }}
                textAlign={'center'}
              >
                <Center h={'full'} w={'full'}>
                  {uncompletedCount}
                </Center>
              </Badge>
            )}
          </Button>
        </PopoverTrigger>
        <PopoverContent
          rounded={'2xl'}
          overflow={'auto'}
          minW={'280px'}
          w={{ base: '90vw', sm: '360px', md: '410px' }}
          maxH={'calc(100vh - 200px)'}
          minH={'280px'}
          zIndex={'auto'}
          shadow={'2xl'}
          bg={'gray.800'}
          color={'white'}
        >
          <PopoverCloseButton rounded="full" />
          <VStack
            bgColor={'green.50'}
            px={[4, 6]}
            py={[3, 4]}
            alignItems={'start'}
            gap={2}
            color={'gray.900'}
          >
            <Heading as={'h4'} size={'xl'} fontWeight={'extrabold'}>
              {t(`${ns}title`)}
            </Heading>
            <Text fontWeight={'normal'} fontSize={'sm'} lineHeight={'shorter'}>
              {t(`${ns}description`)}
            </Text>
            <Progress
              colorScheme="green"
              bgColor="blackAlpha.300"
              rounded="full"
              size="sm"
              value={progress}
              w="full"
            />
          </VStack>
          <Checklist
            isLoading={isLoading}
            checklist={checklist}
            userChecklist={userChecklist}
            onCheck={onCheck}
            modal={modal}
            popover={popover}
          />
        </PopoverContent>
      </Popover>
      {showChecklist && popover.isOpen && (
        <Box
          w="100%"
          h="100vh"
          zIndex="1"
          position="fixed"
          left={0}
          top={0}
          right={0}
          onClick={popover.onClose}
        />
      )}
      <Modal isOpen={modal.isOpen} onClose={modal.onClose} size="4xl" isCentered>
        <ModalOverlay />
        <ModalContent py={0}>
          <ModalBody px={0} overflow="hidden" rounded="xl">
            <video
              src={checklistVideos[userOnboarding.watchDemo]}
              autoPlay
              width="100%"
              height="auto"
              preload="auto"
              controls
              poster={`${CDN_IMAGES_URL}/start-a-meeting.jpg`}
              onPlay={() => {
                onCheck({ [userOnboarding.watchDemo]: true });
              }}
            />
          </ModalBody>
          <ModalCloseButton
            colorScheme="gray"
            bgColor="whiteAlpha.600"
            right={-2}
            top={-2}
            _hover={{ bgColor: 'whiteAlpha.800' }}
          />
        </ModalContent>
      </Modal>
    </Flex>
  );
};
