import { routes } from '@/constants/routes';
import { Box, Button, VStack } from '@chakra-ui/react';
import { faSparkles } from '@fortawesome/pro-solid-svg-icons';
import { Icon } from '@modules/core/components/Icon/Icon';
import { userOnboarding } from '@waitroom/models';
import { useTranslation } from 'react-i18next';
import { useNavigate } from 'react-router-dom';
import { MeetingMemoryLocationState } from '../../MeetingMemory/types';
import { checklistVideos, ns } from '../config';
import { DefaultComponentProps } from '../types';

const key = userOnboarding.tryMeetingMemory;
const TryMeetingMemory = ({ popover, onCheck }: DefaultComponentProps) => {
  const { t } = useTranslation();
  const navigate = useNavigate();

  return (
    <VStack gap={3}>
      <p>{t(`${ns}${key}-desc`)}</p>
      <Box
        as="video"
        w="full"
        h="auto"
        playsInline
        loop
        autoPlay
        muted
        rounded="2xl"
        src={checklistVideos[key]}
      />
      <Button
        colorScheme="green"
        w="full"
        size={'sm'}
        leftIcon={<Icon icon={faSparkles} size={'lg'} />}
        onClick={() => {
          popover.onClose();
          onCheck({ [key]: true });
          navigate(routes.DASHBOARD.MEETING_MEMORY.link, {
            state: { q: t(`meetingMemory:showcasePrompt`) } as MeetingMemoryLocationState,
          });
        }}
      >
        {t(`${ns}${key}-btn`)}
      </Button>
    </VStack>
  );
};
export default TryMeetingMemory;
