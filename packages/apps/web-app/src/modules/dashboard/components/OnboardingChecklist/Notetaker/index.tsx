import { Box, Button, VStack } from '@chakra-ui/react';
import { userOnboarding } from '@waitroom/models';
import { useTranslation } from 'react-i18next';
import { Link } from 'react-router-dom';
import { routes } from '../../../../../constants/routes';
import { checklistVideos, ns } from '../config';
import { DefaultComponentProps } from '../types';

const key = userOnboarding.noteTaker;
const Notetaker = ({ popover }: DefaultComponentProps) => {
  const { t } = useTranslation();

  return (
    <VStack gap={3}>
      <p>{t(`${ns}${key}-desc`)}</p>
      <Box
        as="video"
        w="full"
        h="auto"
        playsInline
        loop
        autoPlay
        muted
        rounded="2xl"
        src={checklistVideos[key]}
      />
      <Button
        as={Link}
        to={`${routes.DASHBOARD.INTEGRATIONS.link}#notetaker`}
        variant={'outline'}
        colorScheme={'gray.900'}
        w={'full'}
        size={'sm'}
        onClick={() => {
          popover.onClose();
        }}
      >
        {t(`${ns}${key}-btn`)}
      </Button>
    </VStack>
  );
};

export default Notetaker;
