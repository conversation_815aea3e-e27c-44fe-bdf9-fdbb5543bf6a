import { Button, Flex, useDisclosure } from '@chakra-ui/react';
import { checkMark, checkedListSx } from '@core/components/List/Checked/CheckedList';
import Modal from '@core/components/Modal/Modal';
import { currencyFormatter } from '@core/utils/currency';
import { useMutation } from '@tanstack/react-query';
import { setSubscriptionPlan } from '@waitroom/auth';
import { findPrice, usdDivisor } from '@waitroom/common';
import { userApiService } from '@waitroom/common-api';
import { UserSubscriptionPlan } from '@waitroom/models';
import { getQueryRequestData, getRequestData } from '@waitroom/react-query';
import { dateOrNow } from '@waitroom/utils';
import { format } from 'date-fns';
import { ReactElement } from 'react';
import { useTranslation } from 'react-i18next';
import { useToastResponse } from '../../../../../core/hooks/useToastResponse';
import { useGetPlans } from '../../../../../subscription/hooks/useGetPlans';
import { ns } from '../constants';

export type ChangeAnnualButtonProps = {
  id: string;
  nextBilledAt: UserSubscriptionPlan['nextBilledAt'];
  isTrial: boolean;
};

const ChangeAnnualButton = ({
  id,
  nextBilledAt,
  isTrial,
}: ChangeAnnualButtonProps): ReactElement | null => {
  const { t } = useTranslation();
  const { onError } = useToastResponse();
  const { isOpen, onClose, onOpen } = useDisclosure();
  const plansQuery = useGetPlans();
  const plans = getQueryRequestData(plansQuery);
  const { mutate, isPending } = useMutation({
    mutationFn: () => userApiService.updateSubscriptionPlan(id, { data: { monthly: false } }),
    onSuccess: (res) => {
      const plan = getRequestData(res);
      if (res && res.success && plan) {
        setSubscriptionPlan(plan);
      }
    },
    onError,
  });

  const monthlyPrice = findPrice(plans, 'premium', 'year')?.unitPrice;
  const list = t(`dashboard.changeAnnual.${isTrial ? 'trialList' : 'list'}`, {
    returnObjects: true,
    price: currencyFormatter(monthlyPrice?.currencyCode).format(
      Number(monthlyPrice?.amount || 0) / usdDivisor,
    ),
    date: format(dateOrNow(nextBilledAt), 'MMMM dd, yyyy'),
  }) as string[];
  return (
    <>
      <Button textDecoration="underline" variant="link" onClick={onOpen} isLoading={isPending}>
        {t(`${ns}changeToAnnual`)}
      </Button>
      <Modal size="xl" isOpen={isOpen} onClose={onClose} isCentered>
        <Modal.Header mb={6}>{t('dashboard.changeAnnual.title')}</Modal.Header>
        <Modal.Body>
          <Flex as="ul" sx={checkedListSx}>
            {list.map((item, idx) => (
              <li key={idx}>
                {checkMark}
                <div>{item}</div>
              </li>
            ))}
          </Flex>
        </Modal.Body>
        <Modal.Footer mt={6} gap={4} justifyContent="center">
          <Button
            size="def"
            variant="outline"
            colorScheme="gray.900"
            onClick={onClose}
            isLoading={isPending}
          >
            {t('global.dismiss')}
          </Button>
          <Button size="def" colorScheme="gray.900" onClick={() => mutate()} isLoading={isPending}>
            {t('global.upgrade')}
          </Button>
        </Modal.Footer>
      </Modal>
    </>
  );
};
export default ChangeAnnualButton;
