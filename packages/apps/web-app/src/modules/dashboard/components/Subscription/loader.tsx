import { Container, Skeleton } from '@chakra-ui/react';
import FormLoader from '@modules/core/components/Loader/Form/FormLoader';
import { lazyLoad } from '@modules/core/utils/loadable';

export default lazyLoad(
  () => import('./index'),
  <Container maxW="container.md" py={24}>
    <Skeleton w="50%" minW="180px" h="32px" mb={6} />
    <FormLoader label textarea button inputs={4} />
  </Container>,
);
