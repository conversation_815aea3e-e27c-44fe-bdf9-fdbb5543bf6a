import { <PERSON>, <PERSON><PERSON>, <PERSON>lex, <PERSON>, Spinner, Text } from '@chakra-ui/react';
import { spinnerProps } from '@core/components/Common/styles';
import { commonConfig } from '@core/config';
import { currencyFormatter } from '@core/utils/currency';
import { getTrialDaysLeft, isPlanCanceling, isTrialPlan, usdDivisor } from '@waitroom/common';
import { UserComplete } from '@waitroom/models';
import { dateOrNow } from '@waitroom/utils';
import { format, fromUnixTime } from 'date-fns';
import { ReactElement, useEffect, useState } from 'react';
import { Trans, useTranslation } from 'react-i18next';
import Transactions from '../Transactions';
import CancelButton from './CancelButton';
import ChangeAnnualButton from './ChangeAnnualButton';
import PaymentDetails from './PaymentDetails';
import ReactivateButton from './ReactivateButton';
import { ns } from './constants';

export type PaidTierProps = {
  user: UserComplete;
};

const PaidTier = ({ user }: PaidTierProps): ReactElement | null => {
  const { t } = useTranslation();
  const { id, subscriptionPlan: plan, customerID } = user;
  const isAnnual = plan?.billingCycle?.interval === 'year';
  const isCanceling = isPlanCanceling(plan);
  const daysLeft = getTrialDaysLeft(plan);
  const unitPrice = plan?.items?.find((x) => x.status === 'active' || x.status === 'trialing')
    ?.price?.unitPrice;
  const formattedPrice = unitPrice
    ? currencyFormatter(unitPrice.currencyCode).format(Number(unitPrice.amount || 0) / usdDivisor)
    : null;
  const [cancelLoading, setCancelLoading] = useState(false);
  useEffect(() => {
    if (isCanceling) setCancelLoading(false);
  }, [isCanceling]);

  if (!plan) return null;
  return (
    <>
      <Flex align="flex-start" direction={{ base: 'column', lg: 'row' }} gap={8}>
        <Box order={{ base: 1, lg: 0 }} flexGrow={1}>
          {cancelLoading ? (
            <>
              <Text textTransform="uppercase" fontWeight="extrabold" color="red.500" mb={4}>
                {t(`${ns}cancellingLoadingTitle`)}
              </Text>
              <Flex gap={4} mb={3}>
                <div>
                  <Spinner size="xl" {...spinnerProps} thickness="4px" />
                </div>
                <Text fontSize="lg">{t(`${ns}cancelLoadingDesc`)}</Text>
              </Flex>
              <Flex gap={6} align="center" lineHeight="short">
                <Button
                  variant="link"
                  fontWeight="bold"
                  textDecoration="underline"
                  fontSize="lg"
                  onClick={() => window.location.reload()}
                >
                  {t('global.refresh')}
                </Button>
                <Link
                  href={`mailto:${commonConfig.company.emails.support}`}
                  target="_blank"
                  rel="noreferrer noopener"
                  fontWeight="bold"
                  color="gray.500"
                  fontSize="lg"
                >
                  {t('global.contactSupport')}
                </Link>
              </Flex>
            </>
          ) : (
            <div>
              {isCanceling ? (
                <>
                  <Text textTransform="uppercase" fontWeight="extrabold" color="red.500" mb={3}>
                    {t(`${ns}cancellingTitle`)}
                  </Text>
                  <Text p={4} bg="orange.200" rounded="lg" fontSize="sm" fontWeight="bold" mb={4}>
                    {t(`pricing.canceledBox`, {
                      date: format(dateOrNow(plan.currentBillingPeriod?.endsAt), 'MMM dd, yyyy'),
                    })}
                  </Text>
                  <ReactivateButton
                    id={id}
                    isAnnual={isAnnual}
                    isTrial={isTrialPlan(plan)}
                    date={format(
                      dateOrNow(plan.nextBilledAt || plan.currentBillingPeriod?.endsAt),
                      'MMM dd, yyyy',
                    )}
                    price={formattedPrice || ''}
                  />
                </>
              ) : (
                <>
                  {!!unitPrice && (
                    <>
                      <Text textTransform="uppercase" fontWeight="extrabold" color="red.500" mb={3}>
                        {t(`${ns}paymentInfo`)}
                      </Text>
                      <Flex mb={2} gap={2} align="flex-end">
                        <Text fontSize={['4xl', null, '5xl']} fontWeight="extrabold">
                          {formattedPrice}
                        </Text>
                        <Text py={2}>{t(`pricing.${isAnnual ? 'perYear' : 'perMonth'}`)}</Text>
                      </Flex>
                    </>
                  )}
                  {!!daysLeft && plan.trialEndsAt ? (
                    <Text p={4} bg="orange.200" rounded="lg" fontSize="sm" fontWeight="bold" mb={4}>
                      {t(`pricing.trialBox`, {
                        days: daysLeft,
                        date: format(fromUnixTime(plan.trialEndsAt), 'MMM dd, yyyy'),
                      })}
                    </Text>
                  ) : (
                    <Text mb={2}>
                      <Trans
                        i18nKey={`${ns}${isAnnual ? 'descriptionYear' : 'descriptionMonth'}`}
                        values={{
                          date: format(dateOrNow(plan.nextBilledAt), 'MMMM dd, yyyy'),
                        }}
                      />
                    </Text>
                  )}
                  {!isAnnual && (
                    <Flex my={4} align="center" gap={2}>
                      <ChangeAnnualButton
                        id={id}
                        isTrial={isTrialPlan(plan)}
                        nextBilledAt={plan.nextBilledAt}
                      />
                    </Flex>
                  )}
                </>
              )}
              {customerID ? <PaymentDetails id={id} customerId={customerID} /> : null}
            </div>
          )}
        </Box>
        <Box flexGrow={1} maxW={320} bgColor="gray.900" p={[4, 5, 6]} rounded="lg">
          <Text textTransform="uppercase" fontWeight="extrabold" color="red.500" mb={1}>
            {t(`${ns}subscription`)}
          </Text>
          <Text fontWeight="extrabold" fontSize={['4xl', null, '5xl']} mb={3}>
            {t(`${ns}${plan?.planConfig?.paddleProductName || 'free'}`)}
          </Text>
          {!isCanceling && <CancelButton onConfirm={() => setCancelLoading(true)} />}
          <Button
            as="a"
            href={`mailto:${commonConfig.company.emails.sales}`}
            target="_blank"
            rel="noreferrer noopener"
            textDecoration="underline"
            variant="link"
            mb={2}
          >
            {t(`${ns}enterpriseBtn`)}
          </Button>
        </Box>
      </Flex>
      <Transactions mt={16} id={user.id} planId={String(plan?.paddleSubscriptionID)} />
    </>
  );
};
export default PaidTier;
