import { Box, Container } from '@chakra-ui/react';
import { selectCurrentUser, useAuthStore } from '@waitroom/auth';
import { isFreePlan, isFreeTrialPlan, isTeamMember } from '@waitroom/common';
import { ReactElement, useEffect } from 'react';
import { Navigate } from 'react-router-dom';
import { routes } from '../../../../constants/routes';
import { analyticsService } from '../../../analytics/services';
import { defaultBodyPadding } from '../Layout/Default/constants';
import Bottom from './Bottom';
import FreeTier from './FreeTier';
import { Modals } from './Modals';
import PaidTier from './PaidTier';

const Subscription = (): ReactElement | null => {
  const currentUser = useAuthStore(selectCurrentUser);

  useEffect(() => {
    analyticsService.page('SUBSCRIPTIONS');
  }, []);

  if (!currentUser) return <Navigate to={routes.HOME} />;
  const isCurrentUserATeamMember = isTeamMember(currentUser);
  if (isCurrentUserATeamMember) return <Navigate to={routes.DASHBOARD.DEFAULT.link} />;
  const plan = currentUser?.subscriptionPlan;
  return (
    <>
      <Container maxW={'container.2xl'} {...defaultBodyPadding}>
        {!isFreePlan(plan) && !isFreeTrialPlan(plan) ? (
          <PaidTier user={currentUser} />
        ) : (
          <FreeTier user={currentUser} />
        )}
        <Box mt={20}>
          <Bottom />
        </Box>
        <Modals />
      </Container>
    </>
  );
};

export default Subscription;
