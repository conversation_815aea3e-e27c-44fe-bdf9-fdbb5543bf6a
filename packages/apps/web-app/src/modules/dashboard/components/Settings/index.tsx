import { Card, Container, Heading, SystemStyleObject } from '@chakra-ui/react';
import { ReactElement } from 'react';
import { useTranslation } from 'react-i18next';
import Speech from '../../../user/components/Settings/Speech';
import { defaultBodyPadding } from '../Layout/Default/constants';
import GeneralSettings from './General';

const cardSx: SystemStyleObject = {
  borderRadius: 'xl',
  boxShadow: 'none',
  border: '2px',
  borderColor: 'whiteAlpha.400',
  bgColor: 'gray.900',
  p: [4, 5, 6],
};

const Settings = (): ReactElement | null => {
  const { t } = useTranslation();

  return (
    <>
      <Container
        display={'flex'}
        flexDirection={'column'}
        maxW={'container.lg'}
        gap={6}
        {...defaultBodyPadding}
      >
        <Heading as={'h3'} size={'3xl'}>
          {t('global.settings')}
        </Heading>
        <Card sx={cardSx}>
          <GeneralSettings />
        </Card>
        <Card sx={cardSx}>
          <Speech />
        </Card>
      </Container>
    </>
  );
};
export default Settings;
