import { Box, Button, Flex, Heading, Text } from '@chakra-ui/react';
import Carousel from '@core/components/Carousel/Carousel';
import Modal from '@core/components/Modal/Modal';
import { type Release } from '@core/hooks/useReleases';
import { ComponentType, ReactElement } from 'react';
import { Trans, useTranslation } from 'react-i18next';
import Bots from './Children/Bots';
import { ChildProps } from './types';

export type ReleaseProps = ChildProps & {
  releases: Release[] | undefined;
};

const childComponents: Dict<ComponentType<{ onClose: ReleaseProps['onClose'] }>> = {
  bots: Bots,
};

const Releases = ({ releases, onClose }: ReleaseProps): ReactElement | null => {
  const { t } = useTranslation();

  if (!releases?.length) return null;
  const len = releases.length;
  const items = releases.map((r) => {
    const ChildComp = childComponents[r.id];
    return (
      <Flex key={r.id} direction={'column'} w={'full'} minH={'full'} pb={6}>
        <Heading as="h3" size="2xl" mb={6} w={'full'} lineHeight={'shorter'}>
          <Trans>{r.title}</Trans>
        </Heading>
        <Flex direction={'column'} grow={1} w={'full'}>
          <Box w={'full'}>
            <Trans>{r.description}</Trans>
          </Box>
        </Flex>
        {!!ChildComp && <ChildComp onClose={onClose} />}
      </Flex>
    );
  });

  return (
    <>
      <Modal.Header mb={4} />
      <Modal.Body w={'full'}>
        {len > 1 ? (
          <Carousel type="fade">
            <Carousel.Items>{items}</Carousel.Items>
            <Flex justify={'center'} align={'center'}>
              <Carousel.LeftArrow mr={4} />
              <Text fontSize={'sm'} color={'gray.600'} mr={4}>
                <Carousel.Number />
              </Text>
              <Carousel.RightArrow showDisabled={false} />
              <Carousel.Context>
                {({ index, length }) =>
                  index === length - 1 && (
                    <Button
                      size={'xs'}
                      colorScheme={'gray.100'}
                      boxShadow={'none'}
                      textTransform={'uppercase'}
                      onClick={onClose}
                    >
                      {t('global.close')}
                    </Button>
                  )
                }
              </Carousel.Context>
            </Flex>
          </Carousel>
        ) : (
          items
        )}
      </Modal.Body>
    </>
  );
};
export default Releases;
