import { useDisclosure } from '@chakra-ui/react';
import { selectCurrentUserSubscriptionPlan, useAuthStore } from '@waitroom/auth';
import { isFreeOrFreeTrialPlan } from '@waitroom/common';
import { useEffect, useRef } from 'react';
import { useLocation, useNavigate } from 'react-router-dom';

type WelcomeType = 'trial' | 'premium';

export const useWelcomeModal = () => {
  const navigate = useNavigate();
  const state = useLocation().state;
  const { welcome } = state || {};
  const currentPlan = useAuthStore(selectCurrentUserSubscriptionPlan); // loads async
  const type: WelcomeType = isFreeOrFreeTrialPlan(currentPlan) ? 'trial' : 'premium';

  const ref = useRef<WelcomeType | undefined>(welcome ? type : undefined); // store values across rerenders
  const { isOpen, onClose } = useDisclosure({ defaultIsOpen: !!ref.current });

  useEffect(() => {
    // clear welcome state
    if (welcome) navigate('.', { replace: true });
  }, [welcome, navigate]);

  return {
    type: ref.current,
    currentPlan,
    isOpen,
    onClose: () => {
      onClose();
      ref.current = undefined;
    },
  };
};
