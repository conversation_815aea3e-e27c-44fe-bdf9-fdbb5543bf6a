import { IconButton } from '@chakra-ui/react';
import { Icon } from '@core/components/Icon/Icon';
import { toggleRightSideMenu, useDashboardStore } from '@dashboard/store/store';
import { faSidebarFlip } from '@fortawesome/pro-solid-svg-icons';
import { ReactElement } from 'react';
import { SIDE_MENU_BREAKPOINT } from '../../RightSide';

const RightSideMenuToggler = (): ReactElement | null => {
  const exists = useDashboardStore((s) => !!s.layout.rightMenuContent);

  if (!exists) return null;
  return (
    <IconButton
      display={{ base: 'initial', [SIDE_MENU_BREAKPOINT]: 'none' }}
      aria-label="Menu toggle"
      size="xs"
      variant="solid"
      colorScheme="gray.100"
      rounded={'lg'}
      color={'gray.300'}
      boxShadow="none"
      lineHeight={1}
      onClick={toggleRightSideMenu}
    >
      <Icon icon={faSidebarFlip} size="xl" />
    </IconButton>
  );
};
export default RightSideMenuToggler;
