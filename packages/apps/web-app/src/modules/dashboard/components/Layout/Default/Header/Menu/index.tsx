import { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON> } from '@chakra-ui/react';
import { spinnerProps } from '@core/components/Common/styles';
import { Icon } from '@core/components/Icon/Icon';
import { faChevronDown, faVideoPlus } from '@fortawesome/pro-regular-svg-icons';
import { faCalendarPlus, faPlus } from '@fortawesome/pro-solid-svg-icons';
import { useInstantSession } from '@modules/session/hooks/useInstantSession';
import { selectCurrentUser, useAuthStore } from '@waitroom/auth';
import { setModal } from '@waitroom/common';
import { getRequestData } from '@waitroom/react-query';
import { ReactElement } from 'react';
import { useTranslation } from 'react-i18next';
import { ModalType } from '../../../../../../core/components/App/Modals/types';
import { SessionFormProps } from '../../../../../../session/components/Form/Session/SessionForm.types';
import { meetNowInitialValues } from '../../../../../../session/components/Form/Session/SessionForm.utils';
import { openSessionAccessModal } from '../../../../Sessions/helpers';

const HeaderMenu = (): ReactElement | null => {
  const { t } = useTranslation();
  const currentUser = useAuthStore(selectCurrentUser);
  const { start, isPending } = useInstantSession();
  return (
    <Menu>
      <MenuButton
        as={IconButton}
        size={'xs'}
        variant={'solid'}
        colorScheme={'gray.100'}
        rounded={'lg'}
        color={'gray.300'}
        boxShadow={'none'}
        w={'auto'}
        fontWeight={'medium'}
        px={2}
        lineHeight={1}
      >
        <Icon icon={faVideoPlus} size="xl" mr={1} />
        <Text as={'span'} display={{ base: 'none', md: 'inline' }} mr={1}>
          {t('global.new')}
        </Text>
        <Icon icon={faChevronDown} size="sm" />
      </MenuButton>
      <MenuList>
        <MenuItem
          isDisabled={isPending}
          closeOnSelect={false}
          onClick={() => {
            start();
          }}
        >
          {isPending ? (
            <Spinner {...spinnerProps} size={'sm'} mr={2} />
          ) : (
            <Icon icon={faPlus} mr={2} />
          )}
          <Text as={'span'}>{t('dashboard.meetNow')}</Text>
        </MenuItem>
        <MenuItem
          onClick={() => {
            setModal<SessionFormProps>({
              type: ModalType.SESSION_FORM,
              props: {
                initialValues: meetNowInitialValues(currentUser?.firstName, t),
                onSuccess: (response) => {
                  const data = getRequestData(response);
                  openSessionAccessModal(data?.session);
                },
              },
            });
          }}
        >
          <Icon icon={faCalendarPlus} mr={2} />
          <Text as={'span'}>{t('dashboard.newSessionBtn')}</Text>
        </MenuItem>
      </MenuList>
    </Menu>
  );
};
export default HeaderMenu;
