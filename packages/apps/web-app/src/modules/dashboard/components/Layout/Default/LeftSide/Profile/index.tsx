import { routes } from '@/constants/routes';
import { <PERSON><PERSON>, <PERSON>u, MenuButton, MenuItem, MenuList, Text } from '@chakra-ui/react';
import { Icon } from '@core/components/Icon/Icon';
import { commonConfig } from '@core/config';
import { faChevronRight, faGem } from '@fortawesome/pro-solid-svg-icons';
import SubscriptionAvatar from '@modules/user/components/Avatar/SubscriptionAvatar';
import { selectCurrentUser, useAuthStore, useLogout } from '@waitroom/auth';
import { fullName, isFreeOrFreeTrialPlan, isTeamMember } from '@waitroom/common';
import { ReactElement, useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import { Link } from 'react-router-dom';
import { SIDE_MENU_BREAKPOINT, SIDE_MENU_NARROW_BREAKPOINT } from '..';

const Profile = (): ReactElement | null => {
  const { t } = useTranslation();
  const currentUser = useAuthStore(selectCurrentUser);
  const { avatar, subscriptionPlan } = currentUser || {};
  const userFullName = fullName(currentUser);
  const freeOrTrial = isFreeOrFreeTrialPlan(subscriptionPlan);
  const { mutate: onLogout } = useLogout();

  const items = useMemo(() => {
    const conditionalItems = [];
    if (!isTeamMember) {
      conditionalItems.push(
        freeOrTrial
          ? {
              to: routes.DASHBOARD.SUBSCRIPTION.link,
              children: (
                <>
                  <Icon icon={faGem} color={'yellow.400'} mr={2} />
                  {t('global.upgrade')}
                </>
              ),
            }
          : {
              to: routes.DASHBOARD.SUBSCRIPTION.link,
              children: t('global.billing'),
            },
      );
    }
    return [
      {
        to: routes.DASHBOARD.PROFILE.DEFAULT.link,
        children: t('global.profile'),
        className: 'none',
      },
      ...conditionalItems,
      {
        to: commonConfig.links.knowledgebase.default,
        target: '_blank',
        rel: 'noopener noreferrer',
        children: t('global.help'),
        className: 'none',
      },
      {
        to: routes.DASHBOARD.SETTINGS.link,
        children: t('global.settings'),
        className: 'none',
      },
      {
        onClick: onLogout,
        children: t('global.logOut'),
      },
    ];
  }, [freeOrTrial, onLogout, t]);

  const display = {
    [SIDE_MENU_BREAKPOINT]: 'none',
    [SIDE_MENU_NARROW_BREAKPOINT]: 'inline',
  };
  return (
    <Menu placement="right-end" lazyBehavior="unmount" closeOnBlur>
      <MenuButton
        as={Button}
        variant={'ghost'}
        size={'md'}
        justifyContent={'flex-start'}
        textAlign={'left'}
        fontSize={'sm'}
        w={'full'}
        borderTop={'1px solid'}
        borderTopColor={'whiteAlpha.300'}
        px={[2, 2, 2, 3]}
        rounded={'0'}
        fontWeight={'bold'}
        color={'white'}
        sx={{
          '>span': {
            w: 'full',
            display: 'inline-flex',
            alignItems: 'center',
            justifyContent: {
              base: 'flex-start',
              [SIDE_MENU_BREAKPOINT]: 'center',
              [SIDE_MENU_NARROW_BREAKPOINT]: 'flex-start',
            },
            gap: 4,
          },
        }}
      >
        <SubscriptionAvatar
          src={avatar || undefined}
          name={userFullName}
          premium={!freeOrTrial}
          size={'xs'}
        />
        <Text as={'span'} display={display}>
          {userFullName}
        </Text>
        <Icon icon={faChevronRight} display={display} color={'gray.300'} ml={'auto'} />
      </MenuButton>
      <MenuList color={'white'}>
        {items.map((item, i) => (
          <MenuItem as={item.to ? Link : undefined} key={i} {...item}></MenuItem>
        ))}
      </MenuList>
    </Menu>
  );
};
export default Profile;
