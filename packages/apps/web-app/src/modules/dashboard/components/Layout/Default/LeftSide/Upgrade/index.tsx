import { routes } from '@/constants/routes';
import { Flex } from '@chakra-ui/react';
import { selectCurrentUserSubscriptionPlan, useAuthStore } from '@waitroom/auth';
import { isFreeOrFreeTrialPlan } from '@waitroom/common';
import { ReactElement } from 'react';
import { SIDE_MENU_BREAKPOINT, SIDE_MENU_NARROW_BREAKPOINT } from '..';
import UpgradeButton from '../../../../../../subscription/components/Button/Upgrade/UpgradeButton';
import UpgradeIconButton from '../../../../../../subscription/components/Button/Upgrade/UpgradeIconButton';

const Upgrade = (): ReactElement | null => {
  const subscriptionPlan = useAuthStore(selectCurrentUserSubscriptionPlan);
  const freeOrTrial = isFreeOrFreeTrialPlan(subscriptionPlan);

  if (!freeOrTrial) return null;
  return (
    <Flex direction={'column'} align={'center'} w={'full'}>
      <UpgradeButton
        to={routes.DASHBOARD.SUBSCRIPTION.link}
        w={'full'}
        size={'xs'}
        shadow={'none'}
        display={{
          [SIDE_MENU_BREAKPOINT]: 'none',
          [SIDE_MENU_NARROW_BREAKPOINT]: 'inline-flex',
        }}
      />
      <UpgradeIconButton
        to={routes.DASHBOARD.SUBSCRIPTION.link}
        size={'xs'}
        display={{
          base: 'none',
          [SIDE_MENU_BREAKPOINT]: 'inline-flex',
          [SIDE_MENU_NARROW_BREAKPOINT]: 'none',
        }}
      />
    </Flex>
  );
};
export default Upgrade;
