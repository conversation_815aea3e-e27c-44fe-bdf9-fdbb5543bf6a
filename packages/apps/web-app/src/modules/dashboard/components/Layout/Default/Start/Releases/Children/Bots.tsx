import { routes } from '@/constants/routes';
import { AspectRatio, Button, Flex } from '@chakra-ui/react';
import { CDN_VIDEOS_URL } from '@core/config';
import { ReactElement } from 'react';
import { useTranslation } from 'react-i18next';
import { Link } from 'react-router-dom';
import { ChildProps } from '../types';

const Bots = ({ onClose }: ChildProps): ReactElement | null => {
  const { t } = useTranslation();

  return (
    <Flex direction={'column'} justify={'center'} align={'center'} pt={6}>
      <AspectRatio w={'full'} maxH={360} ratio={16 / 9} mb={6}>
        <video
          src={`${CDN_VIDEOS_URL}/notetaker.mp4`}
          autoPlay
          width="100%"
          preload="auto"
          controls
        />
      </AspectRatio>
      <Button
        variant={'outline'}
        colorScheme={'gray.300'}
        color={'gray.900!'}
        size={'sm'}
        minW={200}
        as={Link}
        to={`${routes.DASHBOARD.INTEGRATIONS.link}#notetaker`}
        onClick={onClose}
      >
        {t('dashboard.onboarding.intro.notetakerBtn')}
      </Button>
    </Flex>
  );
};
export default Bots;
