import { Badge } from '@chakra-ui/react';
import { selectCurrentUserLobby, useAuthStore } from '@waitroom/auth';
import { useLobbyParticipants } from '@waitroom/common';
import { memo } from 'react';

export const LobbyCountBadge = memo(() => {
  const lobby = useAuthStore(selectCurrentUserLobby);
  const isActive = lobby?.isActive;
  const participants = useLobbyParticipants(
    isActive ? lobby.lobbyID : '',
    isActive ? lobby.slug : '',
  );
  const count = participants.length;

  if (!count) return null;
  return (
    <Badge
      variant={'solid'}
      bgColor={'red.700'}
      rounded={'full'}
      minW={'24px'}
      textAlign={'center'}
    >
      {count}
    </Badge>
  );
});
