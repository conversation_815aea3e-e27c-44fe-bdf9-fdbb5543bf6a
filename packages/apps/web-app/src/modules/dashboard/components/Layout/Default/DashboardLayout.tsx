import { Box, BoxProps, DarkMode, Flex } from '@chakra-ui/react';
import { ReactElement } from 'react';
import { Modals } from '../../Modals/Modals';
import Header, { HeaderProps } from './Header/Header';
import LeftSideMenu from './LeftSide';
import RightSideMenu from './RightSide';
import Start from './Start';

export interface LayoutProps extends BoxProps {
  headerProps?: HeaderProps;
}

const DashboardLayout = ({ headerProps, ...rest }: LayoutProps): ReactElement | null => (
  <DarkMode>
    <Flex minH={'100vh'} direction={'row'} w={'full'} align={'flex-start'}>
      <LeftSideMenu />
      <Box
        as={'main'}
        flexGrow={1}
        w={'full'}
        h={'100vh'}
        overflow={'auto'}
        bg={'gray.1000'}
        color={'white'}
        px={{ base: 0, sm: 2, lg: 6 }}
        {...rest}
      >
        <Header bg={'gray.1000'} color={'white'} {...headerProps} />
        {rest.children}
      </Box>
      <RightSideMenu />
    </Flex>
    <Modals />
    <Start />
  </DarkMode>
);

export default DashboardLayout;
