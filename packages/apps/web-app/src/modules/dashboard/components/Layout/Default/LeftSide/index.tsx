import { routes } from '@/constants/routes';
import { Flex } from '@chakra-ui/react';
import { usePostHogFeatureFlag } from '@core/components/FeatureFlagGate/FeatureFlag';
import { MenuItem } from '@core/components/SideMenu/NavItem';
import SideMenu, { SideMenuProps } from '@core/components/SideMenu/SideMenu';
import { closeLeftSideMenu, openLeftSideMenu, useDashboardStore } from '@dashboard/store/store';
import {
  faBellConcierge,
  faHome,
  faPlug,
  faSearchPlus,
  faSparkles,
} from '@fortawesome/pro-regular-svg-icons';
import { selectCurrentUserSubscriptionPlan, useAuthStore } from '@waitroom/auth';
import { isFeatureEnabled } from '@waitroom/common';
import { PostHogFeatureFlag } from '@waitroom/models';
import { TFunction } from 'i18next';
import { ReactElement, useMemo } from 'react';
import { Trans, useTranslation } from 'react-i18next';
import { OnboardingChecklist } from '../../../OnboardingChecklist/OnboardingChecklist';
import { LobbyCountBadge } from './LobbyCountBadge';
import LogoLink from './LogoLink';
import Profile from './Profile';
import Upgrade from './Upgrade';

export const SIDE_MENU_BREAKPOINT = 'md';
export const SIDE_MENU_NARROW_BREAKPOINT = 'xl';
export const SIDE_MENU_WIDTH = 250;
export const SIDE_MENU_NARROW_WIDTH = 68;

const baseItems = (t: TFunction): MenuItem[] => [
  {
    to: routes.DASHBOARD.SESSIONS.link,
    icon: faHome,
    content: <Trans i18nKey={'global.meetings'} />,
    title: t('global.home'),
    isActive: (curr) =>
      curr === routes.DASHBOARD.DEFAULT.link || curr.startsWith(routes.DASHBOARD.SESSIONS.link),
  },
  {
    to: routes.DASHBOARD.INTEGRATIONS.link,
    icon: faPlug,
    content: <Trans i18nKey={'global.integrations'} />,
    title: t('global.integrations'),
    isActive: (curr) => curr.startsWith(routes.DASHBOARD.INTEGRATIONS.link),
  },
];

export type LeftSideMenuProps = Pick<SideMenuProps, 'children'>;

const LeftSideMenu = (props: LeftSideMenuProps): ReactElement | null => {
  const { t } = useTranslation();
  const subscriptionPlan = useAuthStore(selectCurrentUserSubscriptionPlan);
  const meetingMemory = isFeatureEnabled(subscriptionPlan, 'meetingMemory');
  const isLobbyFF = usePostHogFeatureFlag(PostHogFeatureFlag.Lobby);
  const isOpen = useDashboardStore((s) => !!s.layout.leftMenuOpen);

  const items = useMemo(() => {
    const items = [...baseItems(t)];
    if (isLobbyFF) {
      items.push({
        to: routes.DASHBOARD.LOBBY.link,
        icon: faBellConcierge,
        content: (
          <>
            <Trans i18nKey={'global.lobby'} />
            <LobbyCountBadge />
          </>
        ),
        isActive: (curr) => curr.startsWith(routes.DASHBOARD.LOBBY.link),
      });
    }
    if (meetingMemory) {
      items.push({
        to: routes.DASHBOARD.MEETING_MEMORY.link,
        icon: faSparkles,
        content: <Trans i18nKey={'global.meetingMemory'} />,
        isActive: (curr) => curr.startsWith(routes.DASHBOARD.MEETING_MEMORY.link),
      });
    }
    items.push({
      to: routes.DASHBOARD.XRAY.DEFAULT.link,
      icon: faSearchPlus,
      content: <Trans i18nKey={'global.xray'} />,
      title: t('global.xray'),
      isActive: (curr) => curr.startsWith(routes.DASHBOARD.XRAY.DEFAULT.link),
    });
    return items;
  }, [isLobbyFF, meetingMemory, t]);

  return (
    <SideMenu
      items={items}
      topUi={<LogoLink />}
      height={'100vh'}
      flexShrink={0}
      placement={'left'}
      width={{
        base: SIDE_MENU_WIDTH,
        [SIDE_MENU_BREAKPOINT]: SIDE_MENU_NARROW_WIDTH,
        [SIDE_MENU_NARROW_BREAKPOINT]: SIDE_MENU_WIDTH,
      }}
      breakpoint={SIDE_MENU_BREAKPOINT}
      narrowBreakpoint={SIDE_MENU_NARROW_BREAKPOINT}
      bottomUi={
        <>
          <Flex
            direction={'column'}
            align={'center'}
            gap={3}
            px={{
              base: 4,
              [SIDE_MENU_BREAKPOINT]: 2,
              [SIDE_MENU_NARROW_BREAKPOINT]: 4,
            }}
            mb={4}
          >
            <OnboardingChecklist />
            <Upgrade />
          </Flex>
          <Profile />
        </>
      }
      isOpen={isOpen}
      onOpen={openLeftSideMenu}
      onClose={closeLeftSideMenu}
      {...props}
    />
  );
};
export default LeftSideMenu;
