import { routes } from '@/constants/routes';
import { Box, Button } from '@chakra-ui/react';
import Modal from '@core/components/Modal/Modal';
import { dateOrNow } from '@waitroom/utils';
import { format } from 'date-fns';
import { memo, ReactElement } from 'react';
import { Trans, useTranslation } from 'react-i18next';
import { Link } from 'react-router-dom';
import Releases from './Releases';
import { useStart } from './useStart';

const ns = 'dashboard.welcome.';

const Start = memo((): ReactElement | null => {
  const { t } = useTranslation();
  const { isOpen, onClose, currentPlan, type, releases } = useStart();

  return (
    <Modal
      size={type === 'release' ? 'xl' : 'lg'}
      isOpen={isOpen && !!type}
      onClose={onClose}
      isCentered
      forceCenter
    >
      {!!type && (
        <>
          {type === 'release' ? (
            <Releases releases={releases} onClose={onClose} />
          ) : (
            <>
              <Modal.Header mb={4}>{!!type && t(`${ns}${type}.title`)}</Modal.Header>
              <Modal.Body>
                <Trans
                  i18nKey={`${ns}${type}.body`}
                  values={{
                    date: format(dateOrNow(currentPlan?.nextBilledAt), 'MMM d, yyyy'),
                  }}
                  components={{
                    button: <Link to={routes.CHECKOUT} />,
                  }}
                />
                <Box textAlign="center">
                  <Button colorScheme="gray.900" mt={8} onClick={onClose}>
                    {!!type && t(`${ns}${type}.button`)}
                  </Button>
                </Box>
              </Modal.Body>
            </>
          )}
        </>
      )}
    </Modal>
  );
});
export default Start;
