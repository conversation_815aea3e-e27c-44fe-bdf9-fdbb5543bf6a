import { useDisclosure } from '@chakra-ui/react';
import { useReleases } from '@core/hooks/useReleases';
import { selectCurrentUserSubscriptionPlan, useAuthStore } from '@waitroom/auth';
import { isFreeOrFreeTrialPlan } from '@waitroom/common';
import { userOnboarding } from '@waitroom/models';
import { useEffect, useState } from 'react';
import { useLocation, useNavigate } from 'react-router-dom';

export type StartType = 'trial' | 'premium' | 'release';

export const useStart = () => {
  const navigate = useNavigate();
  // get navigation start state
  const { start } = useLocation().state || {};
  const currentPlan = useAuthStore(selectCurrentUserSubscriptionPlan); // loads async
  const sType: StartType = isFreeOrFreeTrialPlan(currentPlan) ? 'trial' : 'premium';
  const { hasNew, releases, newIds, mutation } = useReleases();

  const [type, setType] = useState<StartType | undefined>(() =>
    start ? sType : hasNew ? 'release' : undefined,
  ); // store values across rerenders
  const { isOpen, onClose } = useDisclosure({ defaultIsOpen: !!type });

  useEffect(() => {
    // clear start state
    if (start) navigate('.', { replace: true });
  }, [start, navigate]);

  return {
    type,
    releases,
    currentPlan,
    isOpen,
    onClose: () => {
      const currType = type;
      if (currType === 'release') {
        mutation.mutate({
          [userOnboarding.features]: newIds?.join(','),
        });
      }
      const nextType = currType !== 'release' ? (hasNew ? 'release' : undefined) : undefined;
      setType(nextType);
      if (!nextType) onClose();
    },
  };
};
