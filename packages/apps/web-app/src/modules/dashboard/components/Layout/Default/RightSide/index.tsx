import SideMenu from '@core/components/SideMenu/SideMenu';
import { ReactElement } from 'react';
import { closeRightSideMenu, openRightSideMenu, useDashboardStore } from '../../../../store/store';

export const SIDE_MENU_BREAKPOINT = '2xl';
export const SIDE_MENU_WIDTH = 350;

const RightSideMenu = (): ReactElement | null => {
  const isOpen = useDashboardStore((s) => !!s.layout.rightMenuOpen);
  const Content = useDashboardStore((s) => s.layout.rightMenuContent);

  if (!Content) return null;
  return (
    <SideMenu
      height={'100vh'}
      flexShrink={0}
      width={SIDE_MENU_WIDTH}
      breakpoint={SIDE_MENU_BREAKPOINT}
      isOpen={isOpen}
      onOpen={openRightSideMenu}
      onClose={closeRightSideMenu}
      placement={'right'}
    >
      <Content />
    </SideMenu>
  );
};

export default RightSideMenu;
