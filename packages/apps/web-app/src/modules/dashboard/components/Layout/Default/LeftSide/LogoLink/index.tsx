import { routes } from '@/constants/routes';
import { ImageProps, Link, LinkProps } from '@chakra-ui/react';
import { LogoIconImage, LogoImage } from '@core/components/Logo/Logo';
import { ReactElement } from 'react';
import { Link as RLink, useLocation } from 'react-router-dom';
import { SIDE_MENU_BREAKPOINT, SIDE_MENU_NARROW_BREAKPOINT } from '..';

export type LogoLinkProps = LinkProps & {
  logoProps?: ImageProps;
  iconProps?: ImageProps;
};

const LogoLink = ({ logoProps, iconProps, ...props }: LogoLinkProps): ReactElement | null => {
  const { pathname } = useLocation();
  return (
    <Link
      as={RLink}
      to={
        pathname === routes.DASHBOARD.DEFAULT.link
          ? routes.HOME_EXPLICIT
          : routes.DASHBOARD.DEFAULT.link
      }
      className="none"
      display={'inline-flex'}
      tabIndex={0}
      py={6}
      px={5}
      {...props}
    >
      <>
        <LogoIconImage
          display={{
            base: 'none',
            [SIDE_MENU_BREAKPOINT]: 'initial',
            [SIDE_MENU_NARROW_BREAKPOINT]: 'none',
          }}
          dark
          className="logo"
          w={'auto'}
          h={'26px'}
          {...iconProps}
        />
        <LogoImage
          display={{
            [SIDE_MENU_BREAKPOINT]: 'none',
            [SIDE_MENU_NARROW_BREAKPOINT]: 'initial',
          }}
          dark
          className="logo"
          w={'auto'}
          h={'26px'}
          {...logoProps}
        />
      </>
    </Link>
  );
};
export default LogoLink;
