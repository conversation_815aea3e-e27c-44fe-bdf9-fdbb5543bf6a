import { Button, ButtonProps } from '@chakra-ui/react';
import { EventType } from '@waitroom/analytics';
import { useAuthStore } from '@waitroom/auth';
import { useActivateTrial } from '@waitroom/common';
import { UserApiService } from '@waitroom/common-api';
import { getRequestData } from '@waitroom/react-query';
import { ReactElement } from 'react';
import { analyticsService } from '../../../../../../analytics/services';

const StartTrialButton = ({
  onSuccess,
  ...props
}: ButtonProps & {
  onSuccess?: (resp: UserApiService.ActivateTrial['response']) => void;
}): ReactElement | null => {
  const { userId, isAuthenticated } = useAuthStore();

  const { isPending, mutate } = useActivateTrial({
    onSuccess: (response) => {
      const plan = getRequestData(response)?.userSubscriptionPlan;
      if (plan?.status === 'trialing') {
        analyticsService.track(EventType.SubscriptionFreeTrialStarted, {
          isNewHost: false,
        });
      }
      if (onSuccess) onSuccess(response);
    },
  });

  if (!userId || !isAuthenticated) return null;
  return <Button {...props} isLoading={isPending} onClick={() => mutate()} />;
};
export default StartTrialButton;
