import { MEETING_MEMORY } from '@/constants/shortcutKeys';
import { Box, BoxProps, Button, Flex, IconButton, Text } from '@chakra-ui/react';
import { ModalType } from '@core/components/App/Modals/types';
import { Icon } from '@core/components/Icon/Icon';
import { useKeyUp } from '@core/hooks/useKeyPress';
import { filterKeyboardEvent } from '@core/utils/dom';
import { toggleLeftSideMenu } from '@dashboard/store/store';
import { faBars, faSparkles } from '@fortawesome/pro-solid-svg-icons';
import SubscriptionBanner from '@modules/subscription/components/Banner/Subscription/SubscriptionBanner';
import { setModal } from '@waitroom/common';
import { memo, ReactElement, useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import { SIDE_MENU_BREAKPOINT } from '../LeftSide';
import LogoLink from '../LeftSide/LogoLink';
import HeaderMenu from './Menu';
import RightSideMenuToggler from './RightSideMenuToggler';

export type HeaderProps = BoxProps;

export const HEADER_HEIGHT = 58;
const openMeetingMemoryModal = () => {
  setModal({
    type: ModalType.MEETING_MEMORY,
  });
};

const Header = memo((_props: HeaderProps): ReactElement | null => {
  const { t } = useTranslation();

  const keyPressActions = useMemo(() => {
    return {
      KeyM: (ev: KeyboardEvent) => {
        if (filterKeyboardEvent(ev)) openMeetingMemoryModal();
      },
    };
  }, []);
  useKeyUp(keyPressActions); // use keyup to prevent filling the input field with the shortcut key

  return (
    <>
      <SubscriptionBanner />
      <Box
        as="header"
        position={'sticky'}
        top={0}
        w={'auto'}
        bg={'gray.1000'}
        mx={[0, 0, -2, -4]}
        py={2}
        px={[4, 6, 10]}
        display="flex"
        alignItems="center"
        zIndex={'sticky'}
      >
        <Flex w="full" align="center" justify="space-between" h="full">
          <Flex align="center" minW="120px">
            <IconButton
              display={{ base: 'initial', [SIDE_MENU_BREAKPOINT]: 'none' }}
              aria-label="Menu toggle"
              size="xs"
              variant="solid"
              colorScheme="gray.100"
              rounded={'lg'}
              color={'gray.300'}
              boxShadow="none"
              lineHeight={1}
              mr={4}
              onClick={toggleLeftSideMenu}
            >
              <Icon icon={faBars} size="xl" />
            </IconButton>
            <Box lineHeight={0} flexShrink={0}>
              <LogoLink
                p={0}
                logoProps={{
                  display: {
                    base: 'none',
                  },
                }}
                iconProps={{
                  display: {
                    base: 'initial',
                    [SIDE_MENU_BREAKPOINT]: 'none',
                  },
                }}
              />
            </Box>
          </Flex>
          <Flex flex={1} justify={'center'} grow={1}>
            <Button
              borderRadius="8px"
              variant={'outline'}
              colorScheme={'gray.200'}
              borderWidth={1}
              bgColor={'whiteAlpha.100!'}
              color={'white!'}
              fontWeight={'medium'}
              size={'xs'}
              overflow={'hidden'}
              px={[2, 2, 2, 3]}
              gap={[1, 1, 2, 3]}
              rightIcon={
                <Box
                  bgColor="whiteAlpha.200"
                  color="gray.300"
                  fontSize="xs"
                  rounded="sm"
                  px={1}
                  py={0.5}
                  title={`${t('global.shortcut')} ${MEETING_MEMORY}`}
                >
                  {MEETING_MEMORY}
                </Box>
              }
              leftIcon={<Icon icon={faSparkles} color={'red.500'} />}
              title={t('dashboard.askMeetingMemory')}
              onClick={openMeetingMemoryModal}
            >
              <Text as={'span'} display={{ base: 'none', lg: 'inline' }}>
                {t('dashboard.askMeetingMemory')}
              </Text>
              <Text as={'span'} display={{ base: 'none', sm: 'inline', lg: 'none' }}>
                {t('dashboard.askMeetingMemoryShort')}
              </Text>
            </Button>
          </Flex>
          <Flex align="center" gap={2} minW="120px" justify="flex-end">
            <HeaderMenu />
            <RightSideMenuToggler />
          </Flex>
        </Flex>
      </Box>
    </>
  );
});

export default Header;
