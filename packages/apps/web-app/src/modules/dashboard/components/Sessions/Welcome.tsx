import { Box, Flex, Image, Text, VStack } from '@chakra-ui/react';
import { CDN_IMAGES_URL } from '@core/config';
import { selectCurrentUser, useAuthStore } from '@waitroom/auth';
import { useTranslation } from 'react-i18next';
import CreateButtons from './RightSideMenu/CreateButtons';

export const Welcome = () => {
  const currentUser = useAuthStore(selectCurrentUser);
  const { t } = useTranslation();

  const title = t('dashboard.welcome{name}', {
    name: currentUser?.firstName,
  });

  return (
    <VStack py={12} direction="column" gap={8}>
      <Image
        src={`${CDN_IMAGES_URL}/dashboard-landing.svg`}
        htmlWidth="476"
        htmlHeight="440"
        h={{ base: '175px', sm: '216px' }}
        w="auto"
        rounded={'xl'}
        alt={title}
        bgColor={'white'}
        p={6}
      />
      <Box textAlign="center">
        <Text fontSize="3xl" fontWeight={800} mb={2}>
          👋 {title}
        </Text>
        <Text color={'gray.400'}>{t('dashboard.welcomeDesc')}</Text>
      </Box>
      <Flex w={'full'} direction={{ base: 'column', lg: 'row' }} gap={2}>
        <CreateButtons />
      </Flex>
    </VStack>
  );
};
