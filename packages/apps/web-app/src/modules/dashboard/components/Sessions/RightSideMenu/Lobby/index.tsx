import { Box, Text } from '@chakra-ui/react';
import { selectCurrentUserLobby, useAuthStore } from '@waitroom/auth';
import { ReactElement } from 'react';
import { useTranslation } from 'react-i18next';
import LobbyCardSmall from '../../../Lobby/LobbyCard/Small/LobbyCardSmall';

const Lobby = (): ReactElement | null => {
  const lobby = useAuthStore(selectCurrentUserLobby);
  const { t } = useTranslation();

  if (!lobby) return null;
  return (
    <Box py={8} borderBottom={'1px'} borderColor={'gray.700'}>
      <LobbyCardSmall
        lobby={lobby}
        subtext={
          <Text fontSize={'sm'} color={'gray.300'} mb={2}>
            {t('dashboard.sessions.rightMenu.lobbySubtext')}
          </Text>
        }
      />
    </Box>
  );
};
export default Lobby;
