import {
  <PERSON>,
  Container,
  <PERSON>lex,
  <PERSON><PERSON>,
  Icon<PERSON><PERSON>on,
  <PERSON><PERSON>,
  <PERSON>u<PERSON><PERSON><PERSON>,
  <PERSON>u<PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON>b<PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
} from '@chakra-ui/react';
import { ModalType } from '@core/components/App/Modals/types';
import { Icon } from '@core/components/Icon/Icon';
import { faCalendarPlus, faPlus } from '@fortawesome/pro-solid-svg-icons';
import { selectCurrentUser, useAuthStore } from '@waitroom/auth';
import { setModal } from '@waitroom/common';
import { getRequestData } from '@waitroom/react-query';
import { useTranslation } from 'react-i18next';
import { SessionFormProps } from '../../../session/components/Form/Session/SessionForm.types';
import { meetNowInitialValues } from '../../../session/components/Form/Session/SessionForm.utils';
import { useInstantSession } from '../../../session/hooks/useInstantSession';
import { useRightSidebar } from '../../hooks/useRightSidebar';
import { BookADemo } from '../BookADemo/BookADemo';
import { defaultBodyPadding } from '../Layout/Default/constants';
import FutureSessions from './Future';
import { openSessionAccessModal } from './helpers';
import MeetingMemory from './MeetingMemory/MeetingMemory';
import { Modals } from './Modals';
import PastSessions from './Past';
import RightSideMenu from './RightSideMenu';

const Sessions = () => {
  const { t } = useTranslation();
  const currentUser = useAuthStore(selectCurrentUser);
  const { start, isPending } = useInstantSession();
  useRightSidebar(RightSideMenu);

  return (
    <Container maxW={'3xl'} {...defaultBodyPadding}>
      <Box justifySelf="center" w="100%" maxW={{ xl: '680px' }} mx="auto">
        <MeetingMemory />
        <Flex align={'center'} gap={4} mb={8}>
          <Heading as="h1">{t('dashboard.yourMeetings')}</Heading>
          <Menu placement="bottom">
            <MenuButton
              as={IconButton}
              variant={'outline'}
              borderWidth={'2px'}
              colorScheme={'red.700'}
              color={'white'}
              size={'3xs'}
            >
              <Icon icon={faPlus} size="lg" />
            </MenuButton>
            <MenuList>
              <MenuItem
                isDisabled={isPending}
                onClick={() => {
                  start();
                }}
              >
                {isPending && <Spinner mr={4} />}
                <Icon icon={faPlus} mr={2} />
                {t('dashboard.meetNow')}
              </MenuItem>
              <MenuItem
                onClick={() => {
                  setModal<SessionFormProps>({
                    type: ModalType.SESSION_FORM,
                    props: {
                      initialValues: meetNowInitialValues(currentUser?.firstName, t),
                      onSuccess: (response) => {
                        const data = getRequestData(response);
                        openSessionAccessModal(data?.session);
                      },
                    },
                  });
                }}
              >
                <Icon icon={faCalendarPlus} mr={2} />
                {t('dashboard.newSessionBtn')}
              </MenuItem>
            </MenuList>
          </Menu>
        </Flex>
        <Tabs variant={'line'} colorScheme={'red'} isLazy>
          <TabList gap={1}>
            <Tab>{t(`dashboard.upcomingTabTitle`)}</Tab>
            <Tab>{t(`dashboard.pastTabTitle`)}</Tab>
          </TabList>
          <TabPanels>
            <TabPanel pt={0} px={0}>
              <FutureSessions />
            </TabPanel>
            <TabPanel pt={0} px={0}>
              <PastSessions />
            </TabPanel>
          </TabPanels>
        </Tabs>
      </Box>
      <Modals />
      <BookADemo />
    </Container>
  );
};

export default Sessions;
