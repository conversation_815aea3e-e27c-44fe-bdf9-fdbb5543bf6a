import { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Spin<PERSON> } from '@chakra-ui/react';
import { ModalType } from '@core/components/App/Modals/types';
import { Icon } from '@core/components/Icon/Icon';
import { faCalendarPlus, faPlus } from '@fortawesome/pro-solid-svg-icons';
import { SessionFormProps } from '@modules/session/components/Form/Session/SessionForm.types';
import { meetNowInitialValues } from '@modules/session/components/Form/Session/SessionForm.utils';
import { useInstantSession } from '@modules/session/hooks/useInstantSession';
import { selectCurrentUser, useAuthStore } from '@waitroom/auth';
import { setModal } from '@waitroom/common';
import { getRequestData } from '@waitroom/react-query';
import { memo, ReactElement } from 'react';
import { useTranslation } from 'react-i18next';
import { openSessionAccessModal } from '../helpers';

const NewMenu = memo((): ReactElement | null => {
  const { t } = useTranslation();
  const currentUser = useAuthStore(selectCurrentUser);
  const { start, isPending } = useInstantSession();

  return (
    <Menu placement="bottom">
      <MenuButton
        as={IconButton}
        variant={'outline'}
        borderWidth={'2px'}
        colorScheme={'red.700'}
        color={'white'}
        size={'3xs'}
      >
        <Icon icon={faPlus} size="lg" />
      </MenuButton>
      <MenuList>
        <MenuItem
          isDisabled={isPending}
          onClick={() => {
            start();
          }}
        >
          {isPending && <Spinner mr={4} />}
          <Icon icon={faPlus} mr={2} />
          {t('dashboard.meetNow')}
        </MenuItem>
        <MenuItem
          onClick={() => {
            setModal<SessionFormProps>({
              type: ModalType.SESSION_FORM,
              props: {
                initialValues: meetNowInitialValues(currentUser?.firstName, t),
                onSuccess: (response) => {
                  const data = getRequestData(response);
                  openSessionAccessModal(data?.session);
                },
              },
            });
          }}
        >
          <Icon icon={faCalendarPlus} mr={2} />
          {t('dashboard.newSessionBtn')}
        </MenuItem>
      </MenuList>
    </Menu>
  );
});
export default NewMenu;
