import { Box, Flex, Heading } from '@chakra-ui/react';
import { memo, ReactElement } from 'react';
import { useTranslation } from 'react-i18next';
import Bots from './Bots';
import CreateButtons from './CreateButtons';
import Lobby from './Lobby';
import { ns } from './constants';

const RightSideMenu = memo((): ReactElement | null => {
  const { t } = useTranslation();

  return (
    <Flex px={[4, 4, 5]} direction="column" color={'white'}>
      <Box textAlign={'center'}>
        <Heading as="h3" size="md" px={4} py={6} borderBottomWidth={'1px'} borderColor={'gray.700'}>
          {t(`${ns}.title`)}
        </Heading>
      </Box>
      <Bots />
      <CreateButtons />
      <Lobby />
    </Flex>
  );
});

export default RightSideMenu;
