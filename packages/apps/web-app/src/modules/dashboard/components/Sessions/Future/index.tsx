import { Alert, Box, Text } from '@chakra-ui/react';
import CardLoader from '@core/components/Loader/Card/CardLoader';
import Confirm from '@core/components/Modal/Confirm/Confirm';
import { sessionIdsKey } from '@waitroom/common';
import { repeat } from '@waitroom/react-utils';
import { buildKey } from '@waitroom/utils';
import { memo, useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import DashboardSessionCard from '../../../../session/components/Card/Dashboard/Future/DashboardCard';
import { Welcome } from '../Welcome';
import { useComponent } from './useComponent';

const Future = memo(() => {
  const { t } = useTranslation();
  const {
    query,
    sessions,
    sessionRules,
    onManage,
    deleteOpen,
    deleteMutation,
    onDelete,
    onConfirmDelete,
    onConfirmClose,
  } = useComponent();
  const filtered = useMemo(() => sessions?.filter((x) => x.endTimestamp), [sessions]);

  if (query.isLoading) return <Box mt={6}>{repeat(<CardLoader />)}</Box>;
  if (!filtered?.length) return <Welcome />;
  return (
    <>
      <Confirm
        header={t('dashboard.deleteSessionConfirmTitle')}
        body={
          <>
            {t('dashboard.deleteSessionConfirmDesc')}
            {deleteMutation.isError ? (
              <Alert status="error" fontSize="sm" mt={4}>
                {deleteMutation.error.error?.message}
              </Alert>
            ) : null}
          </>
        }
        cancelText={t('dashboard.keepMeeting')}
        confirmText={t('dashboard.deleteConfirm')}
        isOpen={deleteOpen}
        onConfirm={onConfirmDelete}
        onClose={onConfirmClose}
        isLoading={deleteMutation.isPending}
      />
      <Box minH={5} mt={3}>
        {query.isFetching && (
          <Text fontSize={'sm'} color={'gray.500'}>
            {t('global.updating')}...
          </Text>
        )}
      </Box>
      {filtered.map((session) => (
        <DashboardSessionCard
          key={sessionIdsKey(session)}
          session={session}
          rules={sessionRules.rules[buildKey([session.sessionID, session.sessionRecurrenceID])]}
          onDelete={onDelete}
          onManage={onManage}
        />
      ))}
    </>
  );
});

export default Future;
