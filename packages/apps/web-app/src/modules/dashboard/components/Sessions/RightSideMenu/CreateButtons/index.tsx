import { Box, Button, Flex, Heading } from '@chakra-ui/react';
import { faCalendarPlus, faPlus } from '@fortawesome/pro-solid-svg-icons';
import { ModalType } from '@modules/core/components/App/Modals/types';
import { Icon } from '@modules/core/components/Icon/Icon';
import { useInstantSession } from '@modules/session/hooks/useInstantSession';
import { selectCurrentUser, useAuthStore } from '@waitroom/auth';
import { setModal } from '@waitroom/common';
import { getRequestData } from '@waitroom/react-query';
import { ReactElement } from 'react';
import { useTranslation } from 'react-i18next';
import { SessionFormProps } from '../../../../../session/components/Form/Session/SessionForm.types';
import { meetNowInitialValues } from '../../../../../session/components/Form/Session/SessionForm.utils';
import { openSessionAccessModal } from '../../helpers';
import { ns } from '../constants';

const CreateButtons = (): ReactElement | null => {
  const { t } = useTranslation();
  const currentUser = useAuthStore(selectCurrentUser);
  const { start, isPending } = useInstantSession();

  return (
    <Box py={8} borderBottom={'1px'} borderColor={'gray.700'}>
      <Flex mb={6}>
        <Icon icon={faPlus} size={'lg'} color={'gray.500'} mr={3} />
        <Heading as={'h4'} size={'md'}>
          {t(`${ns}.createTitle`)}
        </Heading>
      </Flex>
      <Button
        colorScheme="red"
        size={'xs'}
        w={'full'}
        leftIcon={<Icon icon={faPlus} />}
        boxShadow={'none'}
        isLoading={isPending}
        isDisabled={isPending}
        mb={4}
        onClick={() => {
          start();
        }}
      >
        {t('dashboard.meetNow')}
      </Button>
      <Button
        variant={'outline'}
        colorScheme={'gray.800'}
        boxShadow={'none'}
        size={'xs'}
        w={'full'}
        data-id="create-scheduled-session"
        leftIcon={<Icon icon={faCalendarPlus} />}
        onClick={() => {
          setModal<SessionFormProps>({
            type: ModalType.SESSION_FORM,
            props: {
              initialValues: meetNowInitialValues(currentUser?.firstName, t),
              onSuccess: (response) => {
                const data = getRequestData(response);
                openSessionAccessModal(data?.session);
              },
            },
          });
        }}
      >
        {t('dashboard.newSessionBtn')}
      </Button>
    </Box>
  );
};
export default CreateButtons;
