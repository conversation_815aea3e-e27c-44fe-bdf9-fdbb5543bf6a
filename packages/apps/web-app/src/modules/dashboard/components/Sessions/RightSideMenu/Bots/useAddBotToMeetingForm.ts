import { zodResolver } from '@hookform/resolvers/zod';
import { useMutation } from '@tanstack/react-query';
import { transcriptionsApiService } from '@waitroom/common-api';
import { useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { useTranslation } from 'react-i18next';
import validationSchema from './schema';
import { AddBotToMeetingFormValues } from './types';

export const useAddBotToMeetingForm = () => {
  const { t } = useTranslation();
  const methods = useForm<AddBotToMeetingFormValues>({
    resolver: zodResolver(validationSchema),
    defaultValues: {
      meetingLink: '',
    },
  });
  const { reset: formReset } = methods;

  const mutation = useMutation({
    mutationFn: (values: AddBotToMeetingFormValues) =>
      transcriptionsApiService.addBotToMeeting({ data: { meetingLink: values.meetingLink } }),
    onSuccess: () => {
      formReset();
    },
  });

  const { reset, isSuccess, isError } = mutation;

  useEffect(() => {
    let timeout: SetTimeout | undefined;
    if (isSuccess || isError) timeout = setTimeout(() => reset(), 6000);

    return () => {
      if (timeout) clearTimeout(timeout);
    };
  }, [reset, isSuccess, isError]);

  return { t, methods, mutation };
};
