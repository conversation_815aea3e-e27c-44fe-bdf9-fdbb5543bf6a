import { routes } from '@/constants/routes';
import { Box, Text } from '@chakra-ui/react';
import {
  selectCurrentUserOnboarding,
  selectCurrentUserSubscriptionPlan,
  useAuthStore,
} from '@waitroom/auth';
import { isFeatureEnabled } from '@waitroom/common';
import { type MeetingMemory as MM, userOnboarding } from '@waitroom/models';
import { ReactElement, useCallback } from 'react';
import { useTranslation } from 'react-i18next';
import { useNavigate } from 'react-router';
import { PromptContainer } from '../../../../ai/components/MeetingMemory/PromptContainer/PromptContainer';
import { MeetingMemoryLocationState } from '../../MeetingMemory/types';

const MeetingMemory = (): ReactElement | null => {
  const { t } = useTranslation();
  const navigate = useNavigate();
  const onboarding = useAuthStore(selectCurrentUserOnboarding);
  const subscriptionPlan = useAuthStore(selectCurrentUserSubscriptionPlan);
  const showcase = !onboarding?.[userOnboarding.tryMeetingMemory];
  const meetingMemory = isFeatureEnabled(subscriptionPlan, 'meetingMemory');

  const onSubmit = useCallback(
    (q: string, sessions?: MM.AskAIRequestBodySessions) => {
      navigate(routes.DASHBOARD.MEETING_MEMORY.link, {
        state: { q, sessions } as MeetingMemoryLocationState,
      });
    },
    [navigate],
  );

  if (!meetingMemory) return null;
  return (
    <Box bg={'gray.900'} p={[4, 6]} mx={[-4, -6, 0]} rounded={[0, 0, 'xl']} mb={6}>
      <PromptContainer
        onSubmit={onSubmit}
        pastThreads
        minimal
        spacing={2}
        suggestionsProps={{
          text: (
            <Text as="span" fontSize={'sm'}>
              {t(
                showcase
                  ? 'dashboard.meetingMemoryFirstBottomText'
                  : 'dashboard.meetingMemoryBottomText',
              )}
            </Text>
          ),
          shownLimit: 0,
        }}
        showcase={showcase}
      />
    </Box>
  );
};
export default MeetingMemory;
