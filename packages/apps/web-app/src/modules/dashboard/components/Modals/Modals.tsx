import SettingsModal from '@/modules/user/components/Settings/Modal/SettingsModal';
import { PromptModal } from '@ai/components/MeetingMemory/PromptModal/PromptModal';
import { ModalType } from '@core/components/App/Modals/types';
import { setModal, useCommonStore } from '@waitroom/common';
import { ReactElement, memo } from 'react';
import LobbySlugFormModal from './LobbySlugForm/LobbySlugFormModal';

export const Modals = memo((): ReactElement | null => {
  const { type, props, onClose } = useCommonStore.use.modal() || {};
  const internalOnClose = () => {
    setModal();
    onClose?.();
  };

  return (
    <>
      <LobbySlugFormModal
        isOpen={type === ModalType.LOBBY_SLUG_FORM}
        onClose={internalOnClose}
        closeOnOverlayClick={false}
        {...props}
      />
      <SettingsModal
        isOpen={type === ModalType.USER_SETTINGS}
        onClose={internalOnClose}
        {...props}
      />
      <PromptModal
        isOpen={type === ModalType.MEETING_MEMORY}
        onClose={internalOnClose}
        {...props}
      />
    </>
  );
});
