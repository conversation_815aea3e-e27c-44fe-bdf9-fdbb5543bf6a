import {
  <PERSON>lex,
  <PERSON>,
  <PERSON>u,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  Td,
  Text,
  Tr,
} from '@chakra-ui/react';
import { faEllipsisVertical } from '@fortawesome/pro-solid-svg-icons';
import { Icon } from '@modules/core/components/Icon/Icon';
import Confirm from '@modules/core/components/Modal/Confirm/Confirm';
import { useIntegrationDisconnect } from '@modules/integrations/hooks/useIntegrations';
import { CompatibilityTags } from '@core/components/Integrations/CompatibilityTags';
import { ProviderConnection } from '@waitroom/models';
import { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useRecallIntegration } from '../../../integrations/hooks/useRecallIntegration';
import { RecallCalendarPlatform } from '@waitroom/common-api';

export type ConnectionRowProps = {
  connection: ProviderConnection;
  category: string;
};

export const ConnectionRow = ({ connection, category }: ConnectionRowProps) => {
  const { t } = useTranslation();
  const [modalData, setModalData] = useState<
    { connection: ProviderConnection; category: string } | undefined
  >();
  const { mutate, isPending } = useIntegrationDisconnect();
  const recallIntegration = useRecallIntegration();

  return (
    <Tr opacity={isPending ? 0.6 : 1}>
      <Td minW="300px" pl={0}>
        <Flex gap={4} align="center" overflow="hidden">
          <Image
            w={12}
            h="auto"
            alt={connection.provider.label}
            src={connection.provider.icon.url}
            rounded="lg"
            backgroundColor={connection.provider.icon.backgroundColor}
          />
          <div>
            <Text fontWeight={800} fontSize="xl" mb={1}>
              {connection.provider.label}
            </Text>
            <p>
              {connection.workspace?.name ? `${connection.workspace?.name} — ` : ''}
              {connection.user?.email || ''}
            </p>
          </div>
        </Flex>
      </Td>
      <Td>
        <p>{t(`${category}`)}</p>
      </Td>
      <Td textAlign="right">
        <CompatibilityTags
          integrations={connection.provider.integrations}
          justify="right"
          w="full"
        />
      </Td>
      <Td textAlign="right" px={0}>
        <Menu placement="bottom-end">
          <MenuButton p={1}>
            <Icon icon={faEllipsisVertical} size="lg" color="gray.600" />
          </MenuButton>
          <MenuList shadow="md" border="1px" borderColor="blackAlpha.100">
            <MenuItem
              isDisabled={isPending}
              color="red.800"
              onClick={() =>
                setModalData({
                  category,
                  connection,
                })
              }
            >
              {isPending && <Spinner size="sm" mr={2} />}
              {t('integrations.disconnectAccount')}
            </MenuItem>
          </MenuList>
        </Menu>
        <Confirm
          isOpen={!!modalData}
          onClose={() => setModalData(undefined)}
          header={`${t('integrations.revokeAccess')}?`}
          body={`${t('integrations.revokeAccessDescription{connection}', {
            connection: `${modalData?.connection.user?.email || ''}`,
            provider: modalData?.connection.provider.label,
          })}?`}
          closeOnConfirm
          onConfirm={() => {
            if (!modalData) return;
            if (modalData.connection.integration.name === 'recall') {
              recallIntegration.handleDisconnectCalendar(
                modalData.connection.provider.name as RecallCalendarPlatform,
              );
            } else {
              mutate({
                params: {
                  integrationName: modalData.connection.provider.name,
                },
                category: modalData.category,
              });
            }
          }}
        />
      </Td>
    </Tr>
  );
};
