import { analyticsService } from '@modules/analytics/services';
import { useIntegrations } from '@modules/integrations/hooks/useIntegrations';
import { useRecallIntegration } from '@modules/integrations/hooks/useRecallIntegration';
import { useEffect, useMemo } from 'react';

export const useComponent = () => {
  useEffect(() => {
    analyticsService.page('INTEGRATIONS');
  }, []);

  const { providerCategories, isFetchingProviderCategories } = useIntegrations();
  const recallCalendar = useRecallIntegration();
  const connections = recallCalendar.query.data?.connections;

  const [hasConnected, hasUnconnected] = useMemo(
    () => [
      providerCategories?.some(({ connectedProviders }) => connectedProviders.length > 0) ||
        !!connections?.length,
      providerCategories?.some(({ unconnectedProviders }) => unconnectedProviders.length > 0) ||
        !connections?.length,
    ],
    [providerCategories, connections],
  );

  return {
    hasConnected,
    hasUnconnected,
    isFetchingProviderCategories,
    providerCategories: recallCalendar.providerCategory
      ? [...(providerCategories || []), recallCalendar.providerCategory]
      : providerCategories,
    recallCalendar,
  };
};
