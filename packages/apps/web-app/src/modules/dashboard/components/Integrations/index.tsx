import {
  Box,
  Container,
  Flex,
  Heading,
  SimpleGrid,
  Skeleton,
  Stack,
  Table,
  Tbody,
  Text,
  Th,
  Thead,
  Tr,
} from '@chakra-ui/react';
import { IntegrationCard } from '@modules/session/components/Card/Integration/IntegrationCard';
import UpgradeIconButton from '@modules/subscription/components/Button/Upgrade/UpgradeIconButton';
import { selectCurrentUserSubscriptionPlan, useAuthStore } from '@waitroom/auth';
import { isFreeOrFreeTrialPlan } from '@waitroom/common';
import { repeat } from '@waitroom/react-utils';
import { Fragment } from 'react';
import { useTranslation } from 'react-i18next';
import { useIsUnlistedCategory } from '../../../integrations/hooks/useIntegrations';
import { CustomPlanIconButton } from '../../../subscription/components/Button/CustomPlan/CustomPlan';
import { defaultBodyPadding } from '../Layout/Default/constants';
import Bottom from './Bottom';
import { ns } from './config';
import { ConnectionRow } from './ConnectionRow';
import { NotetakerGrid } from './Notetaker';
import { Scheduling } from './Scheduling';
import { Setup } from './Setup';
import { useComponent } from './useComponent';

const loader = repeat(
  <Stack gap={4} mb={4}>
    <Flex gap={2} alignItems="flex-end">
      <Skeleton height={14} minW={14} rounded="sm" />
      <Flex direction="column" gap={2} w="full">
        <Skeleton height={7} w="50%" />
        <Skeleton height={5} w="full" />
      </Flex>
      <Skeleton height={5} w="full" mx="6%" />
      {repeat(<Skeleton height={5} w="20%" />)}
    </Flex>
  </Stack>,
);

const IntegrationsPage = () => {
  const { t } = useTranslation();
  const cp = useComponent();

  const plan = useAuthStore(selectCurrentUserSubscriptionPlan);
  const isFreeOrTrial = isFreeOrFreeTrialPlan(plan);
  const isUnlisted = useIsUnlistedCategory();

  return (
    <Container maxW={'container.2xl'} {...defaultBodyPadding}>
      <Setup />
      <Heading as="h1" mb={12} mr={3} fontSize="5xl">
        {t('global.integrations')}
      </Heading>
      {cp.isFetchingProviderCategories && !cp.hasConnected && loader}
      {cp.hasConnected && (
        <>
          <Heading id={'active'} as="h2" fontSize="2xl" mb={6}>
            {t(`${ns}activeConnections`)}
          </Heading>
          <Box overflowX="auto">
            <Table>
              <Thead>
                <Tr fontWeight={700} color="gray.500">
                  <Th pl={0} fontSize="md" letterSpacing="normal" textTransform="none">
                    {t(`${ns}integratedPlatform`)}
                  </Th>
                  <Th fontSize="md" letterSpacing="normal" textTransform="none">
                    {t(`${ns}category`)}
                  </Th>
                  <Th fontSize="md" letterSpacing="normal" textTransform="none" textAlign="right">
                    {t(`${ns}compatibility`)}
                  </Th>
                  <Th w="36px" pr={0} />
                </Tr>
              </Thead>
              <Tbody>
                {cp.providerCategories?.map(({ connectedProviders, label, name }) =>
                  connectedProviders.map((connection, idx) =>
                    !isUnlisted(name) ? (
                      <ConnectionRow key={idx} connection={connection} category={label} />
                    ) : null,
                  ),
                )}
              </Tbody>
            </Table>
          </Box>
        </>
      )}
      <Heading as="h2" fontSize="3xl" mt={12} data-testid="inactive-providers-h2">
        {t(
          `${ns}${
            cp.hasConnected
              ? 'connectMoreIntegrations'
              : 'streamlineYourWorkByConnectingIntegrations'
          }`,
        )}
      </Heading>
      <NotetakerGrid {...cp.recallCalendar} />
      <Scheduling />
      {cp.hasUnconnected && (
        <>
          {cp.providerCategories?.map(
            ({ unconnectedProviders, label, name }) =>
              unconnectedProviders.length > 0 && (
                <Fragment key={label}>
                  <Flex gap={4} mt={10}>
                    {isUnlisted(name) ? (
                      <CustomPlanIconButton
                        as="div"
                        variant="button"
                        size="sm"
                        ml={1}
                        py={0}
                        h="auto"
                        minH="auto"
                        w="auto"
                        minW="auto"
                        colorScheme="gray.300"
                      />
                    ) : isFreeOrTrial ? (
                      <UpgradeIconButton
                        as="div"
                        variant="button"
                        size="sm"
                        ml={1}
                        py={0}
                        h="auto"
                        minH="auto"
                        w="auto"
                        minW="auto"
                        colorScheme="gray.300"
                        label={t(`pricing.upgradeIntegrationsTooltip`)}
                      />
                    ) : null}
                    <Heading id={label.toLowerCase()} as="h3" fontSize="2xl">
                      {t(label)}
                    </Heading>
                  </Flex>
                  <Text mt={2} fontSize="xl">
                    {t(`${label}Desc`)}
                  </Text>
                  <SimpleGrid
                    mt={6}
                    spacing={6}
                    gridTemplateColumns="repeat(auto-fill, minMax(300px, 1fr))"
                  >
                    {unconnectedProviders.map((provider) => (
                      <IntegrationCard
                        key={provider.name}
                        provider={provider}
                        category={label}
                        isUnlisted={isUnlisted(name)}
                      />
                    ))}
                  </SimpleGrid>
                </Fragment>
              ),
          )}
        </>
      )}
      <Bottom />
    </Container>
  );
};

export default IntegrationsPage;
