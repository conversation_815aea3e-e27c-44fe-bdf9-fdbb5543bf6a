import { CDN_IMAGES_URL, commonConfig } from '@core/config';
import { Provider } from '@waitroom/models';
import { TFunction } from 'i18next';

export const ns = 'integrations.';

const googleCalendar = `${CDN_IMAGES_URL}/google-calendar.svg`;
const outlookCalendar = `${CDN_IMAGES_URL}/outlook.png`;
export const getSchedulingProviders = (t: TFunction) => {
  return [
    {
      name: 'gCal',
      label: t(`${ns}gCal.label`),
      icon: {
        url: googleCalendar,
      },
      description: t(`${ns}gCal.desc`),
      integrations: [],
      link: commonConfig.links.googleCalendar,
    },
    {
      name: 'outlookCal',
      label: t(`${ns}outlookCal.label`),
      icon: {
        url: outlookCalendar,
      },
      description: t(`${ns}gCal.desc`),
      integrations: [],
      link: commonConfig.links.outlookCalendar,
    },
    // disabled until chrome extension is ready
    // {
    //   name: 'chromeExtension',
    //   label: t(`${ns}gChrome.label`),
    //   icon: {
    //     url: GoogleChrome,
    //   },
    //   description: t(`${ns}gChrome.desc`),
    //   integrations: [],
    //   link: config.links.chromeExtension,
    // },
  ] as (Provider & { link: string })[];
};
