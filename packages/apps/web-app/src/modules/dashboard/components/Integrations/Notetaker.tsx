import { PostHogFeatureFlagGate } from '@/modules/core/components/FeatureFlagGate/FeatureFlag';
import { <PERSON><PERSON>, Card, Heading, HStack, Image, SimpleGrid, Text, VStack } from '@chakra-ui/react';
import { useUpdateOnboarding } from '@waitroom/common';
import { PostHogFeatureFlag, userOnboarding } from '@waitroom/models';
import { useTranslation } from 'react-i18next';
import { CDN_IMAGES_URL } from '../../../core/config';
import { UseRecallIntegrationResponse } from '../../../integrations/hooks/useRecallIntegration';
import { providers } from '@core/components/Integrations/Notetaker.constants';
import { RecallCalendarPlatform } from '@waitroom/common-api';

const ns = 'integrations.recallCalendar';

export type ExtendedIntegrationCardProps = {
  provider: {
    iconUrl: string;
    name: string;
    description: string;
    supports: string[];
  };
  handleConnectCalendar: () => void;
  handleDisconnectCalendar?: (platform: RecallCalendarPlatform) => void;
  isConnected?: boolean;
};

const NotetakerIntegrationCard = ({
  provider,
  handleConnectCalendar,
  handleDisconnectCalendar,
  isConnected,
}: ExtendedIntegrationCardProps) => {
  const { iconUrl, name, description, supports } = provider;
  const { t } = useTranslation();
  const { mutation } = useUpdateOnboarding();

  return (
    <Card
      border={'2px solid'}
      borderColor={'whiteAlpha.400'}
      bgColor={'gray.900'}
      rounded={'2xl'}
      p={[4, 5, 6]}
    >
      <VStack gap={4}>
        <HStack gap={4}>
          <Image w="full" maxW={14} h="auto" alt={name} src={iconUrl} rounded="lg" />
          <Heading size="lg">{name}</Heading>
        </HStack>
        <Text>{description}</Text>
        <HStack minW="full" align="flex-start" alignItems="center" mb={2}>
          <Text fontWeight={800}>{t('global.supports')}:</Text>
          {supports.map((support) => (
            <Image key={support} src={support} alt={support} w={8} m={1} />
          ))}
        </HStack>
        <Button
          size="sm"
          fontSize="md"
          variant="outline"
          colorScheme="gray.900"
          w="full"
          isLoading={mutation.isPending}
          onClick={async () => {
            await mutation.mutateAsync({ [userOnboarding.noteTaker]: true });
            return isConnected
              ? handleDisconnectCalendar?.(name as RecallCalendarPlatform)
              : handleConnectCalendar();
          }}
          mt="auto"
        >
          {isConnected ? t(`${ns}.disconnect`) : t(`${ns}.connect`)}
        </Button>
      </VStack>
    </Card>
  );
};

export type NotetakerGridProps = UseRecallIntegrationResponse;

export const NotetakerGrid = ({
  query,
  handleConnectCalendar,
  handleDisconnectCalendar,
  handleConnectMicrosoftCalendar,
}: NotetakerGridProps) => {
  const { t } = useTranslation();
  const connections = query.data?.connections;
  const isGoogleConnected = !!connections?.some(
    (connection) => connection.platform === 'google' && connection.connected,
  );
  const isMicrosoftConnected = !!connections?.some(
    (connection) => connection.platform === 'microsoft' && connection.connected,
  );

  return (
    <PostHogFeatureFlagGate flag={PostHogFeatureFlag.RecallCalendarIntegration}>
      <Heading as="h3" id={'notetaker'} fontSize="2xl" mt={10}>
        {t(`${ns}.title`)}
      </Heading>
      <Text mt={2} fontSize="xl">
        {t(`${ns}.description`)}
      </Text>

      <SimpleGrid mt={6} spacing={6} gridTemplateColumns="repeat(auto-fill, minMax(300px, 1fr))">
        {!isGoogleConnected && (
          <NotetakerIntegrationCard
            provider={{
              iconUrl: `${CDN_IMAGES_URL}/google-calendar.svg`,
              name: t(`${ns}.googlePrompt`),
              description: t(`${ns}.googlePromptDesc`),
              supports: Object.values(providers),
            }}
            handleConnectCalendar={handleConnectCalendar}
            handleDisconnectCalendar={() => handleDisconnectCalendar('google')}
          />
        )}
        <PostHogFeatureFlagGate flag={PostHogFeatureFlag.OutlookBotIntegration}>
          {!isMicrosoftConnected && (
            <NotetakerIntegrationCard
              provider={{
                iconUrl: `${CDN_IMAGES_URL}/microsoft-calendar.png`,
                name: t(`${ns}.microsoftPrompt`),
                description: t(`${ns}.microsoftPromptDesc`),
                supports: Object.values(providers),
              }}
              handleConnectCalendar={handleConnectMicrosoftCalendar}
              handleDisconnectCalendar={() => handleDisconnectCalendar('microsoft')}
            />
          )}
        </PostHogFeatureFlagGate>
      </SimpleGrid>
    </PostHogFeatureFlagGate>
  );
};
