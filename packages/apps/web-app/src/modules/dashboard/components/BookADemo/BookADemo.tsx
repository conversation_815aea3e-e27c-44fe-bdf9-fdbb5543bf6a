import { Box, Button, CloseButton, Container, Flex, FlexProps, Image } from '@chakra-ui/react';
import { CDN_IMAGES_URL, commonConfig } from '@core/config';
import { Trans, useTranslation } from 'react-i18next';
import { updateAppState, useAppStore } from '../../../core/store/store';

const ns = 'dashboard.';

export type BookADemoProps = FlexProps;

export const BookADemo = ({ ...rest }: BookADemoProps) => {
  const { t } = useTranslation();
  const hideBookDemo = useAppStore.use.hideBookDemo();

  if (hideBookDemo) return null;
  return (
    <Box position="fixed" bottom={0} left={0} right={0} pointerEvents="none" zIndex={'popover'}>
      <Container maxW="container.lg" py={6} pointerEvents="auto">
        <Flex
          py={5}
          pl={8}
          pr={10}
          rounded="2xl"
          borderWidth="2px"
          bgColor="t.white-80"
          color={'gray.900'}
          transition="background .2s ease-out"
          _hover={{ bgColor: 'white' }}
          position="relative"
          shadow="lg"
          gap={4}
          direction={{ base: 'column', md: 'row' }}
          alignItems="center"
          {...rest}
        >
          <CloseButton
            position="absolute"
            top={2}
            right={2}
            size="sm"
            data-testid="btn-close"
            onClick={() => {
              updateAppState({
                hideBookDemo: true,
              });
            }}
          />
          <p>
            <Trans i18nKey={`${ns}bookDemo`} />
          </p>
          <Button
            as="a"
            href={commonConfig.links.onboarding}
            target="_blank"
            rel="noopener noreferrer"
            leftIcon={
              <Image w={6} h={6} src={`${CDN_IMAGES_URL}/demo-avatar.png`} rounded={'sm'} />
            }
            variant="outline"
            color="gray.900"
            flexShrink={0}
            px={['4!', '8!', '12!']}
            size={['xs', 'sm', 'def']}
            data-testid="btn-book"
          >
            {t('dashboard.findATimeToMeet')}
          </Button>
        </Flex>
      </Container>
    </Box>
  );
};
