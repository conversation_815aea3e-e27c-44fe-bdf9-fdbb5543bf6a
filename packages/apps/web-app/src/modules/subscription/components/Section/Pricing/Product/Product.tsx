import { Badge, Box, BoxProps, Button, Flex, Heading, Text } from '@chakra-ui/react';
import { Icon } from '@core/components/Icon/Icon';
import { checkedListSx } from '@core/components/List/Checked/CheckedList';
import { commonConfig } from '@core/config';
import { currencyFormatter } from '@core/utils/currency';
import { faBullseyeArrow, faGem } from '@fortawesome/pro-solid-svg-icons';
import {
  getPrice,
  isFreeTrialPlan,
  isFreeType,
  isTrialPlan,
  planFeatures,
  usdDivisor,
} from '@waitroom/common';
import { SubscriptionPlan, SubscriptionPlanType, UserSubscriptionPlan } from '@waitroom/models';
import { ReactElement } from 'react';
import { Trans, useTranslation } from 'react-i18next';
import { Link } from 'react-router-dom';
import { routes } from '../../../../../../constants/routes';
import { getFeatureTrans } from '../../../../utils/locale';
import { featureItem } from '../Pricing.utils';
import { badgeSx, headingSx, priceSx, subtextSx, wrapperSx } from './Product.styles';

export interface ProductProps extends BoxProps {
  data: SubscriptionPlan;
  annual?: boolean;
  currentPlan?: UserSubscriptionPlan;
  isTrial?: boolean;
}

const ns = 'pricing.table.';
const bestPlan: SubscriptionPlanType = 'premium';

const Product = ({ data, annual, currentPlan, ...rest }: ProductProps): ReactElement | null => {
  const { t } = useTranslation();
  const type = data.config?.paddleProductName ?? 'free';
  const typeNs = `${ns}${type}.`;
  const isBest = type === bestPlan;
  const isFree = type === 'free';
  const bodyText = t([`${typeNs}body`], '');
  const monthlyPrice = getPrice(data, 'month')?.unitPrice;
  const annualPrice = getPrice(data, 'year')?.unitPrice;
  const hasPrice = type !== 'enterprise';
  const price = annual ? annualPrice : monthlyPrice;
  const saving =
    hasPrice && monthlyPrice && annualPrice
      ? (Number(monthlyPrice?.amount || 0) * 12 - Number(annualPrice?.amount || 0)) / usdDivisor
      : undefined;
  const features = planFeatures[type];
  const currentType = currentPlan?.planConfig?.paddleProductName;
  const currentTrial = isTrialPlan(currentPlan);

  if (!type) return null;
  return (
    <Box
      sx={wrapperSx}
      border={isBest ? '3px solid' : ''}
      bgColor={isBest ? undefined : 'red.50'}
      _dark={{ bgColor: isBest ? undefined : 'whiteAlpha.100' }}
      {...rest}
    >
      {isBest && <Badge sx={badgeSx}>{t(`${ns}bestBadge`)}</Badge>}
      <Box minH={6} mb={2}>
        {!!saving && (
          <Badge colorScheme="green" textTransform="none">
            {t('pricing.saveAmount', {
              amount: currencyFormatter(price?.currencyCode, undefined, {
                minimumFractionDigits: 0,
                maximumFractionDigits: 0,
              }).format(saving),
            })}
          </Badge>
        )}
      </Box>
      <Heading as="h2" size="3xl" sx={headingSx}>
        <Trans
          i18nKey={`${typeNs}title`}
          components={{
            gem: <Icon icon={faGem} color="yellow.400" mr={1} />,
            target: <Icon icon={faBullseyeArrow} color="#D6CB9E" mr={1} />,
          }}
        />
      </Heading>
      <Text sx={priceSx}>
        {t(`${typeNs}price`, {
          price: currencyFormatter(price?.currencyCode).format(
            Number(price?.amount || 0) / usdDivisor / (annual ? 12 : 1),
          ),
        })}
      </Text>
      <Text sx={subtextSx}>
        <Trans
          i18nKey={[`${typeNs}subtext${annual ? 'Annual' : 'Monthly'}`, `${typeNs}subtext`]}
          values={{
            price: currencyFormatter(price?.currencyCode).format(
              Number(annualPrice?.amount || 0) / usdDivisor,
            ),
          }}
        />
      </Text>
      {isFree && (currentTrial || (currentType && isFreeType(currentType))) ? (
        <Text color="red.800" fontSize="xl" fontWeight="bold">
          {t(`${typeNs}${currentTrial ? 'trialText' : 'freeText'}`)}
        </Text>
      ) : currentType !== type || isFreeTrialPlan(currentPlan) ? (
        hasPrice ? (
          <>
            <Button
              as={Link}
              to={isFree ? routes.HOST : `${routes.CHECKOUT}?type=${type}&monthly=${!annual}`}
              variant={isFree ? 'outline' : 'solid'}
              colorScheme={isFree ? 'gray.900' : 'red'}
              w="full"
            >
              {t(`${typeNs}${currentType ? 'buttonAlt' : 'button'}`)}
            </Button>
            <Text mt={4} mb={6} fontSize="sm" fontWeight="bold">
              {t(`${typeNs}subButton`)}
            </Text>
          </>
        ) : (
          <>
            <Button
              as="a"
              href={`mailto:${commonConfig.company.emails.sales}`}
              target="_blank"
              rel="noreferrer noopener"
              variant="outline"
              colorScheme="gray.900"
              w="full"
            >
              {t(`${typeNs}${currentType ? 'buttonAlt' : 'button'}`)}
            </Button>
            <Text mt={4} mb={8} fontSize="sm" fontWeight="bold">
              {t(`${typeNs}subButton`)}
            </Text>
          </>
        )
      ) : null}
      {!!features?.length && (
        <Flex direction={'column'} gap={6} mt={10}>
          <Trans i18nKey={`${typeNs}featuresTitle`} />
          <Flex as="ul" sx={checkedListSx}>
            {features.map((key) => {
              const transArr = data.config ? getFeatureTrans(key, data.config) : undefined;
              return featureItem(transArr, t);
            })}
          </Flex>
        </Flex>
      )}
      {!!bodyText?.length && <Text mt={8}>{t(`${typeNs}body`)}</Text>}
    </Box>
  );
};
export default Product;
