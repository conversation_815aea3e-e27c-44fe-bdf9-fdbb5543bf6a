import { render } from '@testing-library/react';
import { faker } from '@waitroom/tests';
import { afterEach, describe, expect, it, vi } from 'vitest';
import { withAll } from '../../../../../../../tests/helpers/storeProviders';
import { Suggestions } from './Suggestions';

vi.mock('./Card', () => ({
  Card: () => <div data-testid="card">Card</div>,
}));
vi.mock('./Loader', () => ({
  Loader: () => <div data-testid="loader">Loader</div>,
}));

describe('Suggestions', () => {
  afterEach(() => {
    vi.clearAllMocks();
  });

  it('renders nothing when suggestions are empty', () => {
    const { queryByTestId } = render(
      withAll(<Suggestions suggestions={[]} isLoading={false} onClick={vi.fn()} />),
    );

    expect(queryByTestId('loader')).not.toBeInTheDocument();
    expect(queryByTestId('card')).not.toBeInTheDocument();
  });

  it('shows loader when loading', () => {
    const { getByTestId } = render(
      withAll(<Suggestions suggestions={[]} isLoading={true} onClick={vi.fn()} />),
    );

    expect(getByTestId('loader')).toBeInTheDocument();
  });

  it('renders suggestions when present', () => {
    const suggestions = Array.from({ length: 2 }, () =>
      faker.meetingMemory.personalizedSuggestion(),
    );

    const { getAllByTestId, queryByTestId } = render(
      <Suggestions suggestions={suggestions} isLoading={false} onClick={vi.fn()} />,
    );

    expect(getAllByTestId('card')).toHaveLength(2);
    expect(queryByTestId('loader')).not.toBeInTheDocument();
  });
});
