import { Button } from '@chakra-ui/react';

export type SuggestionProps = {
  children: string;
  onClick: (value: string) => void;
};

export const Suggestion = ({ children, onClick }: SuggestionProps) => (
  <Button
    size={'2xs'}
    variant={'outline'}
    colorScheme={'red.700'}
    color={'white'}
    display={'inline-block'}
    px={3}
    fontSize={'sm'}
    fontWeight={'normal'}
    onClick={() => onClick(children)}
    maxW={'full'}
    textOverflow={'ellipsis'}
    overflow={'hidden'}
    title={children}
  >
    {children}
  </Button>
);
