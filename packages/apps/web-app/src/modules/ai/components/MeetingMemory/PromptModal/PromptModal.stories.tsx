import { StorybookProvidersDecorator } from '@/stories/utils/Providers';
import { Meta, StoryObj } from '@storybook/react';
import { PromptModal } from './PromptModal';

const meta: Meta<typeof PromptModal> = {
  title: 'web-app/components/MeetingMemory/PromptModal',
  component: PromptModal,
  tags: ['autodocs'],
  decorators: [StorybookProvidersDecorator],
};

export default meta;
type Story = StoryObj<typeof PromptModal>;

export const Template: Story = {
  args: {
    isOpen: true,
    onClose: () => undefined,
  },
};
