import { Box, Button, Flex, Heading, Stack, Text } from '@chakra-ui/react';
import AutoScroll from '@core/components/AutoScroll/AutoScroll';
import { Icon } from '@core/components/Icon/Icon';
import { scrollToWindowBottom } from '@core/utils/dom';
import { faRectangleVerticalHistory } from '@fortawesome/pro-regular-svg-icons';
import { faPlus, faSparkles } from '@fortawesome/pro-solid-svg-icons';
import { memo, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { Link } from 'react-router-dom';
import { routes } from '../../../../../constants/routes';
import MessageList, { MessageListProps } from './MessageList/MessageList';

export type ConversationProps = MessageListProps & {
  onPastThreads?: () => void;
  onNewThread?: () => void;
};
export const Conversation = memo(
  ({ onNewThread, onPastThreads, threadId, messages, cacheId }: ConversationProps) => {
    const { t } = useTranslation('meetingMemory');

    // scroll instantly to bottom when switching threads or new messages
    const len = messages?.length;
    useEffect(() => {
      if (threadId && len) scrollToWindowBottom();
    }, [threadId, len]);

    return (
      <Box position="relative">
        <Box position="sticky" top={'60px'} transition="all 0.3s" zIndex={2}>
          <Flex pos="absolute" top={0} right={0} gap={2}>
            <Button
              variant="outline"
              colorScheme="gray.800"
              bgColor={'gray.1000'}
              size="2xs"
              shadow="md"
              title={t('pastThreads')}
              onClick={onPastThreads}
            >
              <Icon icon={faRectangleVerticalHistory} fontSize={{ base: 'lg', md: 'sm' }} />
              <Text as="span" display={{ base: 'none', md: 'inline-flex' }} ml={2}>
                {t('pastThreads')}
              </Text>
            </Button>
            <Button
              as={Link}
              to={routes.DASHBOARD.MEETING_MEMORY.link}
              variant="outline"
              colorScheme="gray.800"
              bgColor={'gray.1000'}
              size="2xs"
              shadow="md"
              title={t('newThread')}
              onClick={onNewThread}
            >
              <Icon icon={faPlus} fontSize={{ base: 'lg', md: 'sm' }} />
              <Text as="span" display={{ base: 'none', md: 'inline-flex' }} ml={2}>
                {t('newThread')}
              </Text>
            </Button>
          </Flex>
        </Box>
        <AutoScroll
          as={Stack}
          target={window}
          sx={{
            gap: 6,
          }}
          behavior="instant"
        >
          <Flex justifyContent="space-between" alignItems="center" minH={10} mb={2}>
            <Heading fontSize="xl" pr={24}>
              <Icon icon={faSparkles} color="red.500" size="lg" mr={2} />
              {t('promptModal.title')}
            </Heading>
          </Flex>
          <MessageList threadId={threadId} messages={messages} cacheId={cacheId} />
        </AutoScroll>
      </Box>
    );
  },
);
