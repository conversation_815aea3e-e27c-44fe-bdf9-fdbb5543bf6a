import { Box, Flex, IconButton, Text, Textarea } from '@chakra-ui/react';
import { Icon } from '@core/components/Icon/Icon';
import { faArrowRight } from '@fortawesome/pro-solid-svg-icons';
import { LexicalComposer } from '@lexical/react/LexicalComposer';
import { ContentEditable } from '@lexical/react/LexicalContentEditable';
import { LexicalErrorBoundary } from '@lexical/react/LexicalErrorBoundary';
import { OnChangePlugin } from '@lexical/react/LexicalOnChangePlugin';
import { PlainTextPlugin } from '@lexical/react/LexicalPlainTextPlugin';
import { useInfiniteQuery } from '@tanstack/react-query';
import { selectAuthUserId, useAuthStore } from '@waitroom/auth';
import {
  getInfinityRequestData,
  inactiveOptions,
  meetingMemorySessionsQuery,
} from '@waitroom/react-query';
import { memo, useRef } from 'react';
import { buildEditorConfig } from './config';
import MentionPlugin from './MentionPlugin';
import { SpeechToTextButton } from './SpeechToTextButton/SpeechToTextButton';
import { editorSx, editorWrapperSx } from './styles';
import { PromptInitialValues, PromptProps } from './types';
import { usePrompt } from './usePrompt';

export const Prompt = ({ sessions, isPending, isError, ...rest }: PromptProps) => {
  const { enabled, onSubmit, onTextChange, onSpeechChange } = usePrompt(rest);
  const editorRef = useRef<HTMLTextAreaElement>(null);

  return (
    <Flex
      direction={['column', null, 'row']}
      alignItems={[null, null, 'flex-end']}
      w={'full'}
      gap={2}
    >
      <Box
        position={'relative'}
        flexGrow={1}
        zIndex={1}
        bgColor={rest.bg || 'white'}
        rounded={'md'}
        sx={editorWrapperSx}
      >
        <PlainTextPlugin
          contentEditable={
            <Textarea
              as={ContentEditable}
              p={4}
              w="auto"
              rounded={'none'}
              h={'full'}
              maxH={150}
              minH={16}
              overflow={'auto'}
              color={'gray.700'}
              fontSize={'md'}
              autoFocus
              border="none"
              ref={editorRef}
              sx={editorSx}
            />
          }
          ErrorBoundary={LexicalErrorBoundary}
          placeholder={
            <Text
              marginTop={-16} // push the text up to the first line of the editor
              color={rest.showcase ? 'gray.700' : 'gray.400'}
              zIndex={-1}
              p={4}
              cursor="text"
              onClick={() => {
                editorRef.current?.focus();
              }}
            >
              {rest.placeholder}
            </Text>
          }
        />
        <Textarea as={Box} border="none" minH={0} height={'auto'} p={6}>
          <OnChangePlugin onChange={onTextChange} />
          <Flex position={'absolute'} right={2} bottom={2} alignItems={'flex-end'} gap={2}>
            {!isError && <MentionPlugin data={sessions} isPending={isPending} />}
            <SpeechToTextButton onChange={onSpeechChange} />
            <IconButton
              aria-label="Go"
              title="Go (Enter)"
              colorScheme={'red'}
              size={'xs'}
              flexShrink={0}
              mt={1}
              onClick={onSubmit}
              isDisabled={(!enabled || rest.disabled) && !rest.showcase}
              _disabled={{
                bgColor: 'gray.300',
                _hover: { bgColor: 'gray.300' },
              }}
            >
              <Icon icon={faArrowRight} size="lg" />
            </IconButton>
          </Flex>
        </Textarea>
      </Box>
    </Flex>
  );
};

const PromptWrapper = memo((props: PromptProps & PromptInitialValues) => {
  const config = useRef(buildEditorConfig(props));
  const userId = useAuthStore(selectAuthUserId);
  const { data, isPending, isError } = useInfiniteQuery({
    ...meetingMemorySessionsQuery(userId, { limit: 500 }),
    ...inactiveOptions['3m'],
  });
  const sessions = getInfinityRequestData(data)?.sessions;
  return (
    <LexicalComposer initialConfig={config.current}>
      <Prompt {...props} sessions={sessions} isPending={isPending} isError={isError} />
    </LexicalComposer>
  );
});

export default PromptWrapper;
