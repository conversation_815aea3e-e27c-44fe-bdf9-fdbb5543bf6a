import { <PERSON>a, StoryObj } from '@storybook/react';
import { StorybookProvidersDecorator } from '../../../../../../../stories/utils/Providers';
import { Loader } from './Loader';

const meta: Meta<typeof Loader> = {
  title: 'web-app/components/MeetingMemory/SuggestionLoader',
  component: Loader,
  tags: ['autodocs'],
  decorators: [StorybookProvidersDecorator],
  parameters: {
    layout: 'padded',
  },
};

export default meta;
type Story = StoryObj<typeof Loader>;

export const Default: Story = {
  render: () => <Loader />,
};
