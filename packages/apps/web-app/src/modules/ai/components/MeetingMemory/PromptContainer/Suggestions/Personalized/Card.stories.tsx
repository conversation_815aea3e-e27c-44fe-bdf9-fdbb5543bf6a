import { <PERSON>a, StoryObj } from '@storybook/react';
import { MeetingMemory } from '@waitroom/models';
import { StorybookProvidersDecorator } from '../../../../../../../stories/utils/Providers';
import { Card } from './Card';

const meta: Meta<typeof Card> = {
  title: 'web-app/components/MeetingMemory/SuggestionCard',
  component: Card,
  tags: ['autodocs'],
  decorators: [StorybookProvidersDecorator],
  parameters: {
    layout: 'padded',
  },
  argTypes: {
    onClick: { action: 'clicked' },
  },
};

export default meta;
type Story = StoryObj<typeof Card>;

// Mock PersonalizedSuggestion objects
const mockPersonalizedSuggestion: MeetingMemory.PersonalizedSuggestion = {
  id: '1',
  content:
    'Review my communication during the meeting by examining my strengths, areas for improvement, and next steps.',
  category: 'feedback',
  isPersonal: true,
  createdAt: Date.now(),
};

const longPersonalizedSuggestion: MeetingMemory.PersonalizedSuggestion = {
  id: '2',
  content:
    'List the immediate setup tasks and who they were assigned to in the Pixel / Rumi Check-in Session, including any dependencies and deadlines mentioned.',
  category: 'action',
  isPersonal: false,
  createdAt: Date.now(),
};

export const Default: Story = {
  args: {
    suggestion: mockPersonalizedSuggestion,
    onClick: (suggestion: string) => console.info('Clicked:', suggestion),
  },
};

export const LongText: Story = {
  args: {
    suggestion: longPersonalizedSuggestion,
    onClick: (suggestion: string) => console.info('Clicked:', suggestion),
  },
};

export const MultipleCards: Story = {
  render: (args) => (
    <div
      style={{
        display: 'flex',
        gap: '1rem',
      }}
    >
      <Card
        {...args}
        suggestion={{
          id: '3',
          content: 'Summarize the main decisions made in the meeting.',
          category: 'summary',
          isPersonal: false,
          createdAt: Date.now(),
        }}
      />
      <Card {...args} suggestion={mockPersonalizedSuggestion} />
      <Card
        {...args}
        suggestion={{
          id: '4',
          content: 'Find action items assigned to me.',
          category: 'action',
          isPersonal: true,
          createdAt: Date.now(),
        }}
      />
      <Card {...args} suggestion={longPersonalizedSuggestion} />
    </div>
  ),
  args: {
    onClick: (suggestion: string) => console.info('Clicked:', suggestion),
  },
  parameters: {
    docs: {
      description: {
        story: 'Multiple cards displayed in a grid layout to show various configurations.',
      },
    },
  },
};
