import { Box, Flex, FlexProps, SkeletonText, SkeletonTextProps, Text } from '@chakra-ui/react';
import { Trans, useTranslation } from 'react-i18next';

const skeletonProps: SkeletonTextProps = {
  noOfLines: 3,
  borderWidth: 'thin',
  borderColor: 'orange.500',
  bg: 't.gray-800-50',
  rounded: 'md',
  px: 4,
  py: 8,
  w: { base: 'full', md: '33%' },
  lineHeight: '1.9',
};

type LoaderProps = FlexProps & {
  isCompact?: boolean;
};

export const Loader = ({ isCompact, ...props }: LoaderProps) => {
  const { t } = useTranslation('meetingMemory');
  return (
    <Box mt={isCompact ? 3 : 0}>
      <Text color={'gray.300'} fontSize={'sm'} ml={3}>
        <Trans t={t} i18nKey="suggestionsLoading" components={{ strong: <strong /> }} />
      </Text>
      <Flex
        mt={3}
        gap={4}
        direction={isCompact ? 'column' : { base: 'column', md: 'row' }}
        {...props}
      >
        {isCompact ? (
          <SkeletonText noOfLines={3} />
        ) : (
          <>
            <SkeletonText {...skeletonProps} />
            <SkeletonText {...skeletonProps} borderColor="red.500" />
            <SkeletonText {...skeletonProps} />
          </>
        )}
      </Flex>
    </Box>
  );
};
