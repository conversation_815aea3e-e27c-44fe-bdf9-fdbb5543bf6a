import { Box } from '@chakra-ui/react';
import { virtualizerSx } from '@core/components/VirtualList/VirtualList';
import { useVirtualizer } from '@tanstack/react-virtual';
import { MouseEvent, ReactElement, RefObject } from 'react';
import MenuItem, { MentionOption } from '../Item/MenuItem';
import type { SelectedOptions } from '../types';

export type VirtualListProps = {
  data: MentionOption[];
  parentRef: RefObject<HTMLDivElement | null>;
  selectedIndex?: number | null;
  selectedOptions: SelectedOptions;
  setHighlightedIndex: (index: number) => void;
  onClick: (option: MentionOption, index: number, ev: MouseEvent) => void;
  onSelect: (option: MentionOption, on: boolean) => void;
};

const VirtualList = ({
  data,
  parentRef,
  selectedIndex,
  setHighlightedIndex,
  onClick,
  selectedOptions,
  onSelect,
}: VirtualListProps): ReactElement => {
  const len = data?.length || 0;

  const virtualizer = useVirtualizer({
    count: len,
    getScrollElement: () => parentRef.current,
    estimateSize: () => 58,
    overscan: 6,
    gap: 0,
  });
  const items = virtualizer.getVirtualItems();

  return (
    <Box sx={virtualizerSx.containerSx} h={`${virtualizer.getTotalSize()}px`}>
      <Box
        sx={virtualizerSx.listSx}
        transform={`translateY(${(items[0]?.start || 0) - (virtualizer.options.scrollMargin || 0)}px)`}
      >
        {virtualizer.getVirtualItems().map((v) => {
          const option = data[v.index];
          return (
            <div key={v.key} data-index={v.index} ref={virtualizer.measureElement}>
              <MenuItem
                key={option.key}
                isHighlighted={selectedIndex === v.index}
                isSelected={!!selectedOptions[option.key]}
                onClick={(ev) => onClick(option, v.index, ev)}
                onMouseEnter={() => {
                  setHighlightedIndex(v.index);
                }}
                onSelect={onSelect}
                option={option}
              />
            </div>
          );
        })}
      </Box>
    </Box>
  );
};
export default VirtualList;
