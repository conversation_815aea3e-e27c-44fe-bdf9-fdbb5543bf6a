import { Box, IconButton, Tooltip } from '@chakra-ui/react';
import { ModalType } from '@core/components/App/Modals/types';
import { Icon } from '@core/components/Icon/Icon';
import { useTextToSpeechStorage } from '@core/hooks/useTextToSpeech';
import { faCog, faPause, faPlay } from '@fortawesome/pro-solid-svg-icons';
import { analyticsService } from '@modules/analytics/services';
import { EventType } from '@waitroom/analytics';
import { setModal } from '@waitroom/common';
import { ReactElement, RefObject, useCallback } from 'react';
import { useTranslation } from 'react-i18next';
import { buttonStylesDark, buttonStylesSx } from '../styles';

export type TextToSpeechProps = {
  contentRef: RefObject<HTMLDivElement | null> | null;
  text: string;
};

const TextToSpeech = ({ contentRef, text }: TextToSpeechProps): ReactElement | null => {
  const { t } = useTranslation();
  const getText = useCallback(() => contentRef?.current?.innerText || text, [contentRef, text]);
  const { play, pause, paused } = useTextToSpeechStorage({
    text: getText,
  });

  return (
    <Box
      position="relative"
      sx={{
        p: 1,
        ml: -1,
        '.settings-button': {
          opacity: 0,
          transition: 'all 0.2s ease-in-out',
          transform: 'translateX(-100%)',
        },
        _hover: {
          bg: 'gray.100',
          rounded: 'full',
          '.settings-button': {
            opacity: 1,
            transform: 'translateX(0)',
          },
        },
        _dark: {
          _hover: {
            bg: 'gray.900',
          },
        },
      }}
    >
      <Box display="flex" alignItems="center" gap={1}>
        <Tooltip label={t('global.narrate')}>
          <IconButton
            sx={buttonStylesSx}
            _dark={buttonStylesDark}
            aria-label={t('global.narrate')}
            zIndex={2}
            onClick={() => {
              if (paused) {
                play();
                analyticsService.track(EventType.MeetingMemoryMessageAudioPlay, {});
                return;
              } else {
                pause();
              }
            }}
          >
            <Icon icon={paused ? faPlay : faPause} />
          </IconButton>
        </Tooltip>
        <Tooltip label={t('global.settings')}>
          <IconButton
            onClick={() => setModal({ type: ModalType.USER_SETTINGS })}
            zIndex={1}
            className="settings-button"
            sx={buttonStylesSx}
            aria-label={t('global.settings')}
          >
            <Icon icon={faCog} />
          </IconButton>
        </Tooltip>
      </Box>
    </Box>
  );
};
export default TextToSpeech;
