import { routes } from '@/constants/routes';
import { MeetingMemoryLocationState } from '@/modules/dashboard/components/MeetingMemory/types';
import Modal from '@core/components/Modal/Modal';
import { MeetingMemory } from '@waitroom/models';
import { memo, useCallback } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import { PromptProps } from '../PromptContainer/Prompt/types';
import { PromptContainer } from '../PromptContainer/PromptContainer';

export type PromptModalProps = {
  isOpen: boolean;
  onClose: () => void;
  sessions?: MeetingMemory.AskAIRequestBodySessions;
};

export const PromptModal = memo(({ isOpen, onClose, sessions }: PromptModalProps) => {
  const navigate = useNavigate();
  const { id } = useParams<{ id?: string }>();

  const onSubmit = useCallback<PromptProps['onSubmit']>(
    (value, s) => {
      navigate(routes.DASHBOARD.MEETING_MEMORY.link, {
        state: {
          q: value,
          sessions: s || sessions ? [...(sessions || []), ...(s || [])] : undefined,
        } as MeetingMemoryLocationState,
      });
      onClose();
    },
    [navigate, onClose, sessions],
  );

  return (
    <Modal size="3xl" isOpen={isOpen} onClose={onClose} closeOnEsc closeOnOverlayClick>
      <Modal.Body>
        <PromptContainer
          onSubmit={onSubmit}
          onThreadClick={onClose}
          pastThreads
          selectedThread={id}
        />
      </Modal.Body>
    </Modal>
  );
});
