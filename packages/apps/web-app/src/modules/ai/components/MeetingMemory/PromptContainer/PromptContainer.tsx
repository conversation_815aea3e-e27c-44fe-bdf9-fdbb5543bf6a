import {
  Box,
  Button,
  Flex,
  <PERSON>ing,
  <PERSON>ack,
  StackProps,
  Tooltip,
  useBoolean,
  useDisclosure,
} from '@chakra-ui/react';
import { Icon, IconBox } from '@core/components/Icon/Icon';
import { faRectangleVerticalHistory } from '@fortawesome/pro-regular-svg-icons';
import { faInfo, faSparkles } from '@fortawesome/pro-solid-svg-icons';
import { MeetingMemory } from '@waitroom/models';
import { getQueryRequestData } from '@waitroom/react-query';
import { memo, useCallback } from 'react';
import { useTranslation } from 'react-i18next';
import Threads from '../../../../dashboard/components/MeetingMemory/Threads/Threads';
import { useThreads } from '../../../../dashboard/components/MeetingMemory/Threads/useThreads';
import Prompt from './Prompt/Prompt';
import { PromptProps } from './Prompt/types';
import { Suggestions, SuggestionsProps } from './Suggestions/Suggestions';

export type PromptContainerProps = Omit<StackProps, 'onSubmit'> & {
  onSubmit: PromptProps['onSubmit'];
  onThreadClick?: () => void;
  disabled?: boolean;
  pastThreads?: boolean;
  selectedThread?: MeetingMemory.Thread['id'];
  heading?: boolean;
  autoFocus?: boolean;
  placeholder?: string;
  suggestionsProps?: Partial<SuggestionsProps>;
  minimal?: boolean;
  showcase?: boolean;
};

export const PromptContainer = memo(
  ({
    onSubmit,
    onThreadClick,
    disabled = false,
    pastThreads,
    selectedThread,
    heading = true,
    placeholder,
    autoFocus = true,
    minimal,
    suggestionsProps,
    showcase,
    ...props
  }: PromptContainerProps) => {
    const { t } = useTranslation('meetingMemory');
    const threadsQuery = useThreads({ params: { limit: 30 } });
    const hasThreads = !!getQueryRequestData(threadsQuery)?.threads?.length;
    const [threadsVisible, { toggle, on }] = useBoolean();
    const { isOpen, onClose, onToggle } = useDisclosure();
    const onSubmitInner = useCallback<PromptProps['onSubmit']>(
      (value, sessions) => {
        onClose();
        onSubmit(value, sessions);
      },
      [onClose, onSubmit],
    );

    return (
      <>
        <Stack spacing={6} {...props}>
          {!!heading && (
            <Heading fontSize={{ base: 'sm', sm: 'md' }} lineHeight={2}>
              <Icon icon={faSparkles} color="red.500" size="lg" mr={2} />
              {t('promptModal.title')}
              <Tooltip label={t(`promptTooltip`)}>
                <Box display="inline-flex" ml={2}>
                  <IconBox rounded="full" size="5xs" bg="blackAlpha.300" color="inherit">
                    <Icon icon={faInfo} fontSize="xs" />
                  </IconBox>
                </Box>
              </Tooltip>
            </Heading>
          )}
          <Prompt
            placeholder={
              placeholder || (showcase ? t(`showcasePlaceholder`) : t(`inputPlaceholder`))
            }
            showcasePrompt={t(`showcasePrompt`)}
            onSubmit={onSubmitInner}
            disabled={disabled}
            autoFocus={autoFocus}
            showcase={showcase}
          />
          <Flex align={'center'} wrap={'wrap'} gap={2} maxW={'full'}>
            <Suggestions
              isOpen={isOpen}
              onToggle={onToggle}
              onClick={onSubmitInner}
              {...suggestionsProps}
            />
            {minimal && !!pastThreads && hasThreads && (
              <Button
                size={'2xs'}
                variant={'outline'}
                px={3}
                colorScheme={'gray.800'}
                borderColor={'gray.200'}
                fontSize={'sm'}
                fontWeight={'normal'}
                onClick={toggle}
              >
                {t('pastThreads')}
              </Button>
            )}
          </Flex>
        </Stack>
        {!!pastThreads &&
          hasThreads &&
          (threadsVisible ? (
            <Threads
              wrapperProps={{ mt: 6 }}
              onClick={onThreadClick}
              mb={1}
              selected={selectedThread}
            />
          ) : !minimal ? (
            <Button
              onClick={on}
              colorScheme={'gray.900'}
              variant={'outline'}
              size={'xs'}
              mt={6}
              leftIcon={<Icon icon={faRectangleVerticalHistory} />}
            >
              {t('seePastThreads')}
            </Button>
          ) : null)}
      </>
    );
  },
);
