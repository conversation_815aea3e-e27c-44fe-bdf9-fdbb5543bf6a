import { Box, Flex, FlexProps, Text } from '@chakra-ui/react';
import { Trans, useTranslation } from 'react-i18next';
import { Card } from './Card';
import { Loader } from './Loader';
import { MeetingMemory } from '@waitroom/models';

const borderColors = {
  odd: 'orange.500',
  even: 'red.500',
};

type SuggestionsProps = Omit<FlexProps, 'onClick'> & {
  suggestions: MeetingMemory.PersonalizedSuggestion[];
  isLoading: boolean;
  onClick: (suggestion: string) => void;
  isCompact?: boolean;
};

export const Suggestions = ({
  suggestions,
  isLoading,
  onClick,
  isCompact = false,
  ...rest
}: SuggestionsProps) => {
  const { t } = useTranslation('meetingMemory');
  if (isLoading) return <Loader mb={8} isCompact={isCompact} />;

  if (!suggestions || suggestions?.length === 0) return null;

  return (
    <Box>
      {!isCompact && (
        <Text fontSize="sm" color="gray.300">
          <Trans t={t} i18nKey="suggestedQueries" />
        </Text>
      )}
      <Flex
        gap={isCompact ? 2 : 4}
        w="full"
        mt={3}
        direction={{ base: 'column', lg: 'row' }}
        {...rest}
      >
        {suggestions?.map((suggestion, index) => (
          <Card
            key={suggestion.id}
            suggestion={suggestion}
            onClick={onClick}
            borderColor={isCompact ? 'gray.800' : borderColors[index % 2 === 0 ? 'even' : 'odd']}
            isCompact={isCompact}
            w={isCompact ? 'auto' : { base: 'full', lg: '33%' }}
          />
        ))}
      </Flex>
    </Box>
  );
};
