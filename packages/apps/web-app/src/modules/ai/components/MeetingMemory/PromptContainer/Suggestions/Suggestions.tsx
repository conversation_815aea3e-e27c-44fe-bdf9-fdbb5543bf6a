import { Box, Button, Flex, SkeletonText, Text } from '@chakra-ui/react';
import { Icon } from '@core/components/Icon/Icon';
import { faChevronUp } from '@fortawesome/pro-solid-svg-icons';
import { useSuggestions } from '@waitroom/common';
import { getRequestData } from '@waitroom/react-query';
import { memo, ReactNode } from 'react';
import { useTranslation } from 'react-i18next';
import { Suggestion } from './Suggestion/Suggestion';

export type SuggestionsProps = {
  shownLimit?: number;
  isOpen: boolean;
  onToggle: () => void;
  onClick: (suggestion: string) => void;
  text?: ReactNode;
  hideOnSmall?: boolean;
};

export const Suggestions = memo(
  ({ text, shownLimit = 4, isOpen, onToggle, onClick, hideOnSmall }: SuggestionsProps) => {
    const { t } = useTranslation('meetingMemory');
    const { data, isLoading } = useSuggestions(
      {
        params: {
          // 16 - Not too many and not too little
          limit: 16,
        },
      },
      { enabled: false },
    );
    const { suggestions } = getRequestData(data) || {
      suggestions: t('suggestions', { returnObjects: true }) as string[],
    };

    if (isLoading && !suggestions) {
      return <SkeletonText noOfLines={1} skeletonHeight={3} width={'65%'} />;
    }
    if (!suggestions) return null;
    const showAlways = Math.min(shownLimit, suggestions.length);
    const anyVisible = showAlways > 0;
    return (
      <>
        <Flex
          display={hideOnSmall ? { base: anyVisible ? undefined : 'none', md: 'flex' } : undefined}
          direction={'row'}
          align={'center'}
          gap={2}
          wrap={'wrap'}
          maxW={'full'}
        >
          <Box alignItems="center">
            <Text fontSize={'sm'} color="gray.400" w={anyVisible ? 'full' : 'auto'}>
              {text || t(anyVisible ? `promptDefault` : `promptSmall`)}
            </Text>
          </Box>
          {suggestions.slice(0, isOpen ? suggestions.length : showAlways).map((suggestion, i) => (
            <Suggestion key={i} onClick={onClick}>
              {typeof suggestion === 'string' ? suggestion : suggestion.short}
            </Suggestion>
          ))}
        </Flex>
        {showAlways !== suggestions.length && (
          <Button
            display={
              hideOnSmall ? { base: anyVisible ? undefined : 'none', md: 'inline-flex' } : undefined
            }
            size={'2xs'}
            variant={'outline'}
            px={3}
            colorScheme={'gray.800'}
            fontSize={'sm'}
            fontWeight={'normal'}
            maxW={'full'}
            textOverflow={'ellipsis'}
            overflow={'hidden'}
            onClick={onToggle}
            rightIcon={
              isOpen || showAlways === 0 ? (
                isOpen ? (
                  <Icon icon={faChevronUp} mt={0.5} />
                ) : undefined
              ) : undefined
            }
          >
            {t(`${isOpen ? 'showLess' : showAlways > 0 ? 'moreSuggestions' : 'showSuggestions'}`, {
              count: suggestions ? suggestions.length - shownLimit : 0,
            })}
          </Button>
        )}
      </>
    );
  },
);
