import { Box, SkeletonText, Stack, Text, useDisclosure } from '@chakra-ui/react';
import Markdown from '@core/components/Markdown/Markdown';
import { MeetingMemory } from '@waitroom/models';
import { useEffect, useRef } from 'react';
import { useTranslation } from 'react-i18next';
import { markdownSx } from '../../../../utils/styles';
import { Sources } from '../../../Sources/Sources';
import { Actions } from './Actions/Actions';

export type AIMessageProps = {
  cacheId: string;
  threadId: string;
  message: MeetingMemory.Message;
  isLast: boolean;
};

export const AIMessage = ({ cacheId, threadId, message, isLast }: AIMessageProps) => {
  const { t } = useTranslation('meetingMemory');
  const contentRef = useRef<HTMLDivElement | null>(null);
  const { isOpen, onToggle, onClose } = useDisclosure({ defaultIsOpen: true });
  const messageRef = useRef(null);
  const hasSources = Object.keys(message.sources || {}).length > 0;

  useEffect(() => {
    if (!isLast) onClose();
  }, [isLast, onClose]);

  return (
    <Stack spacing={6} ref={messageRef}>
      <Box position="relative">
        <Stack spacing={4}>
          <Text fontSize="xs" fontWeight="bold" color="gray.500">
            {message.todo
              ? message.todo
              : !message.content?.length
                ? t('loadingPromptResponse')
                : null}
          </Text>
          {message.isStreaming && !message.content?.length ? (
            <SkeletonText noOfLines={2} skeletonHeight={3} spacing={3} maxW="75%" />
          ) : (
            <div ref={contentRef}>
              <Markdown sx={{ overflow: 'auto', ...markdownSx }}>{message.content}</Markdown>
            </div>
          )}
        </Stack>
      </Box>
      <div>
        <Actions
          cacheId={cacheId}
          threadId={threadId}
          message={message}
          sources={message.sources}
          isOpenSources={isOpen}
          onToggleSources={onToggle}
          refresh={isLast}
          isStreaming={message.isStreaming}
          contentRef={contentRef}
        />
      </div>
      {isOpen && hasSources && <Sources sources={message.sources} />}
    </Stack>
  );
};
