import { Box, BoxProps, ButtonProps, Text } from '@chakra-ui/react';
import { MeetingMemory } from '@waitroom/models';
import { memo, ReactElement } from 'react';

export interface SuggestionCardProps extends Omit<BoxProps, 'onClick'> {
  suggestion: MeetingMemory.PersonalizedSuggestion;
  onClick?: (suggestion: string) => void;
  isCompact?: boolean;
}

const buttonProps: ButtonProps = {
  px: 4,
  py: 3,
  borderWidth: 'thin',
  borderColor: 'orange.200',
  bg: 't.gray-800-50',
  _hover: {
    bg: 't.gray-800-90',
  },
  rounded: 'md',
  color: 'gray.50',
  fontSize: 'sm',
  fontWeight: 400,
  variant: 'outline',
  textAlign: 'left',
  display: 'flex',
};

const compactButtonProps: ButtonProps = {
  ...buttonProps,
  px: 3,
  py: 1,
  rounded: 'full',
  borderWidth: 'thin',
  borderColor: 'gray.800',
};

export const Card = memo(
  ({ suggestion, onClick, isCompact, ...rest }: SuggestionCardProps): ReactElement => {
    const { content: suggestionText } = suggestion;

    const handleClick = () => {
      if (onClick) onClick(suggestionText);
    };

    return (
      <Box
        as="button"
        {...(isCompact ? compactButtonProps : buttonProps)}
        onClick={handleClick}
        {...rest}
      >
        <Text
          fontSize="sm"
          fontWeight={400}
          color="gray.50"
          noOfLines={isCompact ? 1 : 5}
          title={suggestionText}
        >
          {suggestionText}
        </Text>
      </Box>
    );
  },
);
