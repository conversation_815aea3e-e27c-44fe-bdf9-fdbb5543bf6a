// https://github.com/facebook/lexical/blob/main/packages/lexical-playground/src/plugins/MentionsPlugin/index.tsx
// https://github.com/sodenn/lexical-beautiful-mentions
import { Box, Button, LightMode, Text } from '@chakra-ui/react';
import { Icon } from '@core/components/Icon/Icon';
import Popover from '@core/components/Popover';
import { faHashtag } from '@fortawesome/pro-solid-svg-icons';
import { useLexicalComposerContext } from '@lexical/react/LexicalComposerContext';
import {
  LexicalTypeaheadMenuPlugin,
  MenuTextMatch,
  useBasicTypeaheadTriggerMatch,
} from '@lexical/react/LexicalTypeaheadMenuPlugin';
import flip from '@popperjs/core/lib/modifiers/flip.js';
import preventOverflow from '@popperjs/core/lib/modifiers/preventOverflow.js';
import { type Instance } from '@popperjs/core/lib/popper-lite';
import { SessionSummary } from '@waitroom/models';
import {
  $getSelection,
  $isRangeSelection,
  COMMAND_PRIORITY_NORMAL,
  KEY_ESCAPE_COMMAND,
  TextNode,
} from 'lexical';
import { MouseEvent, ReactElement, useCallback, useMemo, useState } from 'react';
import { createPortal } from 'react-dom';
import { useTranslation } from 'react-i18next';
import { MentionOption } from './Menu/Item/MenuItem';
import MentionsMenu from './Menu/MentionMenu';
import { $createMentionNode, $isMentionNode, buildMentionValue } from './Node/helpers';
import { SESSION_REFERENCE, SUGGESTION_LIST_LENGTH_LIMIT } from './config';
import { checkForMentions } from './helpers';
import { useMentionLookupService } from './useMentionLookup';

function getPossibleQueryMatch(text: string): MenuTextMatch | null {
  return checkForMentions(text, 0);
}
const onInit = (inst: Instance | null) => {
  // currently there is an issue where the anchor element is initially 0 in size
  setTimeout(() => {
    inst?.forceUpdate();
  }, 0);
};

const elementSx = {
  '&[data-popper-placement="right-end"]': {
    bottom: '16!',
  },
};

const modifiers = [preventOverflow, flip];

export type MentionPluginProps = {
  data: SessionSummary[] | undefined;
  isPending?: boolean;
};

export default function MentionsPlugin({
  data,
  isPending,
}: MentionPluginProps): ReactElement | null {
  const { t } = useTranslation();
  const [editor] = useLexicalComposerContext();
  const [queryString, setQueryString] = useState<string | null>(null);

  const results = useMentionLookupService(data, queryString);
  const checkForSlashTriggerMatch = useBasicTypeaheadTriggerMatch('/', {
    minLength: 0,
  });

  const options = useMemo(
    () =>
      results
        .map((result) => new MentionOption(result.sessionTitle, result))
        .slice(0, SUGGESTION_LIST_LENGTH_LIMIT),
    [results],
  );

  const onSelectOption = useCallback(
    (selectedOption: MentionOption, nodeToReplace: TextNode | null, closeMenu?: () => void) => {
      editor.update(() => {
        const s = selectedOption.session;
        const mentionNode = $createMentionNode<SessionSummary>(
          buildMentionValue(s.sessionTitle),
          s,
        );
        if (nodeToReplace) {
          nodeToReplace.replace(mentionNode);
          const otherOptions = selectedOption.meta?.otherOptions;
          if (otherOptions?.length) {
            let lastNode = mentionNode;
            otherOptions.forEach((option) => {
              const newMentionNode = $createMentionNode<SessionSummary>(
                buildMentionValue(option.session.sessionTitle),
                option.session,
              );
              lastNode.insertAfter(newMentionNode);
              lastNode = newMentionNode;
            });
          }
        }
        closeMenu?.();
        editor.focus();
      });
    },
    [editor],
  );

  const checkForMentionMatch = useCallback(
    (text: string) => {
      const slashMatch = checkForSlashTriggerMatch(text, editor);
      if (slashMatch !== null) return null;
      return getPossibleQueryMatch(text);
    },
    [checkForSlashTriggerMatch, editor],
  );

  const onButtonClick = useCallback(
    (ev: MouseEvent<HTMLButtonElement>) => {
      ev.stopPropagation();
      ev.preventDefault();
      editor.update(() => {
        const selection = $getSelection();
        if (!selection) return;

        // check previous node if it is the same
        if ($isRangeSelection(selection)) {
          const anchorNode = selection.anchor.getNode();
          const anchorOffset = selection.anchor.offset;
          if (!$isMentionNode(selection.getNodes()?.[0]) && anchorOffset > 0) {
            const previousCharacter = anchorNode.getTextContent()[anchorOffset - 1];
            if (previousCharacter === SESSION_REFERENCE) return editor.focus();
          }
        }
        selection.insertText(SESSION_REFERENCE);
      });
      editor.focus();
    },
    [editor],
  );

  // fyi: blur event is triggered before on click so the selection does not get registered
  const onBackdropClick = useCallback(() => {
    editor.dispatchCommand(KEY_ESCAPE_COMMAND, new KeyboardEvent('escape'));
  }, [editor]);

  return (
    <>
      <LexicalTypeaheadMenuPlugin<MentionOption>
        onQueryChange={setQueryString}
        onSelectOption={onSelectOption}
        triggerFn={checkForMentionMatch}
        options={options}
        commandPriority={COMMAND_PRIORITY_NORMAL}
        menuRenderFn={(
          anchorElementRef,
          { selectedIndex, selectOptionAndCleanUp, setHighlightedIndex },
        ) =>
          anchorElementRef.current
            ? createPortal(
                <Box position={'relative'} w={['250px', '400px']} zIndex={'popover'}>
                  <div>
                    <Box
                      className={'backdrop'}
                      position={'fixed'}
                      left={0}
                      top={0}
                      w={'full'}
                      h={'full'}
                      onClick={onBackdropClick}
                    />
                  </div>
                  <Popover
                    isOpen={true}
                    placement={'right-start'}
                    strategy={'absolute'}
                    target={anchorElementRef.current}
                    onInit={onInit}
                    modifiers={modifiers}
                    elementSx={elementSx}
                  >
                    <MentionsMenu
                      editor={editor}
                      options={options}
                      isPending={isPending}
                      selectedIndex={selectedIndex}
                      selectOptionAndCleanUp={selectOptionAndCleanUp}
                      setHighlightedIndex={setHighlightedIndex}
                    />
                  </Popover>
                </Box>,
                document.body,
              )
            : null
        }
      />
      <LightMode>
        <Button
          aria-label={'Meetings'}
          title={'Meetings'}
          colorScheme={'gray.50'}
          color={'gray.900'}
          shadow={'none'}
          size={'3xs'}
          fontSize={'sm'}
          px={[2, 4]}
          h={`${32}px`}
          minW={`${32}px`}
          fontWeight={'normal'}
          onClick={onButtonClick}
          zIndex={2}
        >
          <Icon icon={faHashtag} />
          <Text ml={1} display={['none', 'inline']}>
            {t('global.findMeeting')}
          </Text>
        </Button>
      </LightMode>
    </>
  );
}
