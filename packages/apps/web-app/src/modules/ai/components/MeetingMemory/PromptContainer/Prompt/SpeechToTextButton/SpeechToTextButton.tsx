import { Alert, AlertIcon, IconButton, LightMode, useToast } from '@chakra-ui/react';
import { Icon } from '@core/components/Icon/Icon';
import { SpeakingIcon } from '@core/components/Icon/Speaking/SpeakingIcon';
import { useSpeechToText } from '@core/hooks/useSpeechToText';
import { faMicrophone } from '@fortawesome/pro-solid-svg-icons';
import { memo } from 'react';
import { useTranslation } from 'react-i18next';
import theme from '../../../../../../theme';

type SpeechToTextProps = {
  onChange: (transcript: string) => void;
};

export const SpeechToTextButton = memo(({ onChange }: SpeechToTextProps) => {
  const toast = useToast();
  const { t } = useTranslation();

  const { isListening, stop, start, isEnabled } = useSpeechToText({
    onChange,
    onError: (ev) => {
      const id = 'stt-error';
      if (!toast.isActive(id)) {
        toast({
          id,
          render: () => (
            <Alert status="error" p={2}>
              <AlertIcon />
              {ev?.message || t('global.speechToTextError')}
            </Alert>
          ),
        });
      }
    },
  });
  if (!isEnabled) return null;
  return (
    <LightMode>
      <IconButton
        aria-label="Listen"
        title="Listen"
        colorScheme="gray.50"
        isRound
        shadow="none"
        size={'2xs'}
        onClick={isListening ? stop : start}
      >
        {isListening ? (
          <SpeakingIcon w="24px" h="24px" color={theme.colors.gray[400]} />
        ) : (
          <Icon icon={faMicrophone} size={'lg'} color={'gray.800'} />
        )}
      </IconButton>
    </LightMode>
  );
});
