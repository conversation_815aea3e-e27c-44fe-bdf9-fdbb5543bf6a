import { Meta, StoryObj } from '@storybook/react';
import { faker } from '@waitroom/tests';
import { StorybookProvidersDecorator } from '../../../../../../stories/utils/Providers';
import { SourceItem } from './SourceItem';

const meta: Meta<typeof SourceItem> = {
  title: 'web-app/components/MeetingMemory/SourceItem',
  component: SourceItem,
  tags: ['autodocs'],
  decorators: [StorybookProvidersDecorator],
};

export default meta;
type Story = StoryObj<typeof SourceItem>;

export const Template: Story = {
  args: {
    data: faker.meetingMemory.sourceData(),
  },
};
