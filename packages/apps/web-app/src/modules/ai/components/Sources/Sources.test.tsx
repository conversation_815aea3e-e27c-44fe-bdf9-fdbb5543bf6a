import { fireEvent, render } from '@testing-library/react';
import { faker } from '@waitroom/tests';
import { describe, expect, it } from 'vitest';
import { withAll } from '../../../../tests/helpers/storeProviders';
import { Sources } from './Sources';

vi.mock('../utils/date', () => ({
  mixDateFormat: vi.fn((date, t, format) => `${date}-${format}`),
}));

describe('<Sources />', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });
  it('should render nothing when no sources given', () => {
    const { queryByRole } = render(<Sources />);
    expect(queryByRole('button')).toBeNull();
  });

  it('should render all sources', () => {
    const count = 4;
    const sources = faker.meetingMemory.sources(count);
    const { getAllByTestId, queryAllByTestId } = render(withAll(<Sources sources={sources} />));
    expect(getAllByTestId('source')).toHaveLength(count);
    expect(queryAllByTestId('show-more')).toHaveLength(0);
  });

  it('should render only 5 sources when there are more than 5 sources', () => {
    const count = 10;
    const sources = faker.meetingMemory.sources(count);
    const { queryAllByTestId } = render(withAll(<Sources sources={sources} />));
    expect(queryAllByTestId('source')).toHaveLength(5);
    expect(queryAllByTestId('show-more')).toHaveLength(1);
  });

  it('should unfold when clicking on "Show more" button', () => {
    const count = 10;
    const sources = faker.meetingMemory.sources(count);
    const { queryAllByTestId, getByTestId } = render(withAll(<Sources sources={sources} />));
    expect(queryAllByTestId('source')).toHaveLength(5);
    fireEvent.click(getByTestId('show-more'));
    expect(queryAllByTestId('source')).toHaveLength(count);
  });
});
