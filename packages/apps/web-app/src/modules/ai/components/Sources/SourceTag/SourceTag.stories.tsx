import { Meta, StoryObj } from '@storybook/react';
import { faker } from '@waitroom/tests';
import { StorybookProvidersDecorator } from '../../../../../stories/utils/Providers';
import { SourceTag } from './SourceTag';

const meta: Meta<typeof SourceTag> = {
  title: 'web-app/components/MeetingMemory/SourceTag',
  component: SourceTag,
  tags: ['autodocs'],
  decorators: [StorybookProvidersDecorator],
};

export default meta;
type Story = StoryObj<typeof SourceTag>;

export const Template: Story = {
  args: {
    sources: faker.def.helpers.multiple(() => faker.meetingMemory.source(), {
      count: { min: 1, max: 12 },
    }),
  },
};
