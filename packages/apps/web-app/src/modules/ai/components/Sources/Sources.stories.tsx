import { Meta, StoryObj } from '@storybook/react';
import { faker } from '@waitroom/tests';
import { StorybookProvidersDecorator } from '../../../../stories/utils/Providers';
import { Sources } from './Sources';

const meta: Meta<typeof Sources> = {
  title: 'web-app/components/MeetingMemory/Sources',
  component: Sources,
  tags: ['autodocs'],
  decorators: [StorybookProvidersDecorator],
};

export default meta;
type Story = StoryObj<typeof Sources>;

export const Template: Story = {
  args: {
    sources: faker.def.helpers.maybe(() => faker.meetingMemory.sources()),
  },
};
