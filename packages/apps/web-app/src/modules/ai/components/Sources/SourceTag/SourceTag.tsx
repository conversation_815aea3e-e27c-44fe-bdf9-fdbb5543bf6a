import {
  Button,
  ButtonProps,
  Popover,
  PopoverBody,
  PopoverContent,
  PopoverTrigger,
  Text,
} from '@chakra-ui/react';
import { mixDateFormat } from '@core/utils/dateTime';
import { MeetingMemory } from '@waitroom/models';
import { useTranslation } from 'react-i18next';
import { SourceItem } from './SourceItem/SourceItem';

export type SourceTagProps = ButtonProps & {
  size?: ButtonProps['size'];
  sources: MeetingMemory.Source[];
};

export const SourceTag = ({ sources, size = '2xs', ...rest }: SourceTagProps) => {
  const { t } = useTranslation();
  const { data } = sources[0] || {};
  const dateText =
    sources.length === 1 && data?.startedAt
      ? mixDateFormat(data.startedAt * 1000, 'MMM d', t)
      : t('global.various');

  if (!data) return null;
  return (
    <Popover trigger="hover" isLazy lazyBehavior="unmount" placement="auto">
      <PopoverTrigger>
        <Button
          variant={'outline'}
          size={size}
          fontSize={'sm'}
          borderWidth={'thin'}
          borderColor={'red.300'}
          color={'inherit'}
          _hover={{
            bgColor: 'red.200',
            color: 'gray.900',
          }}
          maxW={'full'}
          overflow={'hidden'}
          textOverflow={'ellipsis'}
          {...rest}
        >
          {data.title}
          <Text as="span" fontWeight="normal" ml={1}>
            ({dateText})
          </Text>
        </Button>
      </PopoverTrigger>
      <PopoverContent rounded={'2xl'} overflow={'hidden'} w={'full'} maxW={'356px'}>
        <PopoverBody p={0}>
          {sources.map(({ data }) => (
            <SourceItem data={data} key={data.recurrenceId} />
          ))}
        </PopoverBody>
      </PopoverContent>
    </Popover>
  );
};
