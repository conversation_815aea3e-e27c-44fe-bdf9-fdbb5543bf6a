import { Box, BoxProps, Button, ButtonProps, Wrap, WrapItem } from '@chakra-ui/react';
import { Icon } from '@core/components/Icon/Icon';
import { faChevronDown } from '@fortawesome/pro-solid-svg-icons';
import { MeetingMemory } from '@waitroom/models';
import { useEffect, useMemo, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { SourceTag } from './SourceTag/SourceTag';

type SourcesProps = BoxProps & {
  size?: ButtonProps['size'];
  sources?: MeetingMemory.Sources;
};

export const Sources = ({ sources, size = '2xs', ...rest }: SourcesProps) => {
  const { t } = useTranslation('meetingMemory');
  const sourcesArr = useMemo(() => (sources ? Object.entries(sources) : []), [sources]);
  const [foldAt, setFoldAt] = useState<number>(sourcesArr.length);
  useEffect(() => {
    setFoldAt(sourcesArr.length > 5 ? 5 : sourcesArr.length);
  }, [sourcesArr]);

  const showAll = () => {
    if (sourcesArr.length > foldAt) setFoldAt(sourcesArr.length);
  };

  if (sourcesArr.length === 0) return null;
  return (
    <Box {...rest}>
      <Wrap spacing={2}>
        {sourcesArr.slice(0, foldAt).map(([sourceId, sources]) => (
          <WrapItem key={sourceId}>
            <SourceTag sources={sources} data-testid="source" size={size} />
          </WrapItem>
        ))}
        {foldAt < sourcesArr.length && (
          <WrapItem>
            <Button
              onClick={showAll}
              variant={'outline'}
              colorScheme={'gray'}
              size={size}
              fontSize={'sm'}
              borderColor={'gray.200'}
              color={'gray.800'}
              bgColor={'gray.50'}
              fontWeight={'normal'}
              _hover={{
                bgColor: 'red.200',
                color: 'gray.900',
              }}
              _dark={{
                bgColor: 'gray.900',
                color: 'gray.300',
                _hover: {
                  color: 'gray.50',
                },
              }}
              rightIcon={<Icon icon={faChevronDown} mt={0.5} />}
              data-testid="show-more"
            >
              {t('showAllSources')}
            </Button>
          </WrapItem>
        )}
      </Wrap>
    </Box>
  );
};
