import { Button } from '@chakra-ui/react';
import { Icon } from '@core/components/Icon/Icon';
import { BASE_URL } from '@core/config';
import { faApple } from '@fortawesome/free-brands-svg-icons';
import { useMountedRef } from '@waitroom/hooks';
import { AuthType } from '@waitroom/models';
import { nanoid } from 'nanoid/non-secure';
import React, { useEffect, useRef } from 'react';
import { appleAuthHelpers, useScript } from 'react-apple-signin-auth';
import { useTranslation } from 'react-i18next';
import { APPLE_CLIENT_ID } from '../../../config';
import { SocialButtonProps } from '../types';

export type AppleAuthResponse = {
  authorization: {
    /** ID JWT */
    id_token: string;
    /** Grant code valid for 5m */
    code: string;
    /** State string passed to the request */
    state?: string;
  };
  /** Only provided by apple in the first request */
  user?: {
    email: string;
    name: {
      firstName: string;
      lastName: string;
    };
  };
};

const AppleAuth = ({
  mutation,
  setError,
  loading,
  setLoading,
  onCompleted,
  redirectUrl,
  clientId,
  state,
  codeChallenge,
  isSignup,
  ...rest
}: SocialButtonProps): React.ReactElement | null => {
  useScript(appleAuthHelpers.APPLE_SCRIPT_SRC);
  const { t } = useTranslation();
  const mountedRef = useMountedRef();
  const rawNonce = useRef<string | undefined>(undefined);
  const nonce = useRef<string | undefined>(undefined);

  useEffect(() => {
    const generateNonce = async () => {
      rawNonce.current = nanoid();
      const msgUint8 = new TextEncoder().encode(rawNonce.current); // encode as (utf-8) Uint8Array
      const hashBuffer = await crypto.subtle.digest('SHA-256', msgUint8); // hash the message
      const hashArray = Array.from(new Uint8Array(hashBuffer)); // convert buffer to byte array
      const hashHex = hashArray.map((b) => b.toString(16).padStart(2, '0')).join(''); // convert bytes to hex string
      nonce.current = hashHex;
    };

    if (!nonce.current) generateNonce();
  }, [nonce, rawNonce]);

  const signInApple = () => {
    if (!mountedRef.current) return;
    setError(false);
    setLoading(AuthType.APPLE);
    appleAuthHelpers.signIn({
      authOptions: {
        clientId: APPLE_CLIENT_ID || '',
        /** Requested scopes, seperated by spaces - eg: 'email name' */
        scope: 'email name',
        /** Apple's redirectURI - must be one of the URIs you added to the serviceID - the undocumented trick in apple docs is that you should call auth from a page that is listed as a redirectURI, localhost fails */
        redirectURI: BASE_URL,
        /** Nonce */
        nonce: nonce.current,
        usePopup: true,
      },
      onSuccess: (response: AppleAuthResponse) => {
        mutation.mutate({
          type: 'apple',
          userDetails: response.user
            ? {
                nonce: rawNonce.current,
                firstName: response.user.name.firstName,
                lastName: response.user.name.lastName,
              }
            : { nonce: rawNonce.current },
          token: response.authorization.id_token,
          onCompleted,
          redirectUrl,
          clientId,
          state,
          codeChallenge,
        });
        if (!mountedRef.current) return;
        setLoading(undefined);
      },
      onError: () => {
        setError(true);
        setLoading(undefined);
      },
    });
  };

  return (
    <Button
      isDisabled={loading === AuthType.GOOGLE}
      isLoading={loading === AuthType.APPLE || mutation.isPending}
      type="button"
      color="gray.900"
      w="full"
      variant="outline"
      borderColor="gray.300"
      fontWeight="regular"
      leftIcon={<Icon mr={1} w={7} h={7} icon={faApple} />}
      id={isSignup ? 'submit-apple-signup' : 'submit-apple-login'}
      {...rest}
      onClick={signInApple}
    >
      {t('auth.ssoApple')}
    </Button>
  );
};
export default AppleAuth;
