import { AuthData, RequestOTPPayload, VerifyOTPPayload, useRequestOTP } from '@waitroom/auth';
import { useCallback, useEffect, useRef, useState } from 'react';
import { analyticsService } from '../../analytics/services';

export enum AuthView {
  NONE,
  LOGIN,
  REGISTER,
  VERIFY,
}
const analyticsPage = {
  [AuthView.REGISTER]: 'AUTH_SIGNUP',
  [AuthView.LOGIN]: 'AUTH_LOGIN',
  [AuthView.VERIFY]: 'AUTH_VERIFY',
};
export type AuthFlowProps = AuthData & {
  view?: AuthView;
  type?: 'modal' | 'page';
};
export type SetAuthModalProps = AuthFlowProps & {
  isSwitchAccounts?: boolean;
};
export type FormProps = AuthData;

export const useAuthFlow = ({
  type = 'modal',
  view: defaultView = AuthView.LOGIN,
  redirectUrl,
  clientId,
  state,
  codeChallenge,
  ...rest
}: AuthFlowProps) => {
  const [didRegister, setDidRegister] = useState(false);
  const [view, setView] = useState<AuthView>(defaultView);
  const emailRef = useRef<string | undefined>(undefined);

  const updateView = useCallback(
    (newView: AuthView) =>
      setView((prev) => {
        setDidRegister(prev === AuthView.REGISTER);
        return newView;
      }),
    [],
  );

  const resendMutation = useRequestOTP();
  const { mutate } = resendMutation;

  const onResendClick = useCallback(() => {
    if (!emailRef.current) return;
    mutate({
      isSignup: didRegister,
      email: emailRef.current,
      redirectUrl,
      clientId,
      state,
      codeChallenge,
    });
  }, [clientId, state, codeChallenge, didRegister, mutate, redirectUrl]);

  const onChangeEmailClick = useCallback(() => {
    setView(didRegister ? AuthView.REGISTER : AuthView.LOGIN);
  }, [didRegister, setView]);

  useEffect(() => {
    if (!view || !analyticsPage[view]) return;
    analyticsService.page(analyticsPage[view], { location: type });
  }, [view, type]);

  const onSubmit = useCallback(
    (values: RequestOTPPayload | VerifyOTPPayload): unknown => {
      const email = values?.email;
      if (email) emailRef.current = email;
      return;
    },
    [emailRef],
  );

  return {
    emailRef,
    view,
    updateView,
    onResendClick,
    formProps: {
      onSubmit,
      onSuccess: () => {
        updateView(AuthView.VERIFY);
      },
      redirectUrl,
      clientId,
      state,
      codeChallenge,
      ...rest,
    },
    didRegister,
    onChangeEmailClick,
    resendMutation,
  };
};
