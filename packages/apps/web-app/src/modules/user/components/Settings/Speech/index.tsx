import { Box, Flex, Heading, IconButton, Input, Select, Text } from '@chakra-ui/react';
import { Icon } from '@core/components/Icon/Icon';
import { SpeechSynthesis, useTextToSpeech } from '@core/hooks/useTextToSpeech';
import { faPause, faPlay } from '@fortawesome/pro-solid-svg-icons';
import { ChangeEvent, ReactElement, useMemo, useState } from 'react';
import { updateDeepUserSettingsState, useAppStore } from '../../../../core/store/store';

const rates = [1, 1.1, 1.25, 1.5, 2, 2.5];

const Speech = (): ReactElement | null => {
  const { voice, rate = 1 } = useAppStore.use.textToSpeech() || {};
  const [text, setText] = useState<string>(
    'This is a sample text that will be read out loud. Rumi.ai is the best!',
  );
  const voices = useMemo(() => SpeechSynthesis?.getVoices(), []);
  const { play, pause, paused, selected } = useTextToSpeech({ text, voice, rate });

  const onVoiceChange = (ev: ChangeEvent<HTMLSelectElement>) => {
    const voice = ev.currentTarget.value;
    updateDeepUserSettingsState('textToSpeech', { voice });
    setTimeout(() => {
      play({ voice });
    }, 100);
  };

  return (
    <>
      <Box mb={4}>
        <Heading as={'h3'} size={'xl'} mb={1}>
          Text to speech
        </Heading>
        <Text fontSize={'sm'} color={'gray.600'}>
          Choose a voice for text-to-speech playback. You can test different voices using the play
          button. Your selected voice will be remembered and used whenever text-to-speech is
          activated.
        </Text>
      </Box>
      <div>
        <Flex align={'center'} gap={4}>
          <Text minW={50}>Text:</Text>
          <Input
            type="text"
            bg={'white'}
            color={'gray.900'}
            onChange={(ev) => setText(ev.currentTarget.value)}
            value={text}
            size="sm"
            mb={2}
            placeholder="Filter"
          />
        </Flex>
        <Flex align={'center'} gap={4}>
          <Text minW={50}>Voice:</Text>
          <div>
            <Flex flexDir={'row'} align={'center'} justify={'center'} gap={1}>
              <Box flexGrow={1}>
                <Select
                  value={voice}
                  size="sm"
                  onChange={onVoiceChange}
                  bg={'white'}
                  color={'gray.900'}
                  w={'full'}
                >
                  <option value="">Auto</option>
                  {voices?.map((voice) => (
                    <option key={voice.name} value={voice.name}>
                      {voice.name}
                    </option>
                  ))}
                </Select>
              </Box>
              <Box flexShrink={0}>
                <Select
                  value={rate}
                  size="sm"
                  onChange={(ev) =>
                    updateDeepUserSettingsState('textToSpeech', {
                      rate: Number(ev.currentTarget.value) || 1,
                    })
                  }
                  bg={'white'}
                  color={'gray.900'}
                >
                  {rates?.map((rate) => (
                    <option key={rate} value={rate}>
                      {rate}x
                    </option>
                  ))}
                </Select>
              </Box>
              {paused ? (
                <IconButton
                  aria-label="Play"
                  size="sm"
                  colorScheme="black"
                  onClick={() => play()}
                  boxShadow={'none'}
                >
                  <Icon icon={faPlay} size="lg" />
                </IconButton>
              ) : (
                <IconButton
                  aria-label="Pause"
                  size="sm"
                  colorScheme="black"
                  onClick={pause}
                  boxShadow={'none'}
                >
                  <Icon icon={faPause} size="lg" />
                </IconButton>
              )}
            </Flex>
          </div>
        </Flex>
        {!!selected?.voice && (
          <Text fontSize={'sm'} color={'gray.600'} mt={2} ml={66}>
            Selected: <strong>{selected.voice.name}</strong>
          </Text>
        )}
      </div>
    </>
  );
};
export default Speech;
