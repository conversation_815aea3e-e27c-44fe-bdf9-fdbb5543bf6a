import { Box, Button, Flex } from '@chakra-ui/react';
import ResponseButton from '@core/components/Button/Response/ResponseButton';
import { stickyBottomCss } from '@core/components/Common/styles';
import { Response } from '@core/components/Form/Form';
import * as React from 'react';
import { FormProvider } from 'react-hook-form';
import { Link } from 'react-router-dom';
import { UserFormProps } from './UserForm.types';
import UserFormControls from './UserFormControls';
import { useUserForm } from './useUserForm';

const UserForm = ({
  backLink,
  edit,
  showResponse,
  stickyControls,
  ...rest
}: UserFormProps): React.ReactElement | null => {
  const { t, methods, mutation } = useUserForm(rest);
  const { avatar } = rest.initialValues;
  const isDirty = methods.formState.isDirty;
  return (
    <FormProvider {...methods}>
      <form onSubmit={methods.handleSubmit((data) => mutation.mutate(data))}>
        <UserFormControls simple={rest.simple} avatar={avatar} />
        <Box sx={stickyControls ? stickyBottomCss : undefined}>
          {showResponse && mutation.isError ? (
            <Response mb={2} response={mutation.error} namespace="responses.user." />
          ) : null}
          <Flex direction="row" zIndex={2} mt={4}>
            {backLink ? (
              <Link to={backLink} className="none">
                <Button
                  size={{ base: 'sm', lg: 'def', xl: 'md' }}
                  variant="outline"
                  colorScheme="gray.900"
                  px={8}
                  mr={3}
                  isLoading={mutation.isPending}
                  type="button"
                >
                  {t('global.cancel')}
                </Button>
              </Link>
            ) : null}
            <ResponseButton
              variant="solid"
              size={{ base: 'sm', lg: 'def', xl: 'md' }}
              w="100%"
              colorScheme="gray.900"
              isLoading={mutation.isPending}
              type="submit"
              isDisabled={!isDirty}
              isSuccess={mutation.isSuccess}
            >
              {edit ? t('dashboard.saveChanges') : t('global.createUser')}
            </ResponseButton>
          </Flex>
        </Box>
      </form>
    </FormProvider>
  );
};

export default UserForm;
