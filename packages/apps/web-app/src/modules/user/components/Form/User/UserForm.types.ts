import { MutationOptions, UseMutationResult } from '@tanstack/react-query';
import { UserApiService } from '@waitroom/common-api';
import { RequestResponse, UserComplete } from '@waitroom/models';
import { defaultValues } from './UserForm.constants';

export type UserFormValues = typeof defaultValues;

export type TMutationOptions = MutationOptions<
  UserApiService.Update['response'],
  RequestResponse,
  UserFormValues
>;
export type TMutationResult = UseMutationResult<
  UserApiService.Update['response'],
  RequestResponse,
  UserFormValues
>;

export interface UserFormControlsProps {
  avatar: UserComplete['avatar'];
  showAvatar?: boolean;
  simple?: boolean;
}

export interface UserFormProps extends Pick<UserFormControlsProps, 'showAvatar' | 'simple'> {
  onSubmit?: TMutationOptions['onMutate'];
  onSuccess?: TMutationOptions['onSuccess'];
  onError?: TMutationOptions['onError'];
  initialValues: UserComplete;
  stickyControls?: boolean;
  backLink?: string;
  edit?: boolean;
  simple?: boolean;
  showResponse?: boolean;
}
