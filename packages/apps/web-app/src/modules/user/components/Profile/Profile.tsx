import { Box, Button, Container, Heading } from '@chakra-ui/react';
import { Icon } from '@core/components/Icon/Icon';
import { faChevronRight } from '@fortawesome/pro-regular-svg-icons';
import { EventType } from '@waitroom/analytics';
import { updateCurrentUser } from '@waitroom/auth';
import { UserApiService } from '@waitroom/common-api';
import { LobbyBasic, UserComplete } from '@waitroom/models';
import { useCallback } from 'react';
import { useTranslation } from 'react-i18next';
import { Link } from 'react-router-dom';
import { analyticsService } from '../../../analytics/services';
import LobbyCard, { ns as cardNs } from '../../../dashboard/components/Lobby/LobbyCard/LobbyCard';
import UserForm from '../Form/User/UserForm';

type ProfileProps = {
  currentUser: UserComplete;
  deleteRoute: string;
};

const defaultLobby: LobbyBasic = {
  slug: '',
  isActive: true,
  lobbyID: '',
};

export const Profile = ({ currentUser, deleteRoute }: ProfileProps): React.ReactElement | null => {
  const { t } = useTranslation();

  const onSuccess = useCallback((response: UserApiService.Update['response']) => {
    if (response && response.data?.data?.user) {
      updateCurrentUser(response.data.data.user);
      analyticsService.track(EventType.UserProfileUpdated, {});
    }
  }, []);

  return (
    <Container maxW={'container.md'}>
      <Heading as="h1" mb={8} mr={3}>
        {t('dashboard.wProfile')}
      </Heading>
      <UserForm initialValues={currentUser} onSuccess={onSuccess} edit showResponse />
      <Box mt={8}>
        <LobbyCard
          heading={t(`${cardNs}.profileHeading`)}
          body={t(`${cardNs}.profileDesc`)}
          showUsers={false}
          editText
          lobby={currentUser.lobbies?.[0] || defaultLobby}
          toggleable
        />
      </Box>
      <Box mt={8} borderBottom={'1px'} borderColor={'gray.200'}>
        <Button
          as={Link}
          to={deleteRoute}
          variant="link"
          colorScheme={'gray.900'}
          fontWeight={'normal'}
          py={4}
          w={'full'}
          rounded={0}
          size={'def'}
          justifyContent={'space-between'}
          px={0}
          rightIcon={<Icon icon={faChevronRight} />}
        >
          {t('profile.deleteMyAccount')}
        </Button>
      </Box>
    </Container>
  );
};
