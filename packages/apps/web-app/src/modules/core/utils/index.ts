/* eslint-disable @typescript-eslint/no-explicit-any */
import { RefCallback, RefObject } from 'react';

export type Ref = RefCallback<any> | RefObject<any> | null;
export const mergeRefs = (...refs: Ref[]): Ref => {
  if (!refs.length) return refs[0];
  return (inst: RefCallback<any>): void => {
    for (const ref of refs) {
      if (typeof ref === 'function') ref(inst);
      else if (ref) ref.current = inst;
    }
  };
};

export const isDarkColor = (color: string): boolean => {
  const [, hue] = color.split('.');
  return Number(hue) >= 500;
};

export const delay = (time = 100) => new Promise((resolve) => setTimeout(resolve, time));

export const browserLanguages = navigator.languages || ['en-US'];
export const browserLanguage = navigator.language;
