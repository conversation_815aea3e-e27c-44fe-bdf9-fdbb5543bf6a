import { MouseEventHandler } from 'react';

export const scrollToBottom = (element: HTMLElement | null | undefined) => {
  if (!element) return;
  element.scrollTop = element.scrollHeight;
};

export const scrollToWindowBottom = (behavior?: ScrollBehavior) => {
  window.scroll({
    top: document.body.scrollHeight,
    behavior: behavior,
  });
};

export const isDocumentHidden = () => document.visibilityState === 'hidden';

export const preventDefault: MouseEventHandler = (ev) => ev.preventDefault();
export const stopPropagation: MouseEventHandler = (ev) => ev.stopPropagation();
export const eventCancelAll: MouseEventHandler = (ev) => {
  ev.preventDefault();
  ev.stopPropagation();
};

export const intersectionObserverSupported =
  'IntersectionObserver' in window && 'IntersectionObserverEntry' in window;

export const filterKeyboardEvent = (
  ev: KeyboardEvent,
  {
    disableSecondaryKeys = true,
    filterInputs = true,
  }: { disableSecondaryKeys?: boolean; filterInputs?: boolean } = {},
) => {
  // prevent shortcuts on alt, ctrl, shift, cmd keys
  const otherKeys = ev.altKey || ev.ctrlKey || ev.shiftKey || ev.metaKey;
  if (disableSecondaryKeys && otherKeys) return false;
  // prevent shortcuts on input or textarea typing
  if (filterInputs && ev.target instanceof Element) {
    const target = ev?.target?.tagName.toLowerCase();
    return target !== 'input' && target !== 'textarea';
  }
  return true;
};

export const onUnloadCallback =
  (text = '') =>
  (event: BeforeUnloadEvent) => {
    event.preventDefault();
    event.stopImmediatePropagation();
    event.returnValue = text;
    return text;
  };
export const defaultOnUnloadCallback = onUnloadCallback();
