import { createContext, ReactNode, RefObject, useContext, useMemo } from 'react';

export type FnOnSubmit = (sessionId: string, sessionRecurrenceId: string) => void;

export type FnOnSubmitRef = Record<string, FnOnSubmit>;

export type UnboundFormContextType = {
  onSubmitRef: RefObject<FnOnSubmitRef | null>;
  autoSave: boolean;
};

export const UnboundFormContext = createContext<UnboundFormContextType | undefined>(undefined);

export type UnboundFormProviderProps = UnboundFormContextType & {
  children: ReactNode;
};

export const UnboundFormProvider = ({
  onSubmitRef,
  autoSave = false,
  children,
}: UnboundFormProviderProps) => {
  const onSubmitRefMemo = useMemo(() => ({ onSubmitRef, autoSave }), [autoSave, onSubmitRef]);

  return (
    <UnboundFormContext.Provider value={onSubmitRefMemo}>{children}</UnboundFormContext.Provider>
  );
};

export const useUnboundFormContext = () => {
  const context = useContext(UnboundFormContext);

  return context;
};
