import { LogLevel } from '@waitroom/logger';
import {
  MeetingMemory,
  Session,
  STORAGE_GENERAL,
  STORAGE_USER_SETTINGS,
  UserBasic,
} from '@waitroom/models';
import { createSelectors } from '@waitroom/state';
import { isObject } from '@waitroom/utils';
import { produce } from 'immer';
import { LogLevel as StreamLogLevel } from 'livekit-client';
import { shared } from 'use-broadcast-ts';
import { create } from 'zustand';
import { persist } from 'zustand/middleware';
import type { XRayFormValues } from '../../xray/components/Dashboard/schema';
import { envs } from '../services/envs';
import { storageService } from '../services/storage';

export const PIP_SIZE_OPTIONS = {
  lg: [854, 480],
  md: [640, 360],
  sm: [480, 270],
  xs: [320, 180],
} satisfies { [key: string]: [number, number] };

export type PipSizeKey = keyof typeof PIP_SIZE_OPTIONS;

export type UserSettings = {
  /**
   * 0 - default (all)
   * 1 - all
   * 2 - only important
   * 3 - none
   */
  audioAlerts: 0 | 1 | 2 | 3;
  notifications: {
    requestAccess: boolean;
    queue?: boolean;
    sessionStart?: boolean;
  };
  textToSpeech: {
    voice?: string;
    rate?: number;
  };
  pipSize: PipSizeKey | undefined;
  sessionThreads: Record<Session['sessionID'], MeetingMemory.Thread['id']>;
  xray: {
    values?: XRayFormValues;
    step?: number;
    date?: number;
  };
};

export const defaultUserSettings: UserSettings = {
  audioAlerts: 1,
  notifications: {
    requestAccess: true,
  },
  textToSpeech: {},
  sessionThreads: {},
  pipSize: 'sm',
  xray: {},
};

export type AppState = UserSettings & {
  deleteRequests: Dict<boolean> | undefined;
  hostsOnboarded: Dict<boolean> | undefined;
  hideBookDemo: boolean | undefined;
  hideWelcomeToDashboard: boolean | undefined;
  showChecklist: boolean | undefined;
  onboarding: UserBasic['onboarding'] | undefined;
  hideOnboardingPopovers: boolean | undefined;
  hideGenerateAIFeed: boolean | undefined;
  continueInMobWeb: string | undefined;
  upgradeDismissedOn: string | undefined;
  debug:
    | {
        logLevel?: LogLevel;
        streamLogLevel?: StreamLogLevel;
        previewEndpoint?: string;
      }
    | undefined;
};

const initialState: AppState = {
  deleteRequests: undefined,
  hostsOnboarded: undefined,
  hideBookDemo: undefined,
  hideWelcomeToDashboard: undefined,
  showChecklist: false,
  hideOnboardingPopovers: undefined,
  hideGenerateAIFeed: undefined,
  continueInMobWeb: undefined,
  onboarding: undefined,
  upgradeDismissedOn: undefined,
  debug: {
    logLevel: Number(envs.VITE_LOG_LEVEL ?? 0) as LogLevel,
    streamLogLevel: 4,
  },
  ...defaultUserSettings,
};

const useStore = create<AppState>()(
  persist(
    shared(() => initialState, { name: STORAGE_GENERAL }),
    {
      name: STORAGE_GENERAL,
      version: 1,
      partialize: (state) => {
        const { showChecklist: _, ...partial } = state;
        return partial;
      },
      merge: (persistedState, currentState) => {
        if (!persistedState) {
          persistedState = {
            ...storageService.getParsed<AppState>(STORAGE_GENERAL),
            ...storageService.getParsed<UserSettings>(STORAGE_USER_SETTINGS),
          };
        }
        return isObject<AppState>(persistedState)
          ? {
              ...currentState,
              ...persistedState,
              debug: {
                ...persistedState.debug,
                logLevel: Number(
                  persistedState.debug?.logLevel ?? currentState.debug?.logLevel ?? 0,
                ) as LogLevel,
                streamLogLevel: Number(
                  persistedState.debug?.streamLogLevel ?? currentState.debug?.streamLogLevel ?? 4,
                ),
              },
            }
          : currentState;
      },
    },
  ),
);

export const useAppStore = createSelectors(useStore);

export const updateAppState = (update: Partial<AppState> | ((prev: AppState) => AppState)) => {
  useStore.setState((prev) =>
    typeof update === 'function' ? update(prev) : { ...prev, ...update },
  );
};

export const updateDebugSettings = (updated: Partial<AppState['debug']>) => {
  updateAppState((prev) =>
    produce(prev, (draft) => {
      draft.debug = {
        ...prev.debug,
        ...updated,
      };
    }),
  );
};

export const updateDeepUserSettingsState = <K extends keyof AppState>(
  key: K,
  newData: Partial<AppState[K]>,
) => {
  updateAppState((prev) => {
    const prevK = prev?.[key];
    return {
      ...prev,
      [key]:
        typeof prevK === 'object'
          ? {
              ...prevK,
              ...newData,
            }
          : newData,
    };
  });
};

export const updateXRayState = (values: XRayFormValues | undefined, step?: number) => {
  updateAppState((prev) => ({
    ...prev,
    xray: values
      ? {
          values,
          step,
          date: Date.now(),
        }
      : {},
  }));
};
