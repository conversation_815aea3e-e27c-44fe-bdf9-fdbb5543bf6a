import { createSelectors } from '@waitroom/state';
import { create } from 'zustand';

export type NetworkState = {
  online: boolean;
  lastChange: number;
};

export type GlobalState = {
  network: NetworkState;
};

const initialState: GlobalState = {
  network: {
    online: navigator.onLine,
    lastChange: Date.now(),
  },
};

const useStore = create<GlobalState>()(() => initialState);
export const useGlobalStore = createSelectors(useStore);

export const updateGlobalState = (
  update: Partial<GlobalState> | ((prev: GlobalState) => GlobalState),
) => {
  useStore.setState((prev) =>
    typeof update === 'function' ? update(prev) : { ...prev, ...update },
  );
};

const handleNetworkChange = () => {
  updateGlobalState((prev) => ({
    ...prev,
    network: {
      online: navigator.onLine,
      lastChange: Date.now(),
    },
  }));
};

export const setupNetworkListeners = () => {
  window.addEventListener('online', handleNetworkChange);
  window.addEventListener('offline', handleNetworkChange);
};

/** Selectors */
export const selectIsOnline = (state: GlobalState) => state.network.online;
