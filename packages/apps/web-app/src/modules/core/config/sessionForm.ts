import { faBuildings, faLock } from '@fortawesome/pro-regular-svg-icons';
import {
  DataVisibility,
  Recurring,
  SessionSettings,
  SummaryEmailRecipients,
} from '@waitroom/models';
import { environments } from '.';
import { defaultLanguage } from '../utils/session';

// minutes
export const durations = [15, 30, 45, 60, 90, 120];
// translation keys
export const recurring = {
  [Recurring.NONE]: 'global.doesNotRepeat',
  [Recurring.DAILY]: 'global.everyDay',
  [Recurring.WEEKLY]: 'global.everyWeek',
  [Recurring.MONTHLY]: 'global.everyMonth',
};

export const aiFeedVisibility = ['all', 'host'] as const;

const ns = 'form.session.dataVisibilityModal';
export const dataVisibilityModalOptions = {
  [DataVisibility.ParticipantOnly]: {
    heading: `${ns}.inviteOnly.heading`,
    description: `${ns}.inviteOnly.description`,
    icon: faLock,
  },
  [DataVisibility.TeamVisible]: {
    heading: `${ns}.teamVisible.heading{teamName}{teamMembersCount}`,
    description: `${ns}.teamVisible.description`,
    icon: faBuildings,
  },
};

const ns2 = 'form.session.dataVisibility';
export const dataVisibilityOptions: Record<string, Record<string, string>> = {
  [DataVisibility.ParticipantOnly]: {
    label: `${ns2}.inviteOnlyLabel`,
  },
  [DataVisibility.TeamVisible]: {
    label: `${ns2}.teamVisibleLabel{teamName}{teamMembersCount}`,
  },
};

export const defaultUserMeetingTypeTitle = 'general_meeting';

export const defaultValues = {
  sessionTitle: '',
  about: '',
  startTimestamp: undefined as Maybe<string>,
  duration: `${durations[1]}`,
  recurring: `${Recurring.NONE}`,
  dataVisibility: DataVisibility.ParticipantOnly,
  enableRecording: environments.isProd,
  selectedCategoryId: '',
  sessionTags: [] as string[],
  isPrivate: true,
  summaAI: true,
  aiFeedVisibility: 'all' as SessionSettings['aiFeedVisibility'],
  summaryEmailRecipients: 'everyone' as SummaryEmailRecipients | undefined,
  preferredLanguage: 'en-US',
  userMeetingType: '' as Maybe<string>,
};

export const freePlanDefaultValues = {
  ...defaultValues,
  enableRecording: false,
  summaAI: false,
};

export const teamPlanDefaultValues = {
  ...defaultValues,
  dataVisibility: DataVisibility.TeamVisible,
};

export const defaultMeetNow = (title: string) => ({
  sessionTitle: title,
  sessionSettings: {
    enableRecording: defaultValues.enableRecording,
    summaAI: defaultValues.summaAI,
    preferredLanguages: [...new Set<string>([defaultLanguage(), 'en-US'])],
  },
  isPrivate: true,
  dataVisibility: DataVisibility.ParticipantOnly,
});
