import { routes } from '@constants/routes';
import ProtectedRoute from '@modules/auth/components/ProtectedRoute/ProtectedRoute';
import AuthRedirect from '@modules/auth/components/Redirect/Redirect';
import DefaultLayout from '@modules/core/components/Layouts/Default/DefaultLayout';
import PageLoader from '@modules/core/components/Loader/Page/PageLoader';
import About from '@modules/pages/About/loader';
import Auth from '@modules/pages/Auth/loader';
import Dashboard from '@modules/pages/Dashboard/loader';
import Error from '@modules/pages/Error';
import External from '@modules/pages/External';
import Home from '@modules/pages/Home/loader';
import Host from '@modules/pages/Host/loader';
import Lobby from '@modules/pages/Lobby/loader';
import NotFound from '@modules/pages/NotFound/loader';
import Page from '@modules/pages/Page/loader';
import Checkout from '@modules/pages/Pricing/Checkout/loader';
import Pricing from '@modules/pages/Pricing/loader';
import Profile from '@modules/pages/Profile/loader';
import RequestAccess from '@modules/pages/RequestAccess/loader';
import Session from '@modules/pages/Session/loader';
import SessionIsFull from '@modules/pages/SessionIsFull/loader';
import { Role } from '@waitroom/models';
import { ReactElement } from 'react';
import { generatePath, Navigate, Route, Routes, useLocation, useParams } from 'react-router-dom';

const RedirectRecording = () => {
  const { recurrenceId, sessionId } = useParams<{
    sessionId: string;
    recurrenceId?: string;
  }>();
  const location = useLocation();
  const newUrlPath = generatePath(routes.SESSION.route, { id: sessionId, recurrenceId });
  const newUrl = `${newUrlPath}${location.search}${location.hash}`;
  return <Navigate to={newUrl} />;
};

const AppRoutes = (): ReactElement | null => (
  <Routes>
    <Route
      path={routes.HOME}
      element={
        <AuthRedirect
          authenticated
          role={Role.SESSION_OWNER}
          redirectUrl={routes.DASHBOARD.DEFAULT.link}
        >
          <Home />
        </AuthRedirect>
      }
    />
    <Route path={routes.HOME_EXPLICIT} element={<Home />} />
    <Route path={routes.ABOUT} element={<About />} />
    <Route path={routes.SESSION.route} element={<Session />} />
    <Route path={routes.RECORDINGS.route} element={<RedirectRecording />} />
    <Route path={routes.LOBBY.route} element={<Lobby />} />
    <Route path={routes.REQUEST_ACCESS} element={<RequestAccess />} />
    {/* CMS routes */}
    <Route path={routes.LANDING.route} element={<Page />} />
    <Route path={routes.PAGE.route} element={<Page />} />
    <Route path={routes.PRODUCT.route} element={<Page />} />
    <Route path={routes.RESOURCES.route} element={<Page />} />
    <Route path={routes.USE_CASES.route} element={<Page />} />
    <Route path={routes.TURN_BASED_MEETINGS.route} element={<Page />} />
    {/* ********** */}
    <Route
      path={routes.HOST}
      element={
        <ProtectedRoute loader={<PageLoader />}>
          <Host />
        </ProtectedRoute>
      }
    />
    <Route
      path={routes.CHECKOUT}
      element={
        <ProtectedRoute loader={<PageLoader />}>
          <Checkout />
        </ProtectedRoute>
      }
    />
    <Route path={routes.PRICING} element={<Pricing />} />
    <Route
      path={routes.PROFILE.DEFAULT.route}
      element={
        <ProtectedRoute loader={<PageLoader />}>
          <DefaultLayout>
            <Profile />
          </DefaultLayout>
        </ProtectedRoute>
      }
    />
    <Route path={routes.AUTH.DEFAULT.route} element={<Auth />} />
    <Route
      path={routes.DASHBOARD.DEFAULT.route}
      element={
        <ProtectedRoute loader={<PageLoader />} role={Role.SESSION_OWNER} toLogin>
          <Dashboard />
        </ProtectedRoute>
      }
    />
    <Route path={routes.SESSION_IS_FULL} element={<SessionIsFull />} />
    <Route path={routes.EXTERNAL} element={<External />} />
    <Route path={routes.ERROR.route} element={<Error />} />
    <Route path="*" element={<NotFound />} />
  </Routes>
);
export default AppRoutes;
