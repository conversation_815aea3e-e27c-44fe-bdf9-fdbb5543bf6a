// rest of imports
import { ReactElement } from 'react';
import useStorageSync from '../../hooks/useStorageSync';
import CkConsent from '../CkConsent/CkConsent';
import AppBanner from './Banner/AppBanner';
import AppModals from './Modals/AppModals';
import AppNavigate from './Navigate/AppNavigate';
import Providers from './Providers/AppProviders';
import Routes from './Routes/AppRoutes';
import ScrollToTop from './ScrollToTop/ScrollToTop';
import Version from './Version/Version';

const App = (): ReactElement | null => {
  useStorageSync();
  return (
    <Providers>
      <AppNavigate />
      <ScrollToTop />
      <Routes />
      <CkConsent />
      <AppModals />
      <Version />
      <AppBanner />
    </Providers>
  );
};

export default App;
