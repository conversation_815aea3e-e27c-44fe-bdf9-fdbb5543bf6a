import {
  Box,
  BoxProps,
  Button,
  Flex,
  FlexProps,
  IconButton,
  Select,
  Text,
  useDisclosure,
} from '@chakra-ui/react';
import { faCog } from '@fortawesome/pro-solid-svg-icons';
import { braidService } from '@waitroom/common-api';
import { logger, LogLevel } from '@waitroom/logger';
import {
  LOGGER_MEETING_MEMORY,
  LOGGER_SERVICE_AUTH,
  LOGGER_SERVICE_BRAID,
  LOGGER_SERVICE_CHAT,
  LOGGER_SERVICE_DEVICES,
  LOGGER_SERVICE_EFFECTS_SDK,
  LOGGER_SERVICE_STREAM,
} from '@waitroom/models';
import copy from 'copy-to-clipboard';
import { lazy, ReactElement, useMemo } from 'react';
import { Helmet } from 'react-helmet-async';
import { environments, version } from '../../../../config';
import { useKeyDown } from '../../../../hooks/useKeyPress';
import { envs } from '../../../../services/envs';
import { updateDebugSettings, useAppStore } from '../../../../store/store';
import { Icon } from '../../../Icon/Icon';
import Modal from '../../../Modal/Modal';
import { BraidDevTools } from './BraidDevTools/BraidDevTools';
import { defaultBraidDevToolsState, useBraidTools } from './BraidDevTools/BraidDevTools.store';
import Branch, { usePreviewBranch } from './Branch';
import StreamLogs from './StreamLogs';
import StreamStats from './StreamStats';

const servicesArr = [
  LOGGER_SERVICE_AUTH,
  LOGGER_SERVICE_BRAID,
  LOGGER_MEETING_MEMORY,
  LOGGER_SERVICE_CHAT,
  LOGGER_SERVICE_STREAM,
  LOGGER_SERVICE_DEVICES,
  LOGGER_SERVICE_EFFECTS_SDK,
];
const levelsArray: LogLevel[] = [0, 1, 2, 3, 4, 5, 6];

const ReactQueryDevtools = lazy(() =>
  import('@tanstack/react-query-devtools/build/modern/production.js').then((d) => ({
    default: d.ReactQueryDevtools,
  })),
);

// init braid dev tools and logs on dev
useBraidTools.setState(() => defaultBraidDevToolsState);
braidService.addListener((event) => {
  logger.logService(LOGGER_SERVICE_BRAID, 5, event);
  useBraidTools.setState((prev) => {
    const prevItem = prev.data[event.key];
    const type = event.type;
    return {
      ...prev,
      data: {
        ...prev.data,
        [event.key]: {
          errors: type === 'error' ? [event.data, ...(prevItem?.errors || [])] : prevItem?.errors,
          lastUpdate: type === 'patch' ? Date.now() : prevItem?.lastUpdate,
          events: [
            {
              event,
              timestamp: Date.now(),
            },
            ...(prevItem?.events || []),
          ],
        },
      },
    };
  });
});

const sx: BoxProps['sx'] = {
  position: 'fixed',
  bottom: 3,
  left: 0,
  zIndex: 'overlay',
  display: 'flex',
  alignItems: 'center',
  gap: 1,
  '> div .tsqd-open-btn-container': {
    position: 'relative!important',
    m: '0!',
    transform: 'scale(0.75)',
    left: -20,
  },
  '&:not(:hover) > div .tsqd-open-btn-container': {
    w: '0px',
    h: '0px',
  },
  '> .dev-btn': {
    ml: -2.5,
    mb: 1,
    animation: 'all .15s ease-in',
    opacity: 0.5,
  },
  _hover: {
    '> .dev-btn': {
      ml: '1!',
      opacity: 1,
    },
    '> div .tsqd-open-btn-container': {
      left: 0,
      bottom: 1,
    },
  },
};

const flexSx: FlexProps = {
  flexDirection: { base: 'column', md: 'row' },
  alignItems: { md: 'center' },
  columnGap: 2,
};

const PreviewLabel = (): ReactElement | null => {
  const { id } = usePreviewBranch();
  if (!id?.length) return null;
  return (
    <Text
      as="span"
      position={'fixed'}
      bottom={0}
      left={0}
      color={'gray.800'}
      fontSize={'2xs'}
      title="Preview Branch"
      bgColor={'blackAlpha.200'}
      rounded={'sm'}
      px={1}
      m={1}
    >
      {id}
    </Text>
  );
};

const DevTools = (): ReactElement | null => {
  const devModal = useDisclosure();
  const braidTools = useDisclosure();
  const { onToggle: toggleDev } = devModal;
  const { onToggle: toggleBraid } = braidTools;
  const { logLevel = 0 } = useAppStore.use.debug() || {};

  const keyPressActions = useMemo(() => {
    return {
      KeyT: (ev: KeyboardEvent) => {
        if (ev.altKey && ev.shiftKey) {
          ev.preventDefault();
          toggleDev();
        }
      },
      KeyB: (ev: KeyboardEvent) => {
        if (ev.altKey && ev.shiftKey) {
          ev.preventDefault();
          toggleBraid();
        }
      },
    };
  }, [toggleBraid, toggleDev]);
  useKeyDown(keyPressActions);

  if (environments.isProd) return null;
  return (
    <>
      <Helmet>
        <meta name="version" content={`v${version}`} />
      </Helmet>
      <Box sx={sx}>
        <IconButton
          className="dev-btn"
          aria-label={'Debug settings'}
          colorScheme={'gray.200'}
          size={'3xs'}
          onClick={devModal.onOpen}
        >
          <Icon icon={faCog} size={'lg'} />
        </IconButton>
        <ReactQueryDevtools initialIsOpen={false} buttonPosition={'bottom-left'} />
      </Box>
      <PreviewLabel />
      <Modal isOpen={devModal.isOpen} onClose={devModal.onClose} isCentered size={'md'}>
        <Modal.Header>
          <div>
            Debug settings
            <Text
              fontSize={'sm'}
              color={'gray.500'}
              fontWeight={'normal'}
              onClick={() => copy(version)}
              role="button"
              cursor={'pointer'}
              title="Copy"
            >
              v{version} {environments.isDev ? 'dev' : 'stg'}
            </Text>
          </div>
        </Modal.Header>
        <Modal.Body fontSize={'sm'} display={'flex'} flexDirection={'column'} gap={6}>
          <Flex gap={2} wrap={'wrap'}>
            <Button
              size={'2xs'}
              colorScheme={'gray.100'}
              boxShadow={'none'}
              onClick={() => logger.logType('info', envs)}
            >
              Log envs
            </Button>
            <Button
              size={'2xs'}
              colorScheme={'gray.100'}
              boxShadow={'none'}
              title={'( Shift+Alt+B )'}
              onClick={() => {
                braidTools.onOpen();
                devModal.onClose();
              }}
            >
              Braid dev tools{' '}
            </Button>
          </Flex>
          <Flex direction={'column'} gap={2}>
            <Flex sx={flexSx}>
              <Branch />
            </Flex>
            <Flex sx={flexSx}>
              <Text flexShrink={0} minW={160}>
                Log level (0=none, 5=all):
              </Text>
              <Select
                size={'xs'}
                fontSize={'sm'}
                value={logLevel}
                onChange={(ev) => {
                  const newLevel = Number(ev.currentTarget.value) as LogLevel;
                  logger.config.logLevel = newLevel;
                  updateDebugSettings({
                    logLevel: newLevel,
                  });
                }}
              >
                {levelsArray.map((val) => (
                  <option key={val} value={val}>
                    Level {val}
                  </option>
                ))}
              </Select>
            </Flex>
            <Flex sx={flexSx}>
              <Text flexShrink={0} minW={160}>
                Service log filter:
              </Text>
              <Select
                size={'xs'}
                fontSize={'sm'}
                defaultValue={logger.config.filter}
                multiple={false}
                onChange={(ev) => {
                  const value = ev.currentTarget.value;
                  logger.config.filter = value.length ? [value] : undefined;
                }}
              >
                <option value={''}>-</option>
                {servicesArr.map((val) => (
                  <option key={val} value={val}>
                    {val}
                  </option>
                ))}
              </Select>
            </Flex>
            <Flex sx={flexSx}>
              <Text flexShrink={0} w={160}>
                LiveKit log:
              </Text>
              <StreamLogs />
            </Flex>
            <Flex sx={flexSx} justifyContent={'space-between'}>
              <Text flexShrink={0} w={160}>
                Stream stats:
              </Text>
              <StreamStats />
            </Flex>
          </Flex>
        </Modal.Body>
      </Modal>
      <BraidDevTools {...braidTools} />
    </>
  );
};
export default DevTools;
