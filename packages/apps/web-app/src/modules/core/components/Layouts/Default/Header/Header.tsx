import { Box, BoxProps, Container, DarkMode, Flex, Image, LightMode } from '@chakra-ui/react';
import { ReactElement, ReactNode, memo } from 'react';
import { routes } from '../../../../../../constants/routes';
import { CDN_LOGO_URL, commonConfig } from '../../../../config';
import { stickyHeaderSx } from '../../../Common/styles';
import { BlockLink } from '../../../Link/Link';
import ResponsiveMenu, { MenuItem } from '../../../Menu/Menu';
import { darkProps, lightProps, menuProps } from './Header.styles';
import { useComponent } from './useComponent';

export interface HeaderProps extends BoxProps {
  showLogo?: boolean;
  dark?: boolean;
  sticky?: boolean;
  width?: string | number;
  auth?: boolean;
  host?: boolean;
  dashboard?: boolean;
  defaultItems?: boolean;
  menuItems?: MenuItem[];
  children?: ReactNode | null;
}

const Header = memo(
  ({
    width = 'container.2xl',
    showLogo = true,
    sticky = true,
    host = true,
    auth = true,
    dashboard = true,
    defaultItems = true,
    dark,
    menuItems,
    children,
    ...rest
  }: HeaderProps): ReactElement | null => {
    const { items } = useComponent({
      auth,
      host,
      dashboard,
      menuItems,
      defaultItems,
    });
    const Mode = dark ? DarkMode : LightMode;
    const modeProps = dark ? darkProps : lightProps;
    return (
      <Box
        position={sticky ? 'sticky' : undefined}
        sx={stickyHeaderSx}
        data-id="Header"
        {...modeProps}
      >
        {children}
        <Mode>
          <Flex as="header" py={3} w="100%" minH="76px" align="center" {...modeProps} {...rest}>
            <Container maxW={width}>
              <Flex w="100%" align="center">
                <Box mr={4} flexShrink={0}>
                  {showLogo ? (
                    <BlockLink to={routes.HOME}>
                      <Image
                        className="logo"
                        src={`${CDN_LOGO_URL}/${dark ? 'rumi-logo-white.svg' : 'rumi-logo.svg'}`}
                        htmlWidth="110"
                        htmlHeight="35"
                        w="110px"
                        h="auto"
                        alt={commonConfig.company.name}
                      />
                    </BlockLink>
                  ) : null}
                </Box>
                <ResponsiveMenu items={items} breakpoint="lg" menuProps={menuProps} />
              </Flex>
            </Container>
          </Flex>
        </Mode>
      </Box>
    );
  },
);

export default Header;
