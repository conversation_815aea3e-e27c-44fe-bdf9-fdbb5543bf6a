import {
  Box,
  BoxProps,
  Drawer,
  DrawerContent,
  Flex,
  IconButton,
  useBreakpointValue,
  useDisclosure,
  UseDisclosureReturn,
} from '@chakra-ui/react';
import { faArrowLeftFromLine, faArrowRightToLine } from '@fortawesome/pro-solid-svg-icons';
import { memo, ReactElement, ReactNode } from 'react';
import { useTranslation } from 'react-i18next';
import { useLocation } from 'react-router-dom';
import { BreakpointKeys } from '../../../theme/breakpoints';
import { Icon } from '../Icon/Icon';
import NavItem, { MenuItem } from './NavItem';

type SideMenuBaseProps = {
  breakpoint?: BreakpointKeys;
  narrowBreakpoint?: BreakpointKeys;
  items?: MenuItem[];
  topUi?: ReactNode;
  bottomUi?: ReactNode;
  placement?: 'left' | 'right';
};

export type SideMenuContentProps = BoxProps &
  SideMenuBaseProps & {
    onClose: () => void;
  };
const SideMenuContent = ({
  breakpoint = 'xl',
  narrowBreakpoint,
  items,
  topUi,
  bottomUi,
  placement = 'left',
  onClose,
  children,
  ...rest
}: SideMenuContentProps) => {
  const { t } = useTranslation();
  const { pathname } = useLocation();
  const path = pathname.replace(/\/$/, '');
  const itemDisplay = narrowBreakpoint
    ? { base: 'inline', [breakpoint]: 'none', [narrowBreakpoint]: 'inline' }
    : undefined;

  return (
    <Flex
      direction={'column'}
      w={'full'}
      h={'full'}
      gap={1}
      zIndex={1110} // sticky + 10
      bg={'white'}
      _dark={{
        bg: 'gray.900',
      }}
      {...rest}
    >
      {topUi}
      <IconButton
        position={'absolute'}
        display={{ base: 'inline-flex', [breakpoint]: 'none' }}
        top={4}
        right={placement === 'left' ? 4 : undefined}
        left={placement === 'right' ? 4 : undefined}
        onClick={onClose}
        colorScheme={'gray.100'}
        color={'gray.300'}
        size={'xs'}
        zIndex={2}
        boxShadow={'none'}
        aria-label={t('common.close')}
      >
        <Icon
          icon={placement === 'left' ? faArrowLeftFromLine : faArrowRightToLine}
          fontSize={'lg'}
        />
      </IconButton>
      {!!items?.length && (
        <Flex
          gap={1}
          direction={'column'}
          w={'full'}
          flexGrow={1}
          overflow={'auto'}
          px={
            narrowBreakpoint
              ? {
                  base: 3,
                  [breakpoint]: 2,
                  [narrowBreakpoint]: 3,
                }
              : undefined
          }
        >
          {items.map((item, i) => (
            <NavItem
              key={i}
              item={item}
              path={path}
              onClose={onClose}
              textDisplay={itemDisplay}
              justifyContent={
                narrowBreakpoint
                  ? { [breakpoint]: 'center', [narrowBreakpoint]: 'flex-start' }
                  : undefined
              }
            />
          ))}
        </Flex>
      )}
      {children}
      <Box mt={'auto'} w={'full'}>
        {bottomUi}
      </Box>
    </Flex>
  );
};

export type SideMenuProps = BoxProps &
  SideMenuBaseProps &
  Pick<UseDisclosureReturn, 'isOpen' | 'onOpen' | 'onClose'>;

const SideMenu = memo(
  ({
    breakpoint = 'xl',
    width = 250,
    onOpen,
    onClose,
    isOpen,
    ...rest
  }: SideMenuProps): ReactElement | null => {
    const internalDisclosure = useDisclosure({ isOpen, onOpen, onClose });
    const isSmall = useBreakpointValue({ base: true, [breakpoint]: false });

    return (
      <>
        {isSmall ? (
          <Drawer
            {...internalDisclosure}
            placement={rest.placement || 'left'}
            returnFocusOnClose={false}
            onOverlayClick={internalDisclosure.onClose}
          >
            <DrawerContent maxW={width}>
              <SideMenuContent
                breakpoint={breakpoint}
                width={width}
                {...rest}
                onClose={internalDisclosure.onClose}
              />
            </DrawerContent>
          </Drawer>
        ) : (
          <>
            <SideMenuContent
              breakpoint={breakpoint}
              width={width}
              {...rest}
              onClose={() => internalDisclosure.onClose}
            />
          </>
        )}
      </>
    );
  },
);
export default SideMenu;
