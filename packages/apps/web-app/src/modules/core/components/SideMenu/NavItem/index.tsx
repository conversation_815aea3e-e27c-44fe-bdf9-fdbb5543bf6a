import { Button, ButtonProps, Text, TextProps } from '@chakra-ui/react';
import { ReactElement, ReactNode } from 'react';
import { Link } from 'react-router-dom';
import { Icon, IconBox, IconProps } from '../../Icon/Icon';

export type Render = null | ((path: string, onClick?: () => void) => ReactElement | null);
export type MenuItem =
  | {
      to: string;
      content: ReactNode;
      icon?: IconProps['icon'];
      isActive: ((path: string) => boolean) | string;
      closeOnClick?: boolean;
      title?: string;
    }
  | { render: Render };

export type NavItemProps = ButtonProps & {
  item: MenuItem;
  path: string;
  onClose?: () => void;
  textDisplay?: TextProps['display'];
};
const NavItem = ({ item, path, onClose, textDisplay, ...rest }: NavItemProps) => {
  if (!item) return null;
  if ('render' in item && item.render) return item.render(path, onClose);
  if (!('to' in item)) return null;

  const active = typeof item.isActive === 'function' ? item.isActive(path) : path === item.to;
  return (
    <Button
      as={Link}
      to={item.to}
      className={'none'}
      variant={'ghost'}
      size={'sm'}
      w={'full'}
      gap={3}
      px={2}
      isActive={active}
      justifyContent={'flex-start'}
      rounded={'xl'}
      onClick={item.closeOnClick !== false ? onClose : undefined}
      title={item.title}
      {...rest}
    >
      {!!item.icon && (
        <IconBox size={'2xs'} bgColor={'gray.800'} rounded={'lg'}>
          <Icon icon={item.icon} fontSize={'lg'} color={'gray.300'} />
        </IconBox>
      )}
      <Text as={'span'} display={textDisplay}>
        {item.content}
      </Text>
    </Button>
  );
};

export default NavItem;
