import { Alert, AlertProps, List, ListItem } from '@chakra-ui/react';
import { ReactElement, useEffect, useRef, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { getResponseMessage } from '../../utils/api';
import { FormResponseProps, FormValidationProps } from './Form.types';

export const Validation = ({ errors = {}, ...rest }: FormValidationProps): ReactElement | null => {
  const keys = Object.keys(errors);
  if (!keys.length) return null;
  return (
    <List color="red.700" fontSize="sm" {...rest}>
      {keys.map((key) => {
        const curr = errors[key];
        return curr && curr.message ? <ListItem key={key}>{String(curr.message)}</ListItem> : null;
      })}
    </List>
  );
};

export const Response = ({
  component: Component = Alert,
  response = {},
  namespace,
  fallbackToDefault,
  ...rest
}: FormResponseProps & AlertProps): ReactElement | null => {
  const { t } = useTranslation();
  const [success, msg] = getResponseMessage({ response, t, namespace });
  const message = fallbackToDefault ? msg || t(`responses.default`) : msg;

  if (!message || !response) return null;
  return (
    <Component status={success ? 'success' : 'error'} fontSize="sm" p={2} {...rest}>
      {message}
    </Component>
  );
};

export const DisappearingResponse = ({
  timeout,
  response,
  ...rest
}: FormResponseProps & AlertProps & { timeout?: number }): ReactElement | null => {
  const timeoutRef = useRef<SetTimeout>(null);
  const [visible, setVisible] = useState(true);

  useEffect(() => {
    if (!timeout) return;
    if (response) setVisible(true);
    const t = timeoutRef.current;
    if (t) clearTimeout(t);
    setTimeout(() => setVisible(false), timeout);
  }, [response, timeout]);

  if (!visible) return null;
  return <Response response={response} {...rest} />;
};
