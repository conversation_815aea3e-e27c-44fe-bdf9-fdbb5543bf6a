import { useCallback, useEffect, useRef } from 'react';
import { useIntersectionObserver } from '../../hooks/useIntersectionObserver';
import { useResizeObserver } from '../../hooks/useResizeObserver';
import { scrollToBottom, scrollToWindowBottom } from '../../utils/dom';

export const useAutoScroll = (
  toBottom?: boolean,
  target?: HTMLElement | Window,
  behavior?: ScrollBehavior,
) => {
  const enabledRef = useRef(true);
  const behaviorRef = useRef(behavior);
  const timeoutRef = useRef<SetTimeout>(undefined);
  behaviorRef.current = behavior;

  const resizeCallback = useCallback(
    (entries: ResizeObserverEntry[]) => {
      const entry = entries[0];
      if (!entry || !enabledRef.current) return;
      const element = target || entry.target?.parentElement;
      if (!element) return;
      if ('scroll' in element && typeof element.scroll === 'function') {
        if (element === window) scrollToWindowBottom(behaviorRef.current);
        else scrollToBottom(element as HTMLElement);
      } else {
        (element as HTMLElement).scrollTop = entry.target.scrollHeight;
      }
    },
    [target],
  );
  const { ref: containerRef } = useResizeObserver<HTMLDivElement>(resizeCallback);

  const intersectionCallback = useCallback<IntersectionObserverCallback>(([entry]) => {
    const is = entry.isIntersecting;
    if (timeoutRef.current) clearTimeout(timeoutRef.current);
    if (is) {
      enabledRef.current = is;
      return;
    }
    // delay setting to false
    timeoutRef.current = setTimeout(() => {
      enabledRef.current = is;
    }, 100);
  }, []);
  const { ref: bottomRef } = useIntersectionObserver<HTMLDivElement>(
    { rootMargin: '45px 0px' },
    intersectionCallback,
  );

  useEffect(() => {
    if (!toBottom) return;
    bottomRef.current?.scrollIntoView();
  }, [bottomRef, toBottom]);

  return {
    containerRef,
    bottomRef,
  };
};
