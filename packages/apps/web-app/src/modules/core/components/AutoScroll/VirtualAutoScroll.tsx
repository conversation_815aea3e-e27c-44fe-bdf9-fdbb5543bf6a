import type { Virtualizer } from '@tanstack/react-virtual';
import { CSSProperties, ReactNode, useCallback, useEffect, useRef } from 'react';
import { useIntersectionObserver } from '../../hooks/useIntersectionObserver';

export type UseVirtualAutoScrollProps = {
  /** Items count */
  count: number;
  /** Scroll to bottom function */
  scrollTo: Virtualizer<Element, Element>['scrollToIndex'];
  /** Scroll to bottom on mount */
  toBottom?: boolean;
};

const useVirtualAutoScroll = ({ count, scrollTo, toBottom = true }: UseVirtualAutoScrollProps) => {
  const prevCount = useRef<number>(0);
  const scrollFnRef = useRef(() => scrollTo(count - 1, { align: 'end' }));
  const enabledRef = useRef(true);
  const intersectionCallback = useCallback<IntersectionObserverCallback>(([entry]) => {
    const is = entry.isIntersecting;
    enabledRef.current = is;
  }, []);
  const { ref: bottomRef } = useIntersectionObserver<HTMLDivElement>(
    { rootMargin: '0px 0px 200px 0px' },
    intersectionCallback,
  );

  useEffect(() => {
    scrollFnRef.current = () => scrollTo(count - 1, { align: 'end' });
  }, [count, scrollTo]);

  useEffect(() => {
    if (!toBottom) return;
    // https://github.com/TanStack/virtual/issues/615
    setTimeout(() => {
      scrollFnRef.current();
    }, 1);
  }, [toBottom]);

  useEffect(() => {
    if (count > prevCount.current) {
      prevCount.current = count;
      scrollFnRef.current();
    }
  }, [count]);

  return {
    bottomRef,
  };
};

export type VirtualAutoScrollProps = UseVirtualAutoScrollProps & {
  children: ReactNode;
};
const bottomStyle: CSSProperties = {
  height: 1,
  minHeight: 1,
};

const VirtualAutoScroll = ({ children, ...rest }: VirtualAutoScrollProps) => {
  const { bottomRef } = useVirtualAutoScroll(rest);
  return (
    <>
      {children}
      <div style={bottomStyle} ref={bottomRef} />
    </>
  );
};
export default VirtualAutoScroll;
