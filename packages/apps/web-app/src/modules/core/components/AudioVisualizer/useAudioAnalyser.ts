import testSound from '@sounds/test-sound.mp3';
import { useEffect, useRef, useState } from 'react';

function sourceFromInput(
  audioContext: AudioContext,
  input: MediaStream | HTMLMediaElement,
): AudioNode {
  return input instanceof MediaStream
    ? audioContext.createMediaStreamSource(input)
    : audioContext.createMediaElementSource(input);
}

const AudioContext = window.AudioContext || window.webkitAudioContext;
function createAudioContextAndAnalyser(
  input: MediaStream | HTMLMediaElement,
): [AudioContext, AnalyserNode, AudioNode] | undefined {
  const audioContext = new AudioContext();
  if (!audioContext) return;
  const source = sourceFromInput(audioContext, input);
  const analyser = audioContext.createAnalyser();
  analyser.fftSize = 256;
  analyser.minDecibels = -75;
  analyser.maxDecibels = -5;
  analyser.smoothingTimeConstant = 0.85;
  source.connect(analyser);
  return [audioContext, analyser, source];
}

export function useTestSoundAnalyser(): [AnalyserNode | undefined, HTMLAudioElement | undefined] {
  const analyserRef = useRef<AnalyserNode | undefined>(undefined);
  const audioFileRef = useRef<HTMLAudioElement | undefined>(undefined);
  const [analyser, setAnalyser] = useState<AnalyserNode | undefined>(undefined);

  useEffect(() => {
    const loadMedia = async (): Promise<void> => {
      if (audioFileRef.current) {
        audioFileRef.current.pause();
        audioFileRef.current = undefined;
      }
      const audioFile = new Audio();
      audioFileRef.current = audioFile;
      audioFile.src = testSound;
      const response = createAudioContextAndAnalyser(audioFile);
      if (!response) return;
      const [audioContext, analyser, source] = response;
      source.connect(audioContext.destination);
      analyserRef.current = analyser;
      setAnalyser(analyser);
      audioFile.loop = true;
      audioFile.play();
    };
    loadMedia();

    return function cleanup(): void {
      audioFileRef.current?.pause();
    };
  }, []);

  return [analyser, audioFileRef.current];
}

export function useGetAnalyser(track: MediaStreamTrack | undefined) {
  const [analyser, setAnalyser] = useState<AnalyserNode | undefined>();

  useEffect(() => {
    if (!track) {
      setAnalyser(undefined);
      return;
    }
    const ms = new MediaStream();
    ms.addTrack(track);
    setAnalyser(createAudioContextAndAnalyser(ms)?.[1]);
  }, [track]);

  return analyser;
}
