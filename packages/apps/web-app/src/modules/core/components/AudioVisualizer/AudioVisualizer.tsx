import { Box, BoxProps, Flex, Stack } from '@chakra-ui/react';
import { memo, useCallback, useEffect, useMemo, useRef } from 'react';

export type VisualizerSize = 'sm' | 'md' | 'lg';
export type AudioVisualizerProps = {
  bandColor?: string;
  analyser?: AnalyserNode;
  height?: number | string;
  bandNumber?: number;
  silentIndicator?: boolean;
  silentIndicatorColor?: string;
  size?: VisualizerSize;
};

const bandWidth: Record<VisualizerSize, BoxProps['w']> = {
  sm: [0.3, 0.4, 0.5],
  md: [0.4, 0.5, 0.6],
  lg: [0.5, 0.8, 0.9],
};

const createBandsArr = (num: number): number[] => {
  const arr: number[] = [];
  for (let i = 0; i < num; i++) {
    if (i % 2 === 0) arr.unshift(i);
    else arr.push(i);
  }
  return arr;
};

const AudioVisualizer = memo(
  ({
    analyser,
    height = 20,
    bandNumber = 42,
    size = 'md',
    bandColor = 'gray.900',
    silentIndicatorColor = 'gray.400',
    silentIndicator,
  }: AudioVisualizerProps): React.ReactElement | null => {
    const silentRef = useRef<HTMLDivElement>(null);
    const wrapperRef = useRef<HTMLDivElement>(null);
    const requestRef = useRef<number | undefined>(undefined);
    const audioLastActiveTs = useRef<number | undefined>(undefined);
    const bands = useMemo(() => createBandsArr(bandNumber), [bandNumber]);

    const getFrequencyData = useCallback(
      (styleAdjuster: (newAmplitudeData: Uint8Array | undefined) => void): void => {
        if (!analyser) {
          styleAdjuster(undefined);
          return;
        }
        const bufferLength = analyser.frequencyBinCount;
        const amplitudeArray = new Uint8Array(bufferLength);
        analyser.getByteFrequencyData(amplitudeArray);
        styleAdjuster(amplitudeArray);
      },
      [analyser],
    );

    const runSpectrum = useCallback(() => {
      getFrequencyData((newAmplitudeData: Uint8Array | undefined): void => {
        const wrapper = wrapperRef.current;
        if (!wrapper) return;
        let isSilent = true;
        const elements = wrapper.children;
        let isSilentVisible = true;
        if (newAmplitudeData) {
          for (let i = 0; i < bands.length; i++) {
            const band = bands[i];
            isSilent = isSilent && newAmplitudeData[band] <= 0;
            const elem = elements[i] as HTMLDivElement;
            if (!elem) return;
            elem.style.height = `${newAmplitudeData[band]}px`;
            elem.style.borderRadius = `6px`;
          }
          if (!isSilent) audioLastActiveTs.current = Date.now();
          isSilentVisible =
            !!silentIndicator &&
            !!isSilent &&
            (audioLastActiveTs.current === undefined ||
              audioLastActiveTs.current + 500 < Date.now());
        }
        if (silentRef.current) {
          silentRef.current.style.display = isSilentVisible ? 'flex' : 'none';
          wrapper.style.display = isSilentVisible ? 'none' : 'flex';
        }
      });
      requestRef.current = requestAnimationFrame(runSpectrum);
    }, [bands, getFrequencyData, silentIndicator]);

    useEffect(() => {
      requestRef.current = requestAnimationFrame(runSpectrum);
      return () => {
        if (!requestRef.current) return;
        cancelAnimationFrame(requestRef.current);
      };
    }, [runSpectrum]);

    return (
      <>
        <Stack
          ref={wrapperRef}
          display={analyser ? 'flex' : 'none'}
          direction="row"
          w="full"
          h={height}
          spacing={1}
          alignSelf="stretch"
          alignContent="center"
          alignItems="center"
          justifyContent="center"
          justifyItems="center"
        >
          {bands.map((index) => (
            <Box
              key={`${index}`}
              className="frequencyBands"
              borderRadius={1}
              maxHeight={height}
              width={bandWidth[size]}
              minW="1px"
              bgColor={bandColor}
            />
          ))}
        </Stack>
        {!!silentIndicator && (
          <Flex
            ref={silentRef}
            w="100%"
            h={height}
            display={!analyser ? 'none' : 'flex'}
            align="center"
            px={1}
          >
            <Box w="100%" height="2px" rounded="lg" bg={silentIndicatorColor} />
          </Flex>
        )}
      </>
    );
  },
);

export default AudioVisualizer;
