import { useDrag } from '@use-gesture/react';
import { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import { carouselActiveCss } from './Carousel.styles';
import { Arrows, Direction, UseCarouselProps, UseCarouselResponse } from './types';

interface State {
  index: number;
  length: number;
  arrows: Arrows;
}
const defaultState = {
  index: 0,
  length: 0,
  arrows: {},
};

const showArrows = (len: number, current: number): Arrows => {
  const left = current !== 0;
  const right = current < len - 1;
  return {
    has: left || right,
    left,
    right,
  };
};

const checkIndex = (idx: number, len: number, loop?: boolean): number | undefined => {
  if (idx >= 0 && idx < len) return idx;
  return loop ? (idx < 0 ? len - 1 : 0) : undefined;
};

export const useCarousel = ({ loop, interval }: UseCarouselProps): UseCarouselResponse => {
  const timer = useRef<ReturnType<typeof setInterval> | undefined>(undefined);
  const clearTimer = useCallback(() => {
    if (timer.current) clearInterval(timer.current);
  }, []);
  const [state, setState] = useState<State>(defaultState);

  const setLength = useCallback((length: number) => {
    setState((prev) => {
      return {
        ...prev,
        length,
        arrows: showArrows(length, prev.index),
      };
    });
  }, []);

  const slide = useCallback(
    (direction: Direction, stopTimer?: boolean) => {
      if (stopTimer) clearTimer();
      setState((prev) => {
        const index = checkIndex(prev.index + direction, prev.length, loop);
        if (index === undefined) return prev;
        return {
          ...prev,
          index,
          arrows: showArrows(prev.length, index),
        };
      });
    },
    [clearTimer, loop],
  );

  const setSlide = useCallback(
    (idx: number | undefined, stopTimer?: boolean) => {
      if (stopTimer) clearTimer();
      setState((prev) => {
        const index = checkIndex(idx ?? prev.index + 1, prev.length, loop);
        if (index === undefined) return prev;
        return {
          ...prev,
          index,
          arrows: showArrows(prev.length, index),
        };
      });
    },
    [clearTimer, loop],
  );

  const drag = useDrag(
    ({ swipe: [x] }) => {
      if (x === 0) return;
      slide(x === 1 ? Direction.Right : Direction.Left, true);
    },
    {
      axis: 'x',
      swipe: {
        distance: 40,
      },
      preventDefault: true,
      filterTaps: true,
    },
  );

  useEffect(() => {
    if (!interval) return;
    clearTimer();
    timer.current = setInterval(() => slide(Direction.Right), interval);
    return () => clearTimer();
  }, [interval, slide, clearTimer]);

  const props = useMemo(
    () => ({
      ...state,
      slide,
      setSlide,
      setLength,
      drag,
      props: {
        sx: carouselActiveCss(state.index),
      },
    }),
    [state, slide, setSlide, setLength, drag],
  );

  return props;
};
