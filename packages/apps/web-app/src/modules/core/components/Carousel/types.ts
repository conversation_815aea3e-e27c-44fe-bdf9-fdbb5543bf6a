import { BoxProps } from '@chakra-ui/react';
import { useDrag } from '@use-gesture/react';
import { ReactElement } from 'react';

/**
 * Carousel types. Slide carousel does not support index/pages
 */
export type CarouselType = 'slide' | 'fade';

export type Arrows = {
  has?: boolean;
  left?: boolean;
  right?: boolean;
};

export type Item = ReactElement;

export enum Direction {
  Left = -1,
  Right = 1,
}
export enum ArrowKeys {
  Right = 39,
  Left = 37,
}

export interface UseCarouselProps {
  loop?: boolean;
  interval?: number;
  allowDrag?: boolean;
}

export interface UseCarouselResponse {
  arrows: Arrows;
  index: number;
  length: number;
  setLength: (val: number) => void;
  slide: (direction: Direction, stopTimer?: boolean) => void;
  setSlide: (index: number, stopTimer?: boolean) => void;
  props: BoxProps & {
    ref?: React.RefObject<HTMLDivElement | null> | ((node: HTMLDivElement) => void);
  };
  drag?: ReturnType<typeof useDrag>;
}
