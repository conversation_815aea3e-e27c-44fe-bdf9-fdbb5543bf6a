import { useDrag } from '@use-gesture/react';
import { useDebounceFn } from 'ahooks';
import { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import { Arrows, Direction, UseCarouselProps, UseCarouselResponse } from './types';

interface State {
  index: number;
  length: number;
  arrows: Arrows;
}

const defaultState = {
  index: 0,
  length: 0,
  arrows: {},
};

const getOuterWidth = (el: HTMLElement) => {
  const style = getComputedStyle(el);
  return (
    el.offsetWidth + (parseInt(style.marginLeft, 10) || 0) + (parseInt(style.marginRight, 10) || 0)
  );
};

const checkIndex = (
  slider: HTMLDivElement,
  idx: number,
  len: number,
  loop?: boolean,
): number | undefined => {
  if (loop && Math.abs(slider.scrollWidth - slider.scrollLeft - slider.clientWidth) < 1) {
    return 0;
  }
  if (idx >= 0 && idx < len) return idx;
  return loop ? (idx < 0 ? len - 1 : 0) : undefined;
};

export const useSlideCarousel = ({ loop, interval }: UseCarouselProps): UseCarouselResponse => {
  const timer = useRef<ReturnType<typeof setInterval>>(undefined);
  const clearTimer = useCallback(() => {
    if (timer.current) clearInterval(timer.current);
  }, []);
  const slider = useRef<HTMLDivElement>(null);

  const showArrows = useCallback((): Arrows => {
    const sliderElem = slider.current;
    const left = !!sliderElem && sliderElem.scrollLeft > 0;
    const right =
      !!sliderElem && sliderElem.scrollWidth - 1 >= sliderElem.scrollLeft + sliderElem.offsetWidth;
    return {
      has: left || right,
      left,
      right,
    };
  }, []);

  const [state, setState] = useState<State>(defaultState);
  const setLength = useCallback(
    (length: number) => {
      setState((prev) => {
        return {
          ...prev,
          length,
          arrows: showArrows(),
        };
      });
    },
    [showArrows],
  );

  const { run } = useDebounceFn(
    (_: Event) => setState((prev) => ({ ...prev, arrows: showArrows() })),
    {
      wait: 200,
    },
  );

  const onScroll = useCallback(run, [run]);

  const ref = useCallback(
    (node: HTMLDivElement) => {
      if (node !== null) {
        Object.defineProperty(slider, 'current', { value: node });
        setState((prev) => ({ ...prev, arrows: showArrows() }));
        node.addEventListener('scroll', onScroll);
      }
    },
    [onScroll, showArrows],
  );

  const calcSlideAmount = useCallback((direction: Direction): number => {
    const sl = slider.current;
    if (!sl) return 0;
    const isRight = direction === Direction.Right;
    const currentView = isRight ? sl.scrollLeft + sl.offsetWidth : sl.scrollLeft;
    const childNodes = Array.from(sl.children) as HTMLElement[];
    const len = childNodes.length;
    let widthSum = 0;
    for (let i = 0; i < len; i++) {
      const nodeWidth = getOuterWidth(childNodes[i]);
      if (widthSum + nodeWidth > currentView) {
        const cutoff = isRight ? currentView - widthSum : widthSum + nodeWidth - currentView;
        return (sl.offsetWidth - cutoff) * direction;
      }
      widthSum += nodeWidth;
    }
    return sl.offsetWidth;
  }, []);

  const scroll = useCallback((amount: number, isDiff = true) => {
    if (!slider.current) return;
    const s = slider.current;
    if (isDiff) s.scrollLeft = s.offsetLeft + amount;
    else s.scrollLeft = amount;
  }, []);

  const animateScroll = useCallback(
    (time: number, amount: number, start: number) => {
      let curTime = 0;
      for (let i = 0; curTime <= time; i++) {
        setTimeout(scroll, curTime, (i * amount) / 100 + start);
        curTime += time / 100;
      }
    },
    [scroll],
  );

  const slide = useCallback(
    (direction: Direction) => {
      if (!slider.current) return;
      const amount = calcSlideAmount(direction);
      const start = slider.current.scrollLeft;
      animateScroll(400, amount, start);
    },
    [calcSlideAmount, animateScroll],
  );

  const setSlide = useCallback(
    (idx: number | undefined, stopTimer?: boolean) => {
      if (stopTimer) clearTimer();
      const sl = slider.current;
      if (!sl) return;
      setState((prev) => {
        const index = checkIndex(sl, idx ?? prev.index + 1, prev.length, loop);
        if (index === undefined) return prev;
        const cs = getComputedStyle(sl);
        const childNodes = Array.from(sl.children) as HTMLElement[];
        const amount = childNodes[index].offsetLeft - parseFloat(cs.paddingLeft);
        const start = sl.scrollLeft;
        animateScroll(400, amount - start, start);
        return {
          ...prev,
          index,
          arrows: showArrows(),
        };
      });
    },
    [animateScroll, clearTimer, loop, showArrows],
  );

  useEffect(() => {
    if (!interval) return;
    clearTimer();
    timer.current = setInterval(() => setSlide(undefined), interval);
    return () => clearTimer();
  }, [interval, clearTimer, setSlide]);

  useEffect(() => {
    // scroll to active on mount
    const sl = slider.current;
    if (!sl) return;
    const activeElem = sl.querySelector<HTMLElement>('[data-active="true"]');
    if (activeElem) scroll(activeElem.offsetLeft, false);
  }, [scroll]);

  const drag = useDrag(
    ({ offset: [x] }) => {
      scroll(-x);
    },
    {
      axis: 'x',
      preventDefault: true,
      filterTaps: true,
    },
  );

  const props = useMemo(
    () => ({
      ...state,
      slide,
      setLength,
      setSlide,
      drag,
      props: {
        ref,
      },
    }),
    [ref, setSlide, slide, state, setLength, drag],
  );

  return props;
};
