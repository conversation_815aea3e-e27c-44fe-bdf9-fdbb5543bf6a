import { Flex, Radio, RadioProps, Tag, TagLabel, TagLeftIcon } from '@chakra-ui/react';
import { IconDefinition } from '@fortawesome/fontawesome-svg-core';
import { ReactElement, ReactNode, forwardRef } from 'react';
import { Icon } from '../../Icon/Icon';

export interface RadioButtonProps extends RadioProps {
  value?: string;
  label?: string;
  description?: string;
  tagIcon?: IconDefinition;
  tagLabel?: string;
  children?: ReactNode;
}

const RadioButton = forwardRef<HTMLInputElement, RadioButtonProps>(
  (
    { value, label, description, children, tagLabel, tagIcon, isChecked, ...rest },
    ref,
  ): ReactElement => {
    return (
      <Radio
        value={`${value}`}
        colorScheme="green"
        size="xl"
        p={4}
        variant="cardOutline"
        ref={ref}
        alignItems="start"
        isChecked={isChecked}
        cursor={rest.isDisabled ? 'not-allowed' : 'pointer'}
        {...rest}
      >
        {!!label && (
          <Flex
            as="span"
            w="100%"
            fontSize="lg"
            fontWeight="bold"
            mt={-1}
            cursor={rest.isDisabled ? 'not-allowed' : 'pointer'}
          >
            {label}
          </Flex>
        )}
        {!!description && (
          <Flex
            as="span"
            w="100%"
            fontSize="sm"
            cursor={rest.isDisabled ? 'not-allowed' : 'pointer'}
          >
            {description}
          </Flex>
        )}
        {children}
        {tagLabel ? (
          <Tag size="md" bgColor={isChecked ? 'red.300' : 'gray.200'} mt={3} py={1} px={2}>
            {tagIcon && <TagLeftIcon as={Icon} icon={tagIcon} color="black" />}
            <TagLabel color="black" fontWeight="bold">
              {tagLabel}
            </TagLabel>
          </Tag>
        ) : null}
      </Radio>
    );
  },
);

export default RadioButton;
