import {
  FormControl as CFormControl,
  FormControlProps as CFormControlProps,
  FormErrorMessage,
  FormErrorMessageProps,
  FormLabel,
  Text,
} from '@chakra-ui/react';
import { ReactElement, ReactNode } from 'react';
import { FieldErrors } from 'react-hook-form';

export interface FormControlProps extends Omit<CFormControlProps, 'label'> {
  label?: ReactNode;
  labelSubtext?: ReactNode;
  subtext?: ReactNode;
  suffix?: ReactNode;
  error?: FieldErrors[string];
  errorMessage?: boolean;
  errorSize?: FormErrorMessageProps['fontSize'];
  sxError?: FormErrorMessageProps['sx'];
}

export const FormControl = ({
  label,
  labelSubtext,
  subtext,
  error,
  errorMessage = true,
  errorSize = 'xs',
  suffix,
  sxError,
  children,
  ...rest
}: FormControlProps): ReactElement | null => {
  return (
    <CFormControl isInvalid={!!error} {...rest}>
      {label || labelSubtext ? (
        <FormLabel mb={subtext ? 0 : 2} fontSize={rest.fontSize}>
          {label}
          {labelSubtext ? (
            <Text as={'span'} layerStyle={'label-subtext'} ml={1}>
              {labelSubtext}
            </Text>
          ) : null}
        </FormLabel>
      ) : null}
      {subtext ? (
        <Text mb={2} fontSize={'sm'} color={'gray.700'}>
          {subtext}
        </Text>
      ) : null}
      {children}
      {errorMessage && error && error.message ? (
        <FormErrorMessage fontSize={errorSize} sx={sxError}>
          {String(error.message)}
        </FormErrorMessage>
      ) : null}
      {suffix}
    </CFormControl>
  );
};
