import { BadgeProps, InputProps } from '@chakra-ui/react';
import { RefObject } from 'react';
import type { UseTagsInputResponse } from './useTagsInput';

export interface TagsInputProps extends Omit<InputProps, 'onChange' | 'value'> {
  initialValue?: string[];
  value?: string[];
  maxTags?: number;
  setValue: (options: string[]) => void;
  rendedTag?: (option: string, onRemove: () => void) => React.ReactNode;
  tagProps?: BadgeProps;
  filterTags?: (newTags: string[]) => string[];
  componentRef?: RefObject<UseTagsInputResponse | undefined | null>;
}
