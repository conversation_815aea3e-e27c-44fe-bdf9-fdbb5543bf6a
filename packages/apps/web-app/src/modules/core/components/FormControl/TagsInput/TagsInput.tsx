import { Flex, Input, InputProps, Tag, TagCloseButton, TagLabel } from '@chakra-ui/react';
import { mapObject } from '@waitroom/utils';
import { ReactElement, useMemo } from 'react';
import { sizes } from '../../../../theme/common';
import { TagsInputProps } from './types';
import { useTagsInput } from './useTagsInput';

type SizeKey = keyof typeof sizes;
const mapSizeToMinH = (size: InputProps['size']): InputProps['minH'] => {
  if (Array.isArray(size)) {
    return size.map((i) => (i ? sizes[String(i) as SizeKey]?.h : i));
  }
  switch (typeof size) {
    case 'object':
      return mapObject(size, (value) => sizes[String(value) as SizeKey]?.h);
    default:
      return sizes[String(size) as SizeKey]?.h;
  }
};

const TagsInput = ({
  setValue,
  rendedTag,
  tagProps,
  maxTags,
  placeholder,
  filterTags,
  initialValue,
  value,
  componentRef,
  size,
  ...props
}: TagsInputProps): ReactElement | null => {
  const response = useTagsInput({
    setValue,
    maxTags,
    filterTags,
    value,
    initialValue,
  });
  const { tags, removeTag, onPaste, onKeyPress, onBlur, inputRef, containerRef } = response;

  if (componentRef) componentRef.current = response;
  const tagsUi = useMemo(
    () =>
      tags.map((option) => {
        const remove = () => removeTag(option);
        return rendedTag ? (
          rendedTag(option, remove)
        ) : (
          <Tag flexShrink={0} key={option} my={1} mr={1} px={2} {...tagProps} title={option}>
            <TagLabel>{option}</TagLabel>
            <TagCloseButton ml={1} onClick={remove} />
          </Tag>
        );
      }),
    [removeTag, rendedTag, tagProps, tags],
  );
  return (
    <Input
      as={Flex}
      wrap={'wrap'}
      align={'center'}
      direction={'row'}
      maxH={'none!important'}
      height={'auto!important'}
      minH={mapSizeToMinH(size)}
      cursor={'text'}
      onClick={() => {
        inputRef.current?.focus();
      }}
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      ref={containerRef as any}
      {...props}
    >
      {tags.length > 0 ? tagsUi : null}
      {!props.isReadOnly && !props.isDisabled && (
        <Input
          flexGrow={1}
          variant={'unstyled!'}
          className={props.className}
          w={'auto!'}
          py={'2!'}
          px={'0!'}
          onKeyUp={onKeyPress}
          onPaste={onPaste}
          onBlur={onBlur}
          ref={inputRef}
          size={size}
          placeholder={placeholder}
          cursor="text"
          autoCorrect="off"
          autoComplete="off"
          autoCapitalize="off"
        />
      )}
    </Input>
  );
};

export default TagsInput;
