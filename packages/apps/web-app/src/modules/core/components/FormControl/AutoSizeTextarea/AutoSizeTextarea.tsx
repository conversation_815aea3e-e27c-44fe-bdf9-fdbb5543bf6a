import { Textarea, TextareaProps } from '@chakra-ui/react';
import { forwardRef, ComponentProps } from 'react';
import ResizeTextarea from 'react-textarea-autosize';

export type AutoSizeTextareaProps = TextareaProps & ComponentProps<typeof ResizeTextarea>;

const AutoSizeTextarea = forwardRef<HTMLTextAreaElement, AutoSizeTextareaProps>((props, ref) => (
  <Textarea
    minH="unset"
    overflow="auto"
    w="full"
    ref={ref}
    minRows={1}
    as={ResizeTextarea}
    {...props}
  />
));

export default AutoSizeTextarea;
