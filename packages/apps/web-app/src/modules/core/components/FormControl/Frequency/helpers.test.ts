import { cronToFrequency, frequencyToCron } from './helpers';

describe('frequencyToCron & cronToFrequency', () => {
  it('should convert daily frequency to cron and back', () => {
    const freq = {
      type: 'daily' as const,
      time: new Date(2023, 0, 1, 8, 30, 0, 0), // Jan 1, 2023, 08:30 local
    };
    const cron = frequencyToCron(freq);
    expect(cron).toBe('30 8 * * *');
    const parsed = cronToFrequency(cron);
    expect(parsed.type).toBe('daily');
    expect(parsed.time.getHours()).toBe(8);
    expect(parsed.time.getMinutes()).toBe(30);
  });

  it('should convert weekly frequency with days to cron and back', () => {
    const freq = {
      type: 'weekly' as const,
      time: new Date(2023, 0, 1, 10, 15, 0, 0), // Jan 1, 2023, 10:15 local
      days: [1, 3, 5],
    };
    const cron = frequencyToCron(freq);
    expect(cron).toBe('15 10 * * 1,3,5');
    const parsed = cronToFrequency(cron);
    expect(parsed.type).toBe('weekly');
    expect(parsed.days).toEqual([1, 3, 5]);
    expect(parsed.time.getHours()).toBe(10);
    expect(parsed.time.getMinutes()).toBe(15);
  });

  it('should convert monthly frequency with dates to cron and back', () => {
    const freq = {
      type: 'monthly' as const,
      time: new Date(2023, 0, 1, 22, 0, 0, 0), // Jan 1, 2023, 22:00 local
      dates: [1, 15, 30],
    };
    const cron = frequencyToCron(freq);
    expect(cron).toBe('0 22 1,15,30 * *');
    const parsed = cronToFrequency(cron);
    expect(parsed.type).toBe('monthly');
    expect(parsed.dates).toEqual([1, 15, 30]);
    expect(parsed.time.getHours()).toBe(22);
    expect(parsed.time.getMinutes()).toBe(0);
  });

  it('should handle weekly with no days as wildcard', () => {
    const freq = {
      type: 'weekly' as const,
      time: new Date(2023, 0, 1, 7, 0, 0, 0), // Jan 1, 2023, 07:00 local
      days: [],
    };
    const cron = frequencyToCron(freq);
    expect(cron).toBe('0 7 * * *'); // Should fallback to daily cron
    const parsed = cronToFrequency(cron);
    expect(parsed.type).toBe('daily');
  });

  it('should handle monthly with no dates as wildcard', () => {
    const freq = {
      type: 'monthly' as const,
      time: new Date(2023, 0, 1, 7, 0, 0, 0), // Jan 1, 2023, 07:00 local
      dates: [],
    };
    const cron = frequencyToCron(freq);
    expect(cron).toBe('0 7 * * *'); // Should fallback to daily cron
    const parsed = cronToFrequency(cron);
    expect(parsed.type).toBe('daily');
  });

  it('should fallback to daily for invalid cron', () => {
    const parsed = cronToFrequency('invalid cron string');
    expect(parsed.type).toBe('daily');
  });

  it('should handle all days of week', () => {
    const freq = {
      type: 'weekly' as const,
      time: new Date(2023, 0, 1, 12, 0, 0, 0), // Jan 1, 2023, 12:00 local
      days: [0, 1, 2, 3, 4, 5, 6],
    };
    const cron = frequencyToCron(freq);
    expect(cron).toBe('0 12 * * 0,1,2,3,4,5,6');
    const parsed = cronToFrequency(cron);
    expect(parsed.type).toBe('weekly');
    expect(parsed.days).toEqual([0, 1, 2, 3, 4, 5, 6]);
  });
});
