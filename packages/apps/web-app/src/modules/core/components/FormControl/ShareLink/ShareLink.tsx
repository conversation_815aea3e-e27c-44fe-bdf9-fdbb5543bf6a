import { Button, ButtonProps, Flex, FlexProps, Input, InputProps } from '@chakra-ui/react';
import { faLink } from '@fortawesome/pro-solid-svg-icons';
import { ReactElement, ReactNode } from 'react';
import { useTranslation } from 'react-i18next';
import { sizes } from '../../../../theme/common';
import { useClipboard } from '../../../hooks/useClipboard';
import { Icon } from '../../Icon/Icon';

export interface ShareLinkProps extends FlexProps {
  link: string;
  text?: ReactNode;
  buttonText?: React.ReactNode;
  buttonProps?: ButtonProps;
  onClickCopy?: (link: string) => void;
  size?: InputProps['size'];
  variant?: InputProps['variant'];
  multiline?: boolean;
  disabled?: boolean;
}

type TK = keyof typeof sizes;
const getSizeHeight = (size: InputProps['size'] = 'def') => {
  if (Array.isArray(size)) {
    return size.map((x) => (x ? sizes[x as TK]?.h : x));
  }
  if (typeof size === 'object') {
    return Object.keys(size).reduce<Record<string, string | number | undefined>>((acc, key) => {
      acc[key] = sizes[key as TK]?.h;
      return acc;
    }, {});
  }
  return sizes[size as TK]?.h;
};

const ShareLink = ({
  link = '',
  text = link,
  buttonText,
  buttonProps,
  onClickCopy,
  bgColor,
  bg,
  size = 'def',
  variant = 'solid',
  multiline,
  disabled,
  children,
  ...rest
}: ShareLinkProps): ReactElement | null => {
  const { t } = useTranslation();
  const { onCopy } = useClipboard(link);

  return (
    <Flex
      direction={'row'}
      align={'center'}
      justifyContent={'space-between'}
      border={'2px dashed'}
      borderColor={'gray.300'}
      bgColor={bgColor || bg}
      rounded={'md'}
      h={'auto'}
      {...rest}
    >
      <Input
        as={Flex}
        variant={variant}
        bg={'transparent'}
        overflow={'hidden'}
        whiteSpace={multiline ? 'pre' : 'nowrap'}
        size={size}
        w={'full'}
        h={'auto'}
        minH={getSizeHeight(size)}
        wordBreak={'break-word'}
        flexGrow={1}
        align={'center'}
        border={'none'}
        title={link}
        onClick={(ev) => {
          window.getSelection()?.selectAllChildren(ev.currentTarget);
        }}
      >
        <div>{text}</div>
      </Input>
      {!disabled && (
        <Button
          variant={'solid'}
          colorScheme={'gray.200'}
          flexShrink={0}
          boxShadow={'none'}
          fontSize={'.8em'}
          maxH={'3em'}
          px={2}
          mr={'.5em'}
          gap={1}
          zIndex={3}
          onClick={() => {
            onCopy();
            if (onClickCopy) return onClickCopy(link);
          }}
          {...buttonProps}
        >
          <Icon icon={faLink} fontSize={'1.33em'} />
          {buttonText !== undefined ? buttonText : t('global.copyLink')}
        </Button>
      )}
      {children}
    </Flex>
  );
};
export default ShareLink;
