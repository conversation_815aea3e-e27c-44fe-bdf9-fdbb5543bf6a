import {
  Button,
  Flex,
  Input,
  InputProps,
  Popover,
  PopoverBody,
  PopoverContent,
  PopoverTrigger,
  Select,
  useDisclosure,
} from '@chakra-ui/react';
import { ChangeEvent, memo, ReactElement, useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import { FormControl } from '../FormControl';
import { frequencyOptions, FrequencyType, FrequencyValue, weekDays } from './constants';
import { frequencyToText } from './helpers';

const dayButtonSx = (isSelected: boolean) => ({
  borderRadius: 'lg',
  minW: 0,
  px: 0,
  bgColor: isSelected ? 'red.500' : 'transparent',
  color: isSelected ? 'gray.900' : 'inherit',
  _hover: {
    bgColor: isSelected ? 'red.600' : 'whiteAlpha.300',
  },
});

export interface FrequencyPickerProps extends Omit<InputProps, 'onChange' | 'value'> {
  value?: FrequencyValue;
  onChange?: (value: FrequencyValue) => void;
}
export const FrequencyPicker = memo(
  ({ value, onChange, ...rest }: FrequencyPickerProps): ReactElement | null => {
    const { t } = useTranslation();
    const { onOpen, onClose, isOpen } = useDisclosure();

    const timeOptions = useMemo(() => {
      return Array.from({ length: 48 }, (_, i) => {
        const totalMinutes = i * 30;
        const h = Math.floor(totalMinutes / 60);
        const m = totalMinutes % 60;
        const date = new Date();
        date.setHours(h, m, 0, 0);
        const value = `${String(h).padStart(2, '0')}:${String(m).padStart(2, '0')}`;
        const name = date.toLocaleTimeString('en-US', {
          hour: 'numeric',
          minute: '2-digit',
          hour12: true,
        });
        return { value, name };
      });
    }, []);

    const selectedTimeValue = useMemo(() => {
      if (!value?.time) {
        const now = new Date();
        now.setMinutes(Math.round(now.getMinutes() / 30) * 30);
        now.setSeconds(0, 0);
        const hours = String(now.getHours()).padStart(2, '0');
        const minutes = String(now.getMinutes()).padStart(2, '0');
        return `${hours}:${minutes}`;
      }
      const date = new Date(value.time);
      const hours = String(date.getHours()).padStart(2, '0');
      const minutes = String(date.getMinutes()).padStart(2, '0');
      return `${hours}:${minutes}`;
    }, [value?.time]);

    const formattedValue = useMemo(() => {
      if (!value?.type || !value.time) return '';
      return frequencyToText(value, t);
    }, [value, t]);

    const handleFrequencyChange = (ev: ChangeEvent<HTMLSelectElement>) => {
      const type = ev.target.value;
      if (!type) return;
      onChange?.({
        type: type as FrequencyType,
        time: value?.time || new Date(),
        days: type === 'weekly' ? value?.days || [] : undefined,
        dates: type === 'monthly' ? value?.dates || [] : undefined,
      });
    };

    const handleTimeChange = (ev: ChangeEvent<HTMLSelectElement>) => {
      const timeValue = ev.target.value;
      if (!timeValue) return;

      const [hours, minutes] = timeValue.split(':').map(Number);
      const newTime = value?.time ? new Date(value.time) : new Date();
      newTime.setHours(hours, minutes, 0, 0);

      onChange?.({
        type: value?.type || 'daily',
        time: newTime,
        days: value?.days,
        dates: value?.dates,
      });
    };

    const handleDayToggle = (day: string) => {
      const dayNum = parseInt(day);
      const days = value?.days || [];
      const newDays = days.includes(dayNum)
        ? days.filter((d) => d !== dayNum)
        : [...days, dayNum].sort();

      onChange?.({
        type: value?.type || 'weekly',
        time: value?.time || new Date(),
        days: newDays,
      });
    };

    const handleDateToggle = (date: number) => {
      const dates = value?.dates || [];
      const newDates = dates.includes(date)
        ? dates.filter((d) => d !== date)
        : [...dates, date].sort();

      onChange?.({
        type: value?.type || 'monthly',
        time: value?.time || new Date(),
        dates: newDates,
      });
    };

    return (
      <>
        <Popover
          placement="bottom-start"
          onOpen={onOpen}
          onClose={onClose}
          isOpen={isOpen}
          strategy={'fixed'}
        >
          <PopoverTrigger>
            <Input isReadOnly value={formattedValue} cursor="pointer" {...rest} />
          </PopoverTrigger>
          <PopoverContent maxH={420} overflowY={'auto'} rounded={'lg'}>
            <PopoverBody p={0} bg={'inherit'}>
              <Flex direction="column" gap={5} p={6}>
                <FormControl label={t('global.repeats')} sx={{ color: 'white' }} fontSize={'sm'}>
                  <Select
                    onChange={handleFrequencyChange}
                    defaultValue={value?.type}
                    size={rest.size || 'sm'}
                    fontSize={'md'}
                    placeholder={t('global.select')}
                  >
                    {frequencyOptions.map((option) => (
                      <option key={option.value} value={option.value}>
                        {t(`dateTime.${option.value}`)}
                      </option>
                    ))}
                  </Select>
                </FormControl>
                <FormControl
                  label={t('global.selectTime')}
                  mb={value?.type === 'weekly' || value?.type === 'monthly' ? 2 : 0}
                  sx={{ color: 'white' }}
                  fontSize={'sm'}
                >
                  <Select
                    value={selectedTimeValue}
                    onChange={handleTimeChange}
                    size={rest.size || 'sm'}
                    fontSize={'md'}
                    placeholder={t('global.select')}
                  >
                    {timeOptions.map((option) => (
                      <option key={option.value} value={option.value}>
                        {option.name}
                      </option>
                    ))}
                  </Select>
                </FormControl>
                {(value?.type === 'weekly' || value?.type === 'monthly') && (
                  <FormControl label={t('global.repeatOn')} sx={{ color: 'white' }} fontSize={'sm'}>
                    {value?.type === 'weekly' && (
                      <Flex wrap={'wrap'} gap={'px'} mt={4} justifyContent={'space-around'}>
                        {weekDays.map((day) => {
                          const isSelected = !!value?.days?.includes(parseInt(day.value));
                          return (
                            <Button
                              key={day.value}
                              sx={dayButtonSx(isSelected)}
                              variant={'unstyled'}
                              size={rest.size || 'sm'}
                              w={'35px'}
                              h={'35px'}
                              onClick={() => handleDayToggle(day.value)}
                              title={t(`dateTime.days.${day.key}`)}
                            >
                              {t(`dateTime.days.${day.key}`)[0]}
                            </Button>
                          );
                        })}
                      </Flex>
                    )}
                    {value?.type === 'monthly' && (
                      <Flex wrap={'wrap'} gap={'px'} mt={4}>
                        {Array.from({ length: 31 }, (_, i) => i + 1).map((date) => {
                          const isSelected = !!value?.dates?.includes(date);
                          return (
                            <Button
                              key={date}
                              sx={dayButtonSx(isSelected)}
                              variant={'unstyled'}
                              size={rest.size || 'sm'}
                              w={'36px'}
                              h={'36px'}
                              onClick={() => handleDateToggle(date)}
                              title={date.toString()}
                            >
                              {date}
                            </Button>
                          );
                        })}
                      </Flex>
                    )}
                  </FormControl>
                )}
              </Flex>
              <Flex px={6} py={2} position={'sticky'} bottom={0} bg={'inherit'}>
                <Button
                  size="xs"
                  variant="outline"
                  colorScheme="red.500"
                  color={'white'}
                  px={6}
                  onClick={onClose}
                >
                  {t('global.done')}
                </Button>
              </Flex>
            </PopoverBody>
          </PopoverContent>
        </Popover>
      </>
    );
  },
);

export default FrequencyPicker;
