import { Box } from '@chakra-ui/react';
import { Meta, StoryObj } from '@storybook/react';
import { StorybookProvidersDecorator } from '../../../../../stories/utils/Providers';
import { FrequencyPicker } from './index';

const meta: Meta<typeof FrequencyPicker> = {
  title: 'web-app/components/FormControl/FrequencyPicker',
  component: FrequencyPicker,
  tags: ['autodocs'],
  decorators: [StorybookProvidersDecorator],
  render: (args) => (
    <Box bg="gray.900" p={4} minH={350}>
      <Box maxW={400}>
        <FrequencyPicker {...args} />
      </Box>
    </Box>
  ),
};

export default meta;
type Story = StoryObj<typeof FrequencyPicker>;

export const Daily: Story = {
  args: {
    value: {
      type: 'daily',
      time: new Date(),
    },
  },
};

export const Weekly: Story = {
  args: {
    value: {
      type: 'weekly',
      time: new Date(),
      days: [1, 3, 5], // Monday, Wednesday, Friday
    },
  },
};

export const Monthly: Story = {
  args: {
    value: {
      type: 'monthly',
      time: new Date(),
      dates: [1, 15, 30], // 1st, 15th, and 30th of each month
    },
  },
};
