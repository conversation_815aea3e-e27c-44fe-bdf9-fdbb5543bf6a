import { TFunction } from 'i18next';
import { frequencyNs, frequencyOptions, FrequencyValue, weekDays } from './constants';

export const frequencyToCron = (frequency: FrequencyValue, utc = true): string => {
  const date = frequency.time instanceof Date ? frequency.time : new Date(frequency.time);
  const minute = utc ? date.getUTCMinutes() : date.getMinutes();
  const hour = utc ? date.getUTCHours() : date.getHours();
  switch (frequency.type) {
    case 'daily':
      return `${minute} ${hour} * * *`;
    case 'weekly':
      return `${minute} ${hour} * * ${frequency.days?.length ? frequency.days.join(',') : '*'}`;
    case 'monthly':
      return `${minute} ${hour} ${frequency.dates?.length ? frequency.dates.join(',') : '*'} * *`;
    default:
      return `${minute} ${hour} * * *`;
  }
};

export const cronToFrequency = (cron: string, utc = true): FrequencyValue => {
  // Example: '0 0 * * *', '15 10 * * 1,3,5', '30 8 1,15 * *'
  const parts = cron.trim().split(/\s+/);
  if (parts.length !== 5) {
    return { type: 'daily', time: new Date() };
  }
  // The 'month', which is present in the standard cron format (minute hour dayOfMonth month dayOfWeek),
  // but is ignored for daily/weekly/monthly frequencies in this implementation.
  const [minuteStr, hourStr, dayOfMonth /* month */, , dayOfWeek] = parts;
  const minute = parseInt(minuteStr, 10);
  const hour = parseInt(hourStr, 10);
  // Create a date at today, but set the time in UTC
  const now = new Date();
  if (utc) {
    now.setUTCHours(hour, minute, 0, 0);
  } else {
    now.setHours(hour, minute, 0, 0);
  }

  if (dayOfMonth !== '*' && dayOfWeek === '*') {
    // Monthly
    const dates = dayOfMonth
      .split(',')
      .map(Number)
      .filter((n) => !isNaN(n));
    return { type: 'monthly', time: new Date(now), dates };
  } else if (dayOfWeek !== '*' && dayOfMonth === '*') {
    // Weekly
    const days = dayOfWeek
      .split(',')
      .map(Number)
      .filter((n) => !isNaN(n));
    return { type: 'weekly', time: new Date(now), days };
  } else if (dayOfMonth === '*' && dayOfWeek === '*') {
    // Daily
    return { type: 'daily', time: new Date(now) };
  }
  // Fallback: treat as daily
  return { type: 'daily', time: new Date(now) };
};

export const frequencyToText = (value: FrequencyValue, t: TFunction): string => {
  const { type, time, days, dates } = value;

  const timeString = new Date(time).toLocaleTimeString('en-US', {
    hour: 'numeric',
    minute: '2-digit',
    hour12: true,
  });

  const frequencyName = t(`dateTime.${frequencyOptions.find((f) => f.value === type)?.value}`);

  switch (type) {
    case 'daily':
      return t(`${frequencyNs}.daily_at_time`, { frequencyName, time: timeString });
    case 'weekly': {
      if (!days || days.length === 0) {
        return t(`${frequencyNs}.weekly_at_time`, { time: timeString });
      }
      const dayNames = days
        .map((d) =>
          t(`dateTime.days.${weekDays.find((wd) => parseInt(wd.value, 10) === d)?.key}_short`),
        )
        .filter(Boolean)
        .join(', ');
      return t(`${frequencyNs}.weekly_on_days_at_time`, {
        days: dayNames,
        time: timeString,
      });
    }
    case 'monthly': {
      if (!dates || dates.length === 0) {
        return t(`${frequencyNs}.monthly_at_time`, { time: timeString });
      }
      const sortedDates = [...dates].sort((a, b) => a - b).join(', ');
      return t(`${frequencyNs}.monthly_on_dates_at_time`, {
        dates: sortedDates,
        time: timeString,
      });
    }
    default:
      return '';
  }
};
