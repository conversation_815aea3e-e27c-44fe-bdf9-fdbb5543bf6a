import {
  Box,
  Input,
  InputGroup,
  InputProps,
  InputRightElement,
  Textarea,
  TextareaProps,
} from '@chakra-ui/react';
import { forwardRef } from 'react';
import { CounterControlSharedProps } from './types';
import { useControlCounter } from './useCounter';

export const CounterInput = forwardRef<HTMLInputElement, CounterControlSharedProps & InputProps>(
  ({ maxLength, initialValue, value, onChange, size, children, ...rest }, ref) => {
    const [left, change] = useControlCounter({
      maxLength,
      initialValue: value || initialValue,
      onChange,
    });
    return (
      <InputGroup size={size}>
        {children}
        <Input
          {...rest}
          onChange={change}
          maxLength={maxLength}
          size={size}
          value={value}
          ref={ref}
        />
        <InputRightElement>
          <Box
            py="3px"
            px="4px"
            fontWeight="bold"
            fontSize="2xs"
            borderRadius="md"
            color={left < 0 ? 'red.800' : 'gray.500'}
            bgColor="blackAlpha.100"
            _dark={{
              bgColor: 'whiteAlpha.200',
              color: left < 0 ? 'red.800' : 'gray.300',
            }}
            position={'relative'}
            top={'50%'}
            mt={-4}
            mr={4}
          >
            {left}
          </Box>
        </InputRightElement>
      </InputGroup>
    );
  },
);

export const CounterTextarea = forwardRef<
  HTMLTextAreaElement,
  CounterControlSharedProps & TextareaProps
>(({ maxLength, initialValue, value, onChange, ...rest }, ref) => {
  const [left, change] = useControlCounter<HTMLTextAreaElement>({
    maxLength,
    initialValue: value || initialValue,
    onChange,
  });
  return (
    <Box position="relative">
      <Textarea {...rest} pb={10} onChange={change} maxLength={maxLength} value={value} ref={ref} />
      <Box
        position="absolute"
        bottom={3}
        right={4}
        py="3px"
        px="4px"
        fontWeight="bold"
        fontSize="2xs"
        borderRadius="md"
        color={left < 0 ? 'red.800' : 'gray.500'}
        bgColor="blackAlpha.100"
        _dark={{
          bgColor: 'whiteAlpha.200',
          color: left < 0 ? 'red.800' : 'gray.300',
        }}
      >
        {left}
      </Box>
    </Box>
  );
});
