import { faChevronDown } from '@fortawesome/pro-solid-svg-icons';
import { z } from 'zod';
import { Icon } from '../../Icon/Icon';

export type FrequencyType = 'daily' | 'weekly' | 'monthly';

export const frequencyNs = 'global.frequency';

export const frequencyOptions = [
  { value: 'daily', icon: <Icon icon={faChevronDown} /> },
  { value: 'weekly', icon: <Icon icon={faChevronDown} /> },
  { value: 'monthly', icon: <Icon icon={faChevronDown} /> },
];
export const weekDays = [
  { key: 'monday', value: '1' },
  { key: 'tuesday', value: '2' },
  { key: 'wednesday', value: '3' },
  { key: 'thursday', value: '4' },
  { key: 'friday', value: '5' },
  { key: 'saturday', value: '6' },
  { key: 'sunday', value: '0' },
];

export const frequencySchema = z.object({
  type: z.enum<FrequencyType, [FrequencyType, ...FrequencyType[]]>([
    'daily',
    'weekly',
    'monthly',
  ] as const),
  time: z.date(),
  days: z.array(z.number()).optional(), // For weekly - 0 = Sunday, 6 = Saturday
  dates: z.array(z.number()).optional(), // For monthly - 1-31
});

export type FrequencyValue = z.infer<typeof frequencySchema>;
