import { Box, BoxProps, SkeletonText, Text } from '@chakra-ui/react';
import { ReactElement, ReactNode } from 'react';

export type SimpleLoaderProps = BoxProps & {
  lines?: number;
  children?: ReactNode;
};

const SimpleLoader = ({ children, lines = 2, ...rest }: SimpleLoaderProps): ReactElement | null => {
  return (
    <Box textAlign={'left'} w={'full'} {...rest}>
      {!!children && (
        <Text fontWeight={'bold'} color={'gray.500'} mb={4}>
          {children}
        </Text>
      )}
      <SkeletonText
        noOfLines={lines}
        spacing={3}
        skeletonHeight={4}
        layerStyle={'skeleton-subtle'}
      />
    </Box>
  );
};
export default SimpleLoader;
