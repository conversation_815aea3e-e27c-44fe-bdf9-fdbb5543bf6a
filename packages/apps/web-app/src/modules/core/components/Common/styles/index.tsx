import { SpinnerProps, SystemStyleObject } from '@chakra-ui/react';
import theme from '../../../../theme';

export const stickyHeaderSx: SystemStyleObject = {
  zIndex: theme.zIndices.sticky as number,
  top: 0,
  width: '100%',
};

export const stickyBottomCss: SystemStyleObject = {
  position: 'sticky',
  py: 3,
  bottom: 0,
  zIndex: 'sticky',
  bgColor: 'white',
  _dark: {
    bgColor: 'gray.900',
  },
};

export const blockquoteSx: SystemStyleObject = {
  position: 'relative',
  pl: 10,
  ':before': {
    position: 'absolute',
    fontFamily: 'Georgia, "Times New Roman", serif',
    lineHeight: 1,
    left: 0,
    top: 1,
    fontSize: '6xl',
    color: theme.colors.gray[300],
    content: '"\\201C"',
  },
};

export const slashSx = (
  width = 4,
  color: SystemStyleObject['color'] = 'gray.900',
  bg = 'white',
): SystemStyleObject => ({
  position: 'relative',
  _before: {
    content: '" "',
    position: 'absolute',
    left: '-12.5%',
    top: '46%',
    w: '125%',
    h: `${width}px`,
    bg: color,
    rounded: 'full',
    boxShadow: `0 ${width}px 0 0 ${bg}`,
    transform: 'rotate(-45deg)',
  },
});

export const stripesSx = (color = 'rgba(10,15,20,.05)', size = 12): SystemStyleObject => ({
  bgImage: `linear-gradient(135deg, rgba(0, 0, 0, 0) 25%, ${color} 25%, ${color} 50%, rgba(0, 0, 0, 0) 50%, rgba(0, 0, 0, 0) 75%, ${color} 75%, ${color} 100%)`,
  bgSize: `${size * 2}px ${size * 2}px`,
});

export const markdownSx: SystemStyleObject = {
  whiteSpace: 'normal',
  'h1,h2,h3': {
    fontSize: '1.75em',
    fontWeight: 'bold',
    mb: 4,
  },
  'h4,h5,h6': {
    fontSize: '1.4em',
    fontWeight: 'bold',
    mb: 4,
  },
  p: {
    mb: 6,
  },
  '>*:last-child': {
    mb: 0,
  },
  ul: {
    ml: 8,
  },
  ol: {
    ml: 8,
  },
  li: {
    mb: 4,
  },
  'li:last-child': {
    mb: 0,
  },
  'ul p': {
    mb: 0,
  },
  '.contains-task-list': {
    ml: 0,
  },
  blockquote: blockquoteSx,
  a: {
    wordBreak: 'break-all',
  },
};

export const spinnerProps: SpinnerProps = {
  thickness: '.2em',
  emptyColor: 'gray.100',
  color: 'gray.500',
  speed: '0.6s',
};
