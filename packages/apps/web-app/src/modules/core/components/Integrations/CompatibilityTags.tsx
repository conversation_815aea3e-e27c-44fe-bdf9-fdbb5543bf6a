import { Image, Tag, TagLabel, Wrap, WrapItem, WrapProps } from '@chakra-ui/react';
import { Icon } from '@core/components/Icon/Icon';
import { IconDefinition, faBackward, faBolt, faLightbulb } from '@fortawesome/pro-solid-svg-icons';
import { AiSessionFeedType, IntegrationWithActions } from '@waitroom/models';
import { useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import { providers } from './Notetaker.constants';

export const itemsInfoMap: Partial<
  Record<AiSessionFeedType, { text: string; icon: IconDefinition; color: string }>
> = {
  'catch-up': {
    text: 'integrations.catch-up',
    icon: faBackward,
    color: 'gray.700',
  },
  'action-item': {
    text: 'integrations.actionItem',
    color: 'red.700',
    icon: faBolt,
  },
  insight: {
    text: 'integrations.insight',
    color: 'orange.700',
    icon: faLightbulb,
  },
};

type CompatibilityTagsProps = {
  integrations: IntegrationWithActions[];
} & WrapProps;

type TextTagProps = { tag: AiSessionFeedType };
const TextTag = ({ tag }: TextTagProps) => {
  const { t } = useTranslation();
  const info = itemsInfoMap[tag];
  if (!info) return null;

  return (
    <WrapItem>
      <Tag rounded="base" bgColor="gray.50" _dark={{ bgColor: 'gray.800', color: 'white' }}>
        <Icon icon={info.icon} color={info.color} />
        <TagLabel fontSize="xs" ml={1} fontWeight={800}>
          {t(info.text)}
        </TagLabel>
      </Tag>
    </WrapItem>
  );
};

type ImageTagProps = { tag: keyof typeof providers };
const ImageTag = ({ tag }: ImageTagProps) => {
  const info = providers[tag];
  if (!info) return null;

  return (
    <WrapItem>
      <Image src={info} w="auto" h={8} />
    </WrapItem>
  );
};

export const CompatibilityTags = ({ integrations, ...rest }: CompatibilityTagsProps) => {
  const tags = useMemo(() => {
    const actions = [];
    for (let i = 0; i < integrations.length; i++) {
      const item = integrations[i];
      actions.push(...item.actions.map(({ aiFeedItemTypes }) => aiFeedItemTypes));
    }
    return [...new Set(actions.flat())];
  }, [integrations]);

  return (
    <Wrap {...rest}>
      {tags.map((tag) =>
        integrations[0].name === 'recall' ? (
          <ImageTag key={tag} tag={tag as keyof typeof providers} />
        ) : (
          <TextTag key={tag} tag={tag as AiSessionFeedType} />
        ),
      )}
    </Wrap>
  );
};
