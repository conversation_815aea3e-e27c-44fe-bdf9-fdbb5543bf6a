import {
  AlertDialog,
  AlertDialogBody,
  AlertDialogContent,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogOverlay,
  AlertDialogProps,
  Button,
  ButtonProps,
  LinkProps,
  ModalBodyProps,
  ModalContentProps,
  ModalFooterProps,
  ModalHeaderProps,
} from '@chakra-ui/react';
import { ReactElement, ReactNode, useRef } from 'react';
import { useTranslation } from 'react-i18next';

export interface ConfirmProps extends Omit<AlertDialogProps, 'leastDestructiveRef' | 'children'> {
  header?: ReactNode;
  body?: ReactNode;
  onCancel?: () => void;
  onConfirm?: () => void;
  confirmProps?:
    | ButtonProps
    | (Pick<LinkProps, 'href' | 'target' | 'rel'> & { 'data-testid': string });
  confirmText?: string;
  cancelText?: string;
  isLoading?: boolean;
  closeOnConfirm?: boolean;
  headerProps?: ModalHeaderProps & { 'data-testid': string };
  bodyProps?: ModalBodyProps & { 'data-testid': string };
  footerProps?: ModalFooterProps & { 'data-testid': string };
  contentProps?: ModalContentProps & { 'data-testid': string };
}

const Confirm = ({
  header,
  body,
  onClose,
  onCancel,
  onConfirm,
  confirmProps,
  isOpen = false,
  isLoading,
  confirmText,
  cancelText,
  closeOnConfirm,
  headerProps,
  bodyProps,
  footerProps,
  size = 'sm',
  isCentered = true,
  closeOnOverlayClick = true,
  contentProps,
  ...rest
}: ConfirmProps): ReactElement | null => {
  const { t } = useTranslation();
  const cancelRef = useRef(null);

  return (
    <AlertDialog
      isOpen={isOpen}
      leastDestructiveRef={cancelRef}
      onClose={onClose}
      size={size}
      isCentered={isCentered}
      closeOnOverlayClick={closeOnOverlayClick}
      {...rest}
    >
      <AlertDialogOverlay>
        <AlertDialogContent rounded="lg" {...contentProps}>
          {header ? (
            <AlertDialogHeader
              fontSize="lg"
              fontWeight="extrabold"
              mb={2}
              textAlign="center"
              {...(headerProps ? headerProps : {})}
            >
              {header}
            </AlertDialogHeader>
          ) : null}
          {body ? (
            <AlertDialogBody
              {...(bodyProps
                ? bodyProps
                : {
                    textAlign: 'center',
                    px: 6,
                    fontSize: 'sm',
                    fontWeight: 'bold',
                  })}
            >
              {body}
            </AlertDialogBody>
          ) : null}
          <AlertDialogFooter {...(footerProps ? footerProps : { justifyContent: 'center' })}>
            <Button
              ref={cancelRef}
              onClick={() => {
                if (onCancel) onCancel();
                onClose();
              }}
              size="xs"
              variant="outline"
              colorScheme="gray.900"
              fontWeight={800}
              px={7}
            >
              {cancelText ? cancelText : t('global.cancel')}
            </Button>
            <Button
              onClick={() => {
                if (onConfirm) onConfirm();
                if (closeOnConfirm) onClose();
              }}
              size="xs"
              ml={3}
              isLoading={isLoading}
              variant="solid"
              colorScheme="green"
              fontWeight={800}
              px={7}
              {...confirmProps}
            >
              {confirmText ? confirmText : t('global.confirm')}
            </Button>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialogOverlay>
    </AlertDialog>
  );
};

export default Confirm;
