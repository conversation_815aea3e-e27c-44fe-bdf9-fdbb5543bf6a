import ReactEmojiPicker, { EmojiStyle, Theme } from 'emoji-picker-react';
import { ReactElement } from 'react';
import './styles.css';

export type EmojiPickerProps = {
  onSelect: (emoji: string) => void;
};

const EmojiPicker = ({ onSelect }: EmojiPickerProps): ReactElement | null => {
  return (
    <ReactEmojiPicker
      open
      width={'100%'}
      height={380}
      onEmojiClick={(data) => onSelect(data.emoji)}
      lazyLoadEmojis
      skinTonesDisabled
      emojiStyle={EmojiStyle.NATIVE}
      theme={Theme.DARK}
      previewConfig={{
        showPreview: false,
      }}
    />
  );
};
export default EmojiPicker;
