import {
  PlacementWithLogical,
  Popover,
  PopoverBody,
  PopoverContent,
  PopoverTrigger,
  Portal,
  useDisclosure,
} from '@chakra-ui/react';
import { ReactElement, ReactNode, Suspense } from 'react';
import ParagraphLoader from '../../../Loader/Paragraph/ParagraphLoader';
import EmojiPicker from '../EmojiPicker';

export type EmojiPickerPopoverProps = {
  placement?: PlacementWithLogical;
  children: ReactNode;
  onSelect: (emoji: string) => void;
  closeOnSelect?: boolean;
  onClose?: () => void;
};

const EmojiPickerPopover = ({
  placement,
  onSelect,
  children,
  onClose: onC,
  closeOnSelect,
}: EmojiPickerPopoverProps): ReactElement | null => {
  const disclosure = useDisclosure({ onClose: onC });

  const handleSelect = (emoji: string) => {
    onSelect(emoji);
    if (closeOnSelect) disclosure.onClose();
  };

  return (
    <Popover placement={placement} {...disclosure} isLazy>
      <PopoverTrigger>{children}</PopoverTrigger>
      <Portal>
        <PopoverContent
          bg="gray.900"
          rounded="lg"
          border="1px solid"
          borderColor="gray.600"
          maxW={[280, 320]}
          overflow="hidden"
        >
          <PopoverBody p={0}>
            <Suspense fallback={<ParagraphLoader header lines={5} />}>
              <EmojiPicker onSelect={handleSelect} />
            </Suspense>
          </PopoverBody>
        </PopoverContent>
      </Portal>
    </Popover>
  );
};
export default EmojiPickerPopover;
