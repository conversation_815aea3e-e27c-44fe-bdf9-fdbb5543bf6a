import { Box, BoxProps, Button, ButtonProps, Flex, useDisclosure } from '@chakra-ui/react';
import { Fragment, PropsWithChildren, ReactElement, ReactNode } from 'react';
import { Link, LinkProps } from 'react-router-dom';

const placements: Record<string, BoxProps> = {
  bottom: { top: '100%', left: '50%', transform: 'translateX(-50%)' },
  bottomLeft: { top: '100%', left: '0' },
  bottomRight: { top: '100%', right: '0' },
  top: { bottom: '100%', left: '50%', transform: 'translateX(-50%)' },
  topLeft: { bottom: '100%', left: '0' },
  topRight: { bottom: '100%', right: '0' },
  right: { bottom: 0, left: '100%' },
  left: { bottom: 0, right: '100%' },
};

export type DropdownMenuItemType = LinkProps | ButtonProps | (() => ReactElement | null);
export type DropdownMenuItemProps = {
  item: DropdownMenuItemType;
};
export interface DropdownMenuProps extends BoxProps {
  button: ReactNode;
  items: DropdownMenuItemType[];
  placement?: keyof typeof placements;
  hover?: boolean;
  inline?: boolean;
}

const props: BoxProps['sx'] = {
  '.dropdown': {
    position: 'absolute',
    pt: 1,
    '>div': {
      boxShadow: 'lg',
      rounded: 'lg',
      overflow: 'hidden',
      border: '1px solid',
      borderColor: 'blackAlpha.200',
      bg: 'white',
      color: 'gray.900',
      minW: '150px',
      maxW: '280px',
      inlineSize: 'max-content',
      py: 2,
      '>*': {
        borderBottom: '1px solid',
        borderColor: 'blackAlpha.100',
      },
      '>*:last-child': {
        borderBottom: 'none',
      },
    },
  },
};
const inlineProps: BoxProps['sx'] = {
  '.dropdown': {
    position: 'relative',
    w: 'full',
    top: -2,
    '>div': {
      w: 'full',
      fontWeight: 'normal',
      pb: 2,
    },
  },
};

export const DropdownMenuItem = ({ item }: DropdownMenuItemProps) => {
  return (
    <Button
      variant="unstyled"
      size="xs"
      textAlign="left"
      display="flex"
      alignItems="center"
      fontWeight="normal"
      justifyContent="flex-start"
      fontSize="sm"
      as={'to' in item ? Link : undefined}
      className="none"
      py={2}
      px={{ base: 4, xl: 6 }}
      w="full"
      rounded={0}
      _hover={{ background: 'gray.50' }}
      {...(item as PropsWithChildren)}
    />
  );
};

const DropdownMenu = ({
  placement = 'bottom',
  inline,
  hover,
  button,
  items,
  ...rest
}: DropdownMenuProps): ReactElement | null => {
  const { isOpen, onToggle } = useDisclosure();
  const hoverTrigger = hover && !inline;
  return (
    <Box
      position="relative"
      cursor="pointer"
      sx={{
        '&:hover .dropdown': hoverTrigger ? { display: 'initial' } : {},
        ...(inline ? inlineProps : props),
      }}
      {...rest}
    >
      <Flex
        align={'center'}
        w={'full'}
        role="button"
        className="menu-item"
        onClick={hoverTrigger ? undefined : onToggle}
      >
        {button}
      </Flex>
      <Box
        className="dropdown"
        display={isOpen ? 'initial' : 'none'}
        zIndex="dropdown"
        color="inherit"
        {...placements[inline ? '' : placement]}
      >
        <div>
          {items.map((Item, i = 0) => (
            <Fragment key={i}>
              {typeof Item === 'function' ? <Item /> : <DropdownMenuItem item={Item} />}
            </Fragment>
          ))}
        </div>
      </Box>
    </Box>
  );
};

export default DropdownMenu;
