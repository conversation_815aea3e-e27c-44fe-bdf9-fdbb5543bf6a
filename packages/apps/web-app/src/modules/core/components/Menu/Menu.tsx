import {
  Flex,
  IconButton,
  Link,
  LinkProps,
  StackProps,
  useBreakpointValue,
  useDisclosure,
} from '@chakra-ui/react';
import { faBars } from '@fortawesome/pro-solid-svg-icons';
import { MouseEvent, ReactElement, memo, useCallback, useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import { Link as RLink } from 'react-router-dom';
import { BreakpointKeys, breakpointNumbers } from '../../../theme/breakpoints';
import { Icon } from '../Icon/Icon';
import Modal from '../Modal/Modal';
import { closeButtonProps, contentProps, modalCss } from './Menu.styles';

type Element = ReactElement | null;
export type RenderProps = {
  active: boolean;
  text: string;
  onClose: () => void;
  onClick: (ev: MouseEvent) => void;
  isModal: boolean;
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
} & Dict<any>;
export type Render = null | ((props: RenderProps) => ReactElement | null);
export interface MenuItem extends LinkProps {
  to?: string;
  text?: string;
  trans?: string;
  isActive?: (path: string) => boolean;
  render?: Render;
  renderModal?: Render;
  fixed?: boolean;
  breakpoint?: BreakpointKeys;
  order?: number;
  'data-testid'?: string;
}
export interface MenuProps {
  currentLink?: string;
  breakpoint?: BreakpointKeys;
  items: MenuItem[];
  menuProps?: StackProps;
  closeOnClick?: boolean;
}

const Menu = memo(
  ({
    currentLink = '',
    breakpoint = 'md',
    items,
    menuProps,
    closeOnClick = true,
  }: MenuProps): ReactElement | null => {
    const { t } = useTranslation();
    const bpSize = useBreakpointValue(breakpointNumbers) || 0;
    const showModal = bpSize <= breakpointNumbers[breakpoint];
    const { isOpen, onOpen, onClose } = useDisclosure();

    const renderItem = useCallback(
      (item: MenuItem, i: number, isModalItem: boolean): Element => {
        const { text, trans, isActive, render, renderModal, onClick, fixed: _, ...rest } = item;
        const Component = isModalItem ? renderModal : render;
        if (Component === null) return null;

        const content = text || (trans && t(trans)) || '';
        const active = isActive
          ? isActive(currentLink)
          : !!rest.to && currentLink.startsWith(rest.to);
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        const onClickInternal = (ev: MouseEvent<any>): void => {
          if (closeOnClick) onClose();
          if (onClick) onClick(ev);
        };
        return Component ? (
          <Component
            key={i}
            active={active}
            text={content}
            onClose={onClose}
            onClick={onClickInternal}
            isModal={isModalItem}
            {...rest}
          />
        ) : rest.to || rest.as ? (
          <Link
            as={RLink}
            key={i}
            className="simple menu-item"
            _focus={{
              boxShadow: 'none',
            }}
            onClick={onClickInternal}
            {...rest}
          >
            {content}
          </Link>
        ) : null;
      },
      [t, currentLink, closeOnClick, onClose],
    );

    const [menuUi, modalMenuUi] = useMemo(
      () =>
        items.reduce<[Element[], Element[]]>(
          (ac, item, i) => {
            const isSmaller = item.breakpoint && bpSize <= breakpointNumbers[item.breakpoint];
            if ((item.fixed || !showModal) && !isSmaller) {
              ac[0].push(renderItem(item, i, false));
            }
            if ((item.fixed || !item.breakpoint || isSmaller) && showModal) {
              ac[1].push(renderItem(item, i, true));
            }
            return ac;
          },
          [[], []], // [menu items, modal items]
        ),
      [items, renderItem, bpSize, showModal],
    );

    return (
      <>
        <Flex as="nav" gap={2} align="center" {...menuProps}>
          {menuUi}
          {showModal ? (
            <IconButton
              aria-label="Menu"
              size="xs"
              variant="solid"
              colorScheme="gray.900"
              boxShadow="none"
              isRound
              m={1}
              order={100}
              px={0}
              onClick={onOpen}
            >
              <Icon icon={faBars} fontSize="lg" />
            </IconButton>
          ) : null}
        </Flex>
        {showModal ? (
          <Modal size="full" isOpen={isOpen} onClose={onClose} contentProps={contentProps}>
            <Modal.Header bg="white" closeButtonProps={closeButtonProps} />
            <Modal.Body display="flex" alignItems="center">
              <Flex direction="column" w="100%" css={modalCss}>
                {modalMenuUi}
              </Flex>
            </Modal.Body>
          </Modal>
        ) : null}
      </>
    );
  },
);
export default Menu;
