import { MeetingMemory } from '@waitroom/models';
import { useMemoizedFn } from 'ahooks';
import { useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { useMeetingMemoryUrlParams } from './useMeetingMemoryUrlParams';

interface UseMeetingMemorySubmissionWithUrlProps {
  submitFunction: (
    query: string,
    isNew: boolean,
    sessions?: MeetingMemory.AskAIRequestBodySessions,
  ) => void;
  onBeforeSubmit?: () => void;
}

export const useMeetingMemorySubmissionWithUrl = ({
  submitFunction,
  onBeforeSubmit,
}: UseMeetingMemorySubmissionWithUrlProps) => {
  const navigate = useNavigate();
  const { urlQuery, urlSessions } = useMeetingMemoryUrlParams();

  const submitFnRef = useMemoizedFn(submitFunction);

  useEffect(() => {
    let timeout: ReturnType<typeof setTimeout>;
    if (urlQuery) {
      timeout = setTimeout(() => {
        navigate('.', { replace: true });
        const sessions =
          Array.isArray(urlSessions) &&
          !!urlSessions.length &&
          urlSessions.every(
            (element) =>
              !!element.id &&
              Array.isArray(element.recurrenceIds) &&
              !!element.recurrenceIds.length,
          )
            ? urlSessions
            : undefined;
        onBeforeSubmit?.();
        submitFnRef(urlQuery, true, sessions);
      }, 100);
    }
    return () => {
      clearTimeout(timeout);
    };
  }, [urlQuery, navigate, submitFnRef, urlSessions, onBeforeSubmit]);
};
