import { MeetingMemory } from '@waitroom/models';
import { Location, useLocation, useSearchParams } from 'react-router-dom';
import { MeetingMemoryLocationState } from '../../dashboard/components/MeetingMemory/types';

export const useMeetingMemoryUrlParams = () => {
  const location = useLocation() as Location<{
    q?: string;
    sessions?: MeetingMemory.AskAIRequestBodySessions;
  }>;

  const [searchParams] = useSearchParams();
  const { q: urlQuery, sessions: urlSessions } = (location.state || {
      q: searchParams.get('prompt'),
    } ||
    {}) as MeetingMemoryLocationState;

  return {
    urlQuery,
    urlSessions,
  };
};
