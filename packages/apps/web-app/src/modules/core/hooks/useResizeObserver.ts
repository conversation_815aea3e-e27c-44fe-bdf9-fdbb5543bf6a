import { useEffect, useRef } from 'react';

export const useResizeObserver = <TElement extends Element>(
  callback: (entry: ResizeObserverEntry[]) => void,
) => {
  const ref = useRef<TElement>(null);
  const observer = useRef<ResizeObserver>(undefined);

  const cleanOb = () => {
    if (observer.current) observer.current.disconnect();
  };

  useEffect(() => {
    if (!ref.current) return;
    const ob = (observer.current = new ResizeObserver(callback));
    ob.observe(ref.current);

    return () => {
      cleanOb();
    };
  }, [callback]);

  return { ref };
};
