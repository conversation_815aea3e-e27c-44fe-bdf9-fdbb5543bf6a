import { Alert, AlertIcon, useToast as useToastC } from '@chakra-ui/react';
import { GetResponse } from '@waitroom/common-api';
import { ReactNode, useCallback } from 'react';
import { useTranslation } from 'react-i18next';
import { getResponseMessage } from '../utils/api';

type UseToastProps = {
  successMessageDefault?: string;
  errorMessageDefault?: string;
  namespace?: string;
  durationSuccess?: number;
  durationError?: number;
};

/**
 * Return the first non-empty value
 */
export const firstNonEmpty = (
  ...strings: (ReactNode | null | undefined)[]
): ReactNode | null | undefined => {
  return strings.find((s) => !!s?.toString().length);
};

export const useToast = ({
  successMessageDefault,
  errorMessageDefault,
  namespace,
  durationSuccess = 3000,
  durationError = 6000,
}: UseToastProps = {}) => {
  const toast = useToastC({
    containerStyle: {
      minWidth: '200px',
    },
  });
  const { t } = useTranslation();

  const onSuccess = useCallback(
    (message?: ReactNode) => {
      return toast({
        duration: durationSuccess,
        position: 'top',
        render: () => (
          <Alert status="success" fontWeight={'600'} fontSize={'md'} p={2}>
            <AlertIcon />
            {firstNonEmpty(message, successMessageDefault, 'Success')}
          </Alert>
        ),
      });
    },
    [durationSuccess, successMessageDefault, toast],
  );

  const onError = useCallback(
    (message?: ReactNode) => {
      toast({
        duration: durationError,
        position: 'top',
        render: () => (
          <Alert status="error" fontWeight={'600'} fontSize={'md'} p={2}>
            <AlertIcon />
            {firstNonEmpty(message, errorMessageDefault, 'Error')}
          </Alert>
        ),
      });
    },
    [durationError, errorMessageDefault, toast],
  );

  const onResponse = useCallback(
    (response: GetResponse<unknown>, message?: { success?: string; error?: string }) => {
      const msg = getResponseMessage({ response, t, namespace });

      if (response instanceof Error) {
        return onError(response.message);
      }
      return response?.success
        ? onSuccess(msg[1] || message?.success || successMessageDefault)
        : onError(msg[1] || message?.error || errorMessageDefault);
    },
    [errorMessageDefault, namespace, onError, onSuccess, successMessageDefault, t],
  );

  return {
    onSuccess,
    onError,
    onResponse,
  };
};
