import { orFn } from '@waitroom/utils';
import { useCallback, useEffect, useRef, useState } from 'react';
import { useAppStore } from '../store/store';

export const SpeechSynthesis = window.speechSynthesis;
export const isTextToSpeechSupported =
  typeof SpeechSynthesis !== 'undefined' && !!SpeechSynthesisUtterance;

export type UseTextToSpeechProps = {
  text: string | (() => string);
  lang?: string;
  voice?: string;
  rate?: number;
};

export const preferredVoices: [string, number][] = [
  ['andrew', 4],
  ['samantha', 3],
  ['alex', 3],
  ['daniel', 3],
  ['thomas', 1],
  ['david', 1],
  ['microsoft', 2],
  ['google', 2],
];

export const speechVoices = {
  default: [undefined, null] as [string | undefined, SpeechSynthesisVoice | null],
  score: function (
    voice: SpeechSynthesisVoice,
    options: { lang?: string; preferredVoices?: [string, number][] } = {},
  ) {
    let score = 0;
    // Prefer native voices over remote ones
    if (!voice.localService) score -= 2;
    // Prefer voices matching the user's language
    if (voice.lang === options.lang) score += 10;
    // Prefer certain voice types based on common high-quality voices
    const voiceName = voice.name.toLowerCase();
    // Premium voices on different platforms
    if (voiceName.includes('premium')) score += 3;
    if (voiceName.includes('enhanced')) score += 3;
    // Voice names scoring
    const preferred = options.preferredVoices || preferredVoices;
    for (const [key, value] of preferred) {
      if (voiceName.includes(key)) score += value;
    }
    return score;
  },
  get: function (lang = 'en-US', voice: string | undefined = undefined) {
    const vc = voice?.toLowerCase();
    // check if the default voice was already selected
    if (this.default[1] && this.default[0] === vc) return this.default;

    const voices = SpeechSynthesis.getVoices();
    // find provided voice
    if (vc?.length) {
      const found = voices.find(
        (v) => v.name.toLowerCase() === vc || v.name.toLowerCase().includes(vc),
      );
      if (found) {
        this.default = [vc, found];
        return this.default;
      }
    }
    let bestVoice: undefined | [number, SpeechSynthesisVoice] = undefined;
    // find the voice with the best score
    for (let i = 0; i < voices.length; i++) {
      const voice = voices[i];
      const score = this.score(voice, { lang, preferredVoices });
      if (!bestVoice || score > bestVoice[0]) {
        bestVoice = [score, voice];
      }
    }
    if (!bestVoice) return this.default;
    this.default = [voice, bestVoice[1]];
    return this.default;
  },
};

const init = ({ text, lang = 'en-US', voice, rate = 1 }: UseTextToSpeechProps) => {
  if (!isTextToSpeechSupported) return;
  // Cancel any existing speech
  SpeechSynthesis.cancel();
  const utter = new SpeechSynthesisUtterance(orFn(text));
  utter.lang = lang;
  utter.rate = rate;
  utter.voice = speechVoices.get(lang, voice)?.[1];
  return utter;
};

export const useTextToSpeech = ({
  lang = 'en-US',
  text,
  voice,
  rate = 1,
}: UseTextToSpeechProps) => {
  const [paused, setPaused] = useState<boolean>(true);
  const utterRef = useRef<SpeechSynthesisUtterance>(undefined);

  const play = useCallback(
    (props?: Partial<UseTextToSpeechProps>) => {
      const utter =
        utterRef.current ||
        init({
          text,
          lang,
          voice,
          rate,
          ...props,
        });
      if (!utter) return;
      // Add event listeners for speech end and pause
      const onPause = () => {
        setPaused(true);
      };
      const onUnPause = () => {
        setPaused(false);
      };
      utter.onend = onPause;
      utter.onpause = onPause;
      utter.onstart = onUnPause;
      utter.onresume = onUnPause;
      utterRef.current = utter;
      // If speech has ended, restart from beginning
      if (!SpeechSynthesis.speaking) {
        SpeechSynthesis.cancel(); // Clear any previous instance
        SpeechSynthesis.speak(utter);
      } else {
        SpeechSynthesis.resume();
      }
      setPaused(false);
    },
    [lang, rate, text, voice],
  );

  const pause = useCallback(() => {
    SpeechSynthesis.pause();
    setPaused(true);
  }, []);

  useEffect(() => {
    return () => {
      SpeechSynthesis.cancel();
      utterRef.current = undefined;
      setPaused(true);
    };
  }, [text, lang, voice, rate]);

  return { paused, play, pause, init, selected: utterRef.current };
};

export const useTextToSpeechStorage = ({ lang = 'en-US', text }: UseTextToSpeechProps) => {
  const { voice, rate } = useAppStore.use.textToSpeech() || {};
  return useTextToSpeech({ lang, text, voice, rate });
};
