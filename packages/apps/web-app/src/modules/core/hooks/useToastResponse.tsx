/* eslint-disable @typescript-eslint/no-explicit-any */
import { GetResponse } from '@waitroom/common-api';
import { ReactNode, useCallback } from 'react';
import { useTranslation } from 'react-i18next';
import { getResponseMessage } from '../utils/api';
import { useToast } from './useToast';

export interface UseToastResponseProps {
  successMessage?: ReactNode;
  errorMessage?: ReactNode;
  namespace?: string;
}
export interface UseToastResponseResponse {
  onSuccess: (response?: GetResponse<any>) => void;
  onError: (response?: GetResponse<any>) => void;
  onResponse: (response: GetResponse<any>) => void;
}

export const useToastResponse = ({
  successMessage,
  errorMessage,
  namespace,
}: UseToastResponseProps = {}): UseToastResponseResponse => {
  const { t } = useTranslation();
  const { onSuccess: onSuccessT, onError: onErrorT } = useToast({
    successMessageDefault: String(t('global.success')),
    errorMessageDefault: String(t('global.error')),
  });

  const onSuccess = useCallback(
    (response?: GetResponse<any>) => {
      onSuccessT(successMessage || getResponseMessage({ response, t, namespace }));
    },
    [onSuccessT, successMessage, t, namespace],
  );

  const onError = useCallback(
    (response?: GetResponse<any>) => {
      return onErrorT(errorMessage || getResponseMessage({ response, t, namespace }));
    },
    [onErrorT, errorMessage, t, namespace],
  );

  const onResponse = useCallback(
    (response: GetResponse<any>) => {
      const msg = getResponseMessage({ response, t, namespace });
      if (!msg) return;

      if (response instanceof Error) {
        return onErrorT(response.message);
      }
      return response?.success ? onSuccessT(msg) : onErrorT(msg);
    },
    [t, namespace, onSuccessT, onErrorT],
  );

  return { onSuccess, onError, onResponse };
};
