import { useRefCallback } from '@waitroom/hooks';
import { logger } from '@waitroom/logger';
import { useCallback, useEffect, useRef, useState } from 'react';

export const SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition;
export const isSpeechSupported = !!SpeechRecognition;

export type UseSpeechToTextProps = {
  lang?: string;
  continuous?: boolean;
  interimResults?: boolean;
  onChange?: (transcript: string) => void;
  onError?: (error: SpeechRecognitionErrorEvent) => void;
};

type State = {
  error?: SpeechRecognitionErrorEvent;
  transcript?: string;
  isListening?: boolean;
};

export const useSpeechToText = ({
  lang = 'en-US',
  continuous = false,
  interimResults = false,
  onChange,
  onError,
}: UseSpeechToTextProps = {}) => {
  const [state, setState] = useState<State>({});
  const recognitionRef = useRef<SpeechRecognition>(undefined);

  const onChangeCallback = useRefCallback(onChange);
  const onErrorCallback = useRefCallback(onError);

  const updateState = useCallback((newState: Partial<State>) => {
    setState((prev) => ({ ...prev, ...newState }));
  }, []);

  const initializeRecognition = useCallback(() => {
    logger.log('Initializing SpeechRecognition');
    // Create new instance each time
    if (SpeechRecognition) {
      recognitionRef.current = new SpeechRecognition();
      const recognition = recognitionRef.current;

      recognition.lang = lang;
      recognition.continuous = continuous;
      recognition.interimResults = interimResults;

      recognition.onstart = () => {
        logger.log('Speech started');
        updateState({ isListening: true });
      };

      recognition.onend = () => {
        logger.log('Speech ended');
        updateState({ isListening: false });
      };

      recognition.onerror = (event) => {
        if (event?.error === 'aborted') return;
        logger.log('Speech error', event);
        updateState({
          error: {
            error: event.error,
            message: event.message,
          } as SpeechRecognitionErrorEvent,
          isListening: false,
        });
        onErrorCallback.current?.(event);
      };

      recognition.onresult = (event) => {
        logger.log('Speech result', event);
        let currentTranscript = '';
        for (let i = event.resultIndex; i < event.results.length; i++) {
          if (event.results[i].isFinal) {
            currentTranscript += event.results[i][0].transcript;
          }
        }
        onChangeCallback.current?.(currentTranscript);
        updateState({ transcript: currentTranscript });
      };
    }
  }, [continuous, interimResults, lang, onChangeCallback, onErrorCallback, updateState]);

  const start = useCallback(() => {
    initializeRecognition();
    recognitionRef.current?.start();
  }, [initializeRecognition]);

  const stop = () => {
    const recognition = recognitionRef.current;
    if (recognition) {
      recognition.stop();
      updateState({ isListening: false });
      // Clean up the instance
      recognitionRef.current = undefined;
    }
  };

  useEffect(() => {
    return () => {
      recognitionRef.current?.stop();
    };
  }, []);

  return { ...state, start, stop, isEnabled: isSpeechSupported };
};
