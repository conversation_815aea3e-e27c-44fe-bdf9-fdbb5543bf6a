import { useMountedRef } from '@waitroom/hooks';
import { bindEvents, unbindEvents } from '@waitroom/utils';
import { useThrottleFn } from 'ahooks';
import { useCallback, useEffect, useRef, useState } from 'react';

export const DEFAULT_EVENTS = [
  'mousemove',
  'keydown',
  'wheel',
  'DOMMouseScroll',
  'mousewheel',
  'mousedown',
  'touchstart',
  'touchmove',
  'MSPointerDown',
  'MSPointerMove',
];

export interface UseIdleProps {
  ms: number;
  events?: string[];
  throttle?: number;
  initialState?: boolean;
}
export type UseIdleResponse = boolean;

const eventOptions = {
  capture: true,
  passive: true,
};

export const useIdle = ({
  ms = 4500, // 4,5s
  events = DEFAULT_EVENTS,
  throttle = 100,
  initialState = false,
} = {}): UseIdleResponse => {
  const timeout = useRef<SetTimeout>(undefined);
  const mounted = useMountedRef();
  const [isIdle, setIdle] = useState(initialState);
  const updateIdle = useCallback(
    (val: boolean) => {
      if (isIdle === val) return;
      if (mounted.current) setIdle(val);
    },
    [isIdle, mounted],
  );
  const { run } = useThrottleFn(
    () => {
      updateIdle(false);
      if (timeout.current) clearTimeout(timeout.current);
      timeout.current = setTimeout(() => updateIdle(true), ms);
    },
    { wait: throttle },
  );

  useEffect(() => {
    const onVisibility = () => {
      if (!document.hidden) run();
    };

    bindEvents(window, events, run, eventOptions);
    bindEvents(document, ['visibilitychange'], onVisibility, eventOptions);
    timeout.current = setTimeout(() => updateIdle(true), ms);

    return () => {
      unbindEvents(window, events, run, eventOptions);
      unbindEvents(document, ['visibilitychange'], onVisibility, eventOptions);
    };
  }, [events, mounted, ms, run, updateIdle]);

  return isIdle;
};
