import { RefObject, useCallback, useEffect, useRef, useState } from 'react';
import { intersectionObserverSupported } from '../utils/dom';

const defaultOpts = { rootMargin: '150px 0px' };

export const useIntersectionObserver = <TElement extends HTMLElement>(
  options: IntersectionObserverInit = defaultOpts,
  callback: IntersectionObserverCallback,
  ref?: RefObject<TElement | null>,
) => {
  const elemRef = useRef<TElement>(ref?.current || null);
  const optRef = useRef<IntersectionObserverInit>(options);
  const observer = useRef<null | IntersectionObserver>(null);

  const cleanOb = () => {
    if (observer.current) observer.current.disconnect();
  };

  useEffect(() => {
    if (!elemRef.current) return;
    cleanOb();
    const ob = (observer.current = new IntersectionObserver(callback, {
      ...optRef.current,
    }));
    ob.observe(elemRef.current);
    return () => {
      cleanOb();
    };
  }, [callback]);

  return { ref: elemRef, observer };
};

export const useIntersectionObserverState = <TElement extends HTMLElement>(
  options: IntersectionObserverInit = defaultOpts,
  forward = true,
) => {
  const [isIntersecting, setIsIntersecting] = useState(!intersectionObserverSupported);
  const callback = useCallback<IntersectionObserverCallback>(
    ([entry], observer) => {
      const isElementIntersecting = entry.isIntersecting;
      setIsIntersecting((prev) => {
        if (forward && !prev && isElementIntersecting) observer.disconnect();
        return isElementIntersecting;
      });
    },
    [forward],
  );

  const response = useIntersectionObserver<TElement>(options, callback);

  return { ...response, isIntersecting };
};
