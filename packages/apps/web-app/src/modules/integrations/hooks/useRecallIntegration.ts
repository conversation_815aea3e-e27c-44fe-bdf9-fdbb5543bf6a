import { useQuery, useQueryClient } from '@tanstack/react-query';
import {
  RecallCalendarConnection,
  RecallCalendarPlatform,
  recallCalendarService,
} from '@waitroom/common-api';
import { logger } from '@waitroom/logger';
import { useCallback, useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import { CDN_IMAGES_URL } from '../../core/config';
import { useSessionToast } from '../../session/hooks/useSessionToast';
import { TFunction } from 'i18next';
import { ProviderConnection } from '@waitroom/models';

export const recallIntegrationQueryKey = ['recall-integration'];

const mapToProvider = (connection: RecallCalendarConnection, t: TFunction): ProviderConnection => ({
  provider: {
    name: connection.platform,
    label: t(`integrations.recallCalendar.${connection.platform}`),
    icon: { url: CDN_IMAGES_URL + `/${connection.platform}-calendar.png` },
    description: 'Recall',
    integrations: [
      {
        name: 'recall',
        actions: [
          {
            name: 'recall',
            aiFeedItemTypes: ['zoom', 'google', 'microsoft'],
            callout: 'connect to recall',
          },
        ],
      },
    ],
  },
  user: {
    email: connection.email,
  },
  integration: {
    name: 'recall',
  },
  connectionId: '',
  createdAt: '',
});

export const getRecallIntegrationConnections = {
  queryKey: recallIntegrationQueryKey,
  queryFn: recallCalendarService.getCalendarUser,
};

export const useRecallIntegration = () => {
  const toast = useSessionToast();
  const queryClient = useQueryClient();
  const { t } = useTranslation();

  const query = useQuery(getRecallIntegrationConnections);
  const { refetch } = query;
  const connections = query.data?.connections;

  const providerCategory = useMemo(() => {
    if (!connections?.length) return undefined;
    const connectedProviders = connections
      ?.filter((connection) => connection.connected)
      ?.map((connection) => mapToProvider(connection, t));

    return {
      name: 'recall',
      label: 'AI Notetaker',
      connectedProviders: connectedProviders || [],
      unconnectedProviders: [], // TODO: add unconnected providers
    };
  }, [connections, t]);

  const handleConnectCalendar = useCallback(async () => {
    try {
      const response = await recallCalendarService.getGoogleAuthURL();
      // After auth the user is redirected to the backend, which then will save state and redirect to the frontend.
      // see backend/elio/transcriptions/recall.go for more details.
      window.location.href = response;
    } catch (error) {
      queryClient.setQueryData(recallIntegrationQueryKey, undefined);
      toast(t('integrations.recallCalendar.errors.unableToConnect'), 'error');
      logger.log('Error while connecting to Recall calendar', error);
    }
  }, [queryClient, t, toast]);

  const handleDisconnectCalendar = useCallback(
    async (platform: RecallCalendarPlatform) => {
      try {
        await recallCalendarService.disconnectCalendar(platform);
        queryClient.setQueryData(recallIntegrationQueryKey, undefined);
        refetch(); // TODO: Optimize this by updating cached data
      } catch (error) {
        toast(t('integrations.recallCalendar.errors.unableToDisconnect'), 'error');
        logger.log('Error while disconnecting from Recall calendar', error);
      }
    },
    [refetch, queryClient, t, toast],
  );

  const handleConnectMicrosoftCalendar = useCallback(async () => {
    try {
      const response = await recallCalendarService.getMicrosoftAuthURL();
      window.location.href = response;
    } catch (error) {
      queryClient.setQueryData(recallIntegrationQueryKey, undefined);
      toast(t('integrations.recallCalendar.errors.unableToConnect'), 'error');
      logger.log('Error while connecting to Microsoft calendar', error);
    }
  }, [queryClient, t, toast]);

  return {
    query,
    providerCategory,
    handleConnectCalendar,
    handleDisconnectCalendar,
    handleConnectMicrosoftCalendar,
  };
};

export type UseRecallIntegrationResponse = ReturnType<typeof useRecallIntegration>;
