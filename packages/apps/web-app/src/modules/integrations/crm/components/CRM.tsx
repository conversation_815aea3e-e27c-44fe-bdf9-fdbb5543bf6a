import { Box, Button, Circle, Flex, <PERSON>ing, Stack, Tooltip } from '@chakra-ui/react';
import { Icon } from '@core/components/Icon/Icon';
import { useAddTranslations } from '@core/hooks/useAddTranslations';
import { useCRM } from '@core/hooks/useCRM';
import { faInfo } from '@fortawesome/pro-solid-svg-icons';
import { selectCurrentUserId, selectIsCurrentUserGuest, useAuthStore } from '@waitroom/auth';
import { useGetConnections } from '@waitroom/common';
import { SessionStatus } from '@waitroom/models';
import { useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useIntegrationConnect } from '../../hooks/useIntegrations';
import { useIsIntegrationsEnabled } from '../../hooks/useIsIntegrationsEnabled';
import { getProviderComponent } from '../utils/componentsMap';
import { unconnectedButtons } from '../utils/constants';
import { trans } from '../utils/locals';
import { CRMProvider } from './CRMProvider';

export type CRMProps = {
  sessionId?: string;
  sessionRecurrenceId?: string;
  sessionStatus: SessionStatus;
};

export const CRM = ({ sessionId, sessionRecurrenceId, sessionStatus }: CRMProps) => {
  useAddTranslations(trans, 'crm');
  const { t } = useTranslation('crm');
  const currentUserId = useAuthStore(selectCurrentUserId);
  const isGuest = useAuthStore(selectIsCurrentUserGuest);
  const { refetch } = useGetConnections({
    userId: currentUserId,
    enabled: !!currentUserId && !isGuest,
  });
  const { onClickConnect, isConnecting } = useIntegrationConnect({
    onConnect: refetch,
  });
  const [connectingProvider, setConnectingProvider] = useState<string | undefined>();
  const { unconnected, connected } = useCRM();
  useEffect(() => {
    if (!isConnecting) {
      setConnectingProvider(undefined);
    }
  }, [isConnecting]);
  const { isEnabled, redirectTo } = useIsIntegrationsEnabled();

  if (!connected.length && !unconnected.length) return null;

  if (connected.length) {
    return (
      <Stack gap={2} w="full">
        {connected.map((provider) => (
          <CRMProvider
            name={provider}
            key={provider}
            sessionId={sessionId}
            sessionRecurrenceId={sessionRecurrenceId}
            sessionStatus={sessionStatus}
          />
        ))}
      </Stack>
    );
  }

  if (unconnected.length === 1) {
    const Component = getProviderComponent(unconnected[0]);
    if (!Component) return null;

    return (
      <Component
        sessionId={sessionId}
        sessionRecurrenceId={sessionRecurrenceId}
        sessionStatus={sessionStatus}
      />
    );
  }

  return (
    <Box rounded="xl" bgColor="gray.800" px={4} py={5} w={'full'}>
      <Flex gap={2} alignItems="center">
        <Heading fontSize="md" fontWeight={800} color="white">
          {t('selector.title')}
        </Heading>
        <Tooltip label={t('selector.info')}>
          <Circle bgColor="gray.700" color="white" p={1}>
            <Icon icon={faInfo} fontSize="xs" />
          </Circle>
        </Tooltip>
      </Flex>
      <Flex mt={3} gap={2}>
        {unconnected.map((provider) => (
          <Button
            key={provider}
            size="xs"
            fontSize="sm"
            colorScheme="gray"
            bgColor="t.gray-900-50"
            px={4}
            leftIcon={<Icon p={0.5} rounded="md" h={4} w={4} {...unconnectedButtons[provider]} />}
            isLoading={isConnecting && connectingProvider === provider}
            onClick={() => {
              if (!isEnabled) {
                window.open(redirectTo, '_blank', 'noopener,noreferrer');
                return;
              }
              setConnectingProvider(provider);
              onClickConnect({
                integrationId: provider,
              });
            }}
          >
            {t(provider)}
          </Button>
        ))}
      </Flex>
    </Box>
  );
};
