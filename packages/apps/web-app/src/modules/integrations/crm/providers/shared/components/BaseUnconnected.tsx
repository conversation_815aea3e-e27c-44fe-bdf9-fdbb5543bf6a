import { BoxProps, Circle, Spinner } from '@chakra-ui/react';
import { Icon } from '@core/components/Icon/Icon';
import { faChevronRight } from '@fortawesome/pro-solid-svg-icons';
import { ReactElement } from 'react';
import { useIntegrationConnect } from '../../../../hooks/useIntegrations';
import { useIsIntegrationsEnabled } from '../../../../hooks/useIsIntegrationsEnabled';
import { Layout } from './Layout';

type BaseUnconnectedProps = Omit<BoxProps, 'onClick'> & {
  onConnect: () => void;
  headerText: string;
  infoText: string;
  integrationId: string;
  leftIcon: ReactElement;
};

export const BaseUnconnected = ({
  onConnect,
  headerText,
  infoText,
  integrationId,
  leftIcon,
  ...rest
}: BaseUnconnectedProps) => {
  const { onClickConnect, isConnecting } = useIntegrationConnect({ onConnect });
  const { isEnabled, redirectTo } = useIsIntegrationsEnabled();

  return (
    <Layout
      headerText={headerText}
      infoText={infoText}
      onClick={() => {
        if (!isEnabled) {
          window.open(redirectTo, '_blank', 'noopener,noreferrer');
          return;
        }
        onClickConnect({
          integrationId,
        });
        onConnect();
      }}
      rightIcon={
        isConnecting ? (
          <Circle>
            <Spinner size="sm" />
          </Circle>
        ) : (
          <Icon icon={faChevronRight} color="gray.300" />
        )
      }
      leftIcon={leftIcon}
      {...rest}
    />
  );
};
