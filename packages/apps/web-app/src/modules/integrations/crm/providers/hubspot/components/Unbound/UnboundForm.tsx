import { Button, <PERSON>lex, <PERSON><PERSON><PERSON><PERSON><PERSON>, Text } from '@chakra-ui/react';
import { Autocomplete } from '@core/components/Autocomplete/Autocomplete';
import { Icon } from '@core/components/Icon/Icon';
import { UnboundFormContextType, useUnboundFormContext } from '@core/contexts/UnboundForm';
import { faBullseyePointer, faClose } from '@fortawesome/pro-solid-svg-icons';
import { HubspotBindableOption, SessionStatus } from '@waitroom/models';
import { useCallback, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { AutocompleteItem } from '../../../shared/components/AutocompleteItem';
import { getOptionIcon } from '../../utils/utils';
import { useUnboundForm, useUnboundFormQuery } from './useUnboundForm';

type UnboundFormProps = {
  sessionId?: string;
  sessionRecurrenceId?: string;
  onClose: () => void;
  sessionStatus: SessionStatus;
};

export const UnboundForm = ({
  onClose,
  sessionId,
  sessionRecurrenceId,
  sessionStatus,
}: UnboundFormProps) => {
  const { t } = useTranslation('hubspot');
  const { selectedOption, setSelectedOption, isPending, bind, onSelect } = useUnboundForm({
    t,
  });
  const { queryFn } = useUnboundFormQuery(sessionId, sessionRecurrenceId);

  const inputTipText = `inputTip${selectedOption ? (sessionStatus === SessionStatus.ENDED ? 3 : 2) : 1}`;
  const submitButtonText = t(
    `${sessionStatus === SessionStatus.ENDED ? 'sendToHubspot' : 'linkToHubspot'}`,
  );

  const onClick = useCallback(
    () => (sessionId && sessionRecurrenceId ? bind(sessionId, sessionRecurrenceId) : undefined),
    [bind, sessionId, sessionRecurrenceId],
  );

  const { onSubmitRef, autoSave } = useUnboundFormContext() || ({} as UnboundFormContextType);

  useEffect(() => {
    if (onSubmitRef?.current) onSubmitRef.current[`hubspot`] = bind;
  }, [bind, onSubmitRef]);

  useEffect(() => {
    if (autoSave && selectedOption && sessionId && sessionRecurrenceId)
      bind(sessionId, sessionRecurrenceId);
  }, [autoSave, bind, selectedOption, sessionId, sessionRecurrenceId]);

  return (
    <>
      <Autocomplete
        placeholder={t(`placeholder`)}
        placeholderIcon={faBullseyePointer}
        onSelect={onSelect}
        queryKey={['hubspot-autocomplete', sessionId, sessionRecurrenceId]}
        queryFn={queryFn}
        renderSelectedItem={(option, onClear) => (
          <Flex
            alignItems="center"
            gap={2}
            rounded="lg"
            borderWidth="thin"
            borderColor="gray.700"
            bgColor="t.gray-900-50"
            h="52px"
            p={4}
            fontSize="sm"
          >
            <Icon icon={getOptionIcon(option)} h={5} w={5} />
            <Text flex={1}>{option.name}</Text>
            <IconButton
              aria-label="clear"
              size="sm"
              w="auto"
              minW="auto"
              variant="unstyled"
              icon={<Icon icon={faClose} color="white" />}
              onClick={onClear}
              mr={1}
            />
          </Flex>
        )}
        inputSx={{
          h: '52px',
          bgColor: 't.gray-900-50',
          borderColor: 'gray.700',
          borderWidth: 'thin',
          _focus: {
            borderColor: 'gray.700',
          },
          _placeholder: {
            color: 'gray.300',
          },
        }}
      >
        {(data, onClick) =>
          data.length > 0 ? (
            data.map((option: HubspotBindableOption) => (
              <AutocompleteItem
                key={option.id}
                option={option}
                onClick={onClick}
                getOptionIcon={getOptionIcon}
                label={(option) => option.name}
                secondaryLabel={(option) => option.secondaryText}
                tertiaryLabel={(option) => option.tertiaryText}
                isSelected={(option) => option.selected}
              />
            ))
          ) : (
            <Text fontSize="sm" color="gray.200">
              {t('noResults')}
            </Text>
          )
        }
      </Autocomplete>
      <Text fontSize="sm" color="gray.200">
        {t(inputTipText)}
      </Text>
      {!onSubmitRef && (
        <Flex rounded="sm" gap={2} w="full" mt={2}>
          <Button
            colorScheme="green"
            rounded="md"
            size="xs"
            flex={1}
            fontSize="md"
            onClick={onClick}
            isLoading={isPending}
            isDisabled={!selectedOption}
          >
            {submitButtonText}
          </Button>
          <Button
            variant="outline"
            rounded="md"
            size="xs"
            fontSize="md"
            onClick={() => {
              setSelectedOption(undefined);
              onClose();
            }}
            borderColor="gray.500"
            color="gray.200"
          >
            {t(`global.cancel`, { ns: 'translation' })}
          </Button>
        </Flex>
      )}
    </>
  );
};
