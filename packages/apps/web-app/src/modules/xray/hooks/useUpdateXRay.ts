import { useMutation, UseMutationOptions, useQueryClient } from '@tanstack/react-query';
import { useAuthStore } from '@waitroom/auth';
import { DefaultApiResponse, XRayApiService } from '@waitroom/common-api';
import { XRay } from '@waitroom/models';
import { getRequestData, updateXRayMutation, xrayCacheService } from '@waitroom/react-query';

export type UseUpdateXRayProps = {
  id: XRay['id'];
} & UseMutationOptions<
  XRayApiService.Update['response'],
  DefaultApiResponse,
  XRayApiService.UpdateData
>;

export const useUpdateXRay = ({ id, ...rest }: UseUpdateXRayProps) => {
  const client = useQueryClient();
  return useMutation({
    ...updateXRayMutation({ id }),
    ...rest,
    onSuccess: (...args) => {
      rest.onSuccess?.(...args);
      const data = getRequestData(args[0]);
      if (!data) return;
      xrayCacheService.update({
        client,
        data,
        userId: useAuthStore.getState().userId || '',
      });
    },
  });
};
