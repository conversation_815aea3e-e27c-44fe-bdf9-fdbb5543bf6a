import { useMutation, UseMutationOptions, useQueryClient } from '@tanstack/react-query';
import { useAuthStore } from '@waitroom/auth';
import { DefaultApiResponse, XRayApiService } from '@waitroom/common-api';
import { XRay } from '@waitroom/models';
import { deleteXRayMutation, xrayCacheService } from '@waitroom/react-query';

export type UseDeleteXRayProps = {
  id: XRay['id'];
} & UseMutationOptions<
  XRayApiService.Delete['response'],
  DefaultApiResponse,
  XRayApiService.Delete['params']['data']
>;

export const useDeleteXRay = ({ id, ...rest }: UseDeleteXRayProps) => {
  const client = useQueryClient();
  return useMutation({
    ...deleteXRayMutation({ id }),
    ...rest,
    onSuccess: (...args) => {
      rest.onSuccess?.(...args);
      xrayCacheService.remove({
        client,
        id,
        userId: useAuthStore.getState().userId || '',
      });
    },
  });
};
