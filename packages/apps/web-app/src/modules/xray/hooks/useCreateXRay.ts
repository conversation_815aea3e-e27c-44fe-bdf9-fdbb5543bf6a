import { useMutation, UseMutationOptions, useQueryClient } from '@tanstack/react-query';
import { useAuthStore } from '@waitroom/auth';
import { DefaultApiResponse, XRayApiService } from '@waitroom/common-api';
import { createXRayMutation, getRequestData, xrayCacheService } from '@waitroom/react-query';

export type UseCreateXRayProps = UseMutationOptions<
  XRayApiService.Create['response'],
  DefaultApiResponse,
  XRayApiService.CreateData
>;

export const useCreateXRay = (props: UseCreateXRayProps) => {
  const client = useQueryClient();
  return useMutation({
    ...createXRayMutation(),
    ...props,
    onSuccess: (...args) => {
      props.onSuccess?.(...args);
      const data = getRequestData(args[0]);
      if (!data) return;
      xrayCacheService.add({
        client,
        data,
        userId: useAuthStore.getState().userId || '',
      });
    },
  });
};
