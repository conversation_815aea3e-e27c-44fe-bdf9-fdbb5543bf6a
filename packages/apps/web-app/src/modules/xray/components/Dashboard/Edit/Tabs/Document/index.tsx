import Markdown from '@core/components/Markdown/Markdown';
import { XRayDocument } from '@waitroom/models';
import { ReactElement } from 'react';

export type DocumentProps = {
  data: XRayDocument | undefined;
};

const Document = ({ data }: DocumentProps): ReactElement | null => {
  // ! TODO: no data ui
  if (!data?.content) return null;
  return <Markdown>{data.content}</Markdown>;
};
export default Document;
