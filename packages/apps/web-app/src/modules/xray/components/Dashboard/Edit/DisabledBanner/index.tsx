import { Button, Flex, Text } from '@chakra-ui/react';
import { Icon } from '@core/components/Icon/Icon';
import { faEyeSlash, faPowerOff, faTrashCan } from '@fortawesome/pro-regular-svg-icons';
import { ReactElement } from 'react';
import { useTranslation } from 'react-i18next';

export type DisabledBannerProps = {
  onReactivate: () => void;
  onDelete: () => void;
};

const ns = 'dashboard.xray.disabledBanner';

const DisabledBanner = ({ onReactivate, onDelete }: DisabledBannerProps): ReactElement | null => {
  const { t } = useTranslation();

  return (
    <Flex
      pos={'relative'}
      direction={['column', null, 'row']}
      w={'full'}
      align={[null, null, 'center']}
      justify={'space-between'}
      borderWidth={'2px'}
      borderColor={'gray.800'}
      rounded={'xl'}
      gap={2}
      py={[2, 3, 4]}
      px={[4, 5, 6]}
      my={[4, 5, 6]}
    >
      <Flex align={'center'} fontSize={'sm'} gap={3}>
        <Icon icon={faEyeSlash} size={'xl'} />
        <Text>{t(`${ns}.message`)}</Text>
      </Flex>
      <Flex align={'center'} gap={3}>
        <Button
          variant={'outline'}
          colorScheme={'green'}
          color={'white'}
          size={'2xs'}
          leftIcon={<Icon icon={faPowerOff} />}
          onClick={onReactivate}
        >
          {t(`${ns}.reactivate`)}
        </Button>
        <Button
          variant={'outline'}
          colorScheme={'gray.500'}
          color={'white'}
          size={'2xs'}
          onClick={onDelete}
          leftIcon={<Icon icon={faTrashCan} color={'gray.500'} />}
        >
          {t('global.delete')}
        </Button>
      </Flex>
    </Flex>
  );
};

export default DisabledBanner;
