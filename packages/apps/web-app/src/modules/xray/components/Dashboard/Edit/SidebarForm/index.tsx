import {
  Accordion,
  AccordionButton,
  AccordionIcon,
  AccordionItem,
  AccordionPanel,
  Box,
  <PERSON><PERSON>,
  Drawer,
  <PERSON>er<PERSON>ody,
  Drawer<PERSON>lose<PERSON><PERSON>on,
  <PERSON>er<PERSON>ontent,
  Drawer<PERSON>eader,
  <PERSON>er<PERSON><PERSON>lay,
  Flex,
  Heading,
  IconButton,
  Text,
  useDisclosure,
} from '@chakra-ui/react';
import { Icon } from '@core/components/Icon/Icon';
import { useToastResponse } from '@core/hooks/useToastResponse';
import {
  faArrowsH,
  faBell,
  faListTree,
  faSubtitles,
  faWrench,
} from '@fortawesome/pro-regular-svg-icons';
import { zodResolver } from '@hookform/resolvers/zod';
import { XRay, XRayType } from '@waitroom/models';
import { useMemo, useState } from 'react';
import { FormProvider, useForm } from 'react-hook-form';
import { useTranslation } from 'react-i18next';
import { useUpdateXRay } from '../../../../hooks/useUpdateXRay';
import { editFormNs } from '../../constants';
import { fromFormValues, toFormValues } from '../../helpers';
import { schema, XRayFormValues } from '../../schema';
import DiscardConfirmModal from '../DiscardConfirmModal';
import EditSuccessModal from '../EditSuccessModal';
import Alerts from './Alerts';
import Frequency from './Frequency';
import Info from './Info';
import Prompt from './Prompt';

export type XRayEditSidebarBodyProps = {
  onClose: () => void;
  onOpen: () => void;
  defaultIndex?: number;
  data: XRay;
  onSuccess?: () => void;
  toggleExpanded: () => void;
};

const XRayEditSidebarBody = ({
  onClose,
  data,
  defaultIndex,
  onSuccess,
  toggleExpanded,
}: XRayEditSidebarBodyProps) => {
  const { t } = useTranslation();
  const tr = useToastResponse({
    successMessage: t(`${editFormNs}.response.success`),
  });
  const exitDisclosure = useDisclosure();
  const successDisclosure = useDisclosure();
  const mutation = useUpdateXRay({
    id: data.id,
    onError: tr.onError,
    onSuccess: (r) => {
      onClose();
      onSuccess?.();
      tr.onSuccess(r);
    },
  });
  const s = useMemo(() => schema(t), [t]);
  const form = useForm<XRayFormValues>({
    resolver: zodResolver(s),
    defaultValues: toFormValues(data),
  });

  const onSubmit = (values: XRayFormValues) => {
    mutation.mutate(fromFormValues(values));
  };
  const onCancel = () => {
    if (form.formState.isDirty) exitDisclosure.onOpen();
    else onClose();
  };
  const handleConfirmDiscard = () => {
    form.reset();
    exitDisclosure.onClose();
    onClose();
  };

  return (
    <>
      <FormProvider {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)}>
          <DrawerContent bg={'gray.1000'} color={'white'}>
            <DrawerHeader
              display={'flex'}
              py={4}
              gap={2}
              alignItems={'center'}
              justifyContent={'space-between'}
            >
              <Heading as={'h3'} size={'lg'}>
                <Icon icon={faWrench} mr={3} />
                {t(`${editFormNs}.title`)}
              </Heading>
              <Flex align={'center'} gap={2}>
                <IconButton
                  aria-label={t('global.expand')}
                  title={t('global.expand')}
                  colorScheme={'gray.100'}
                  size={'2xs'}
                  onClick={toggleExpanded}
                >
                  <Icon icon={faArrowsH} size={'lg'} />
                </IconButton>
                <DrawerCloseButton position={'relative'} top={0} right={0} />
              </Flex>
            </DrawerHeader>
            <DrawerBody p={0} display={'flex'} flexDirection={'column'}>
              <Accordion allowMultiple={false} allowToggle defaultIndex={defaultIndex}>
                <AccordionItem>
                  <AccordionButton
                    px={[4, 6]}
                    py={4}
                    color={'gray.300'}
                    alignItems={'flex-start'}
                    textAlign={'left'}
                  >
                    <Box flex={1} as={'span'}>
                      <Flex as={'span'} align={'center'} gap={3} flex={1}>
                        <Icon icon={faSubtitles} />
                        <Text as={'span'} fontWeight="bold">
                          {t(`${editFormNs}.info.title`)}
                        </Text>
                      </Flex>
                      <Text as={'span'} fontSize={'xs'}>
                        {t(`${editFormNs}.info.desc`)}
                      </Text>
                    </Box>
                    <AccordionIcon mt={1} />
                  </AccordionButton>
                  <AccordionPanel px={[4, 6]} pb={4}>
                    <Info />
                  </AccordionPanel>
                </AccordionItem>
                <AccordionItem>
                  <AccordionButton
                    px={[4, 6]}
                    py={4}
                    color={'gray.300'}
                    alignItems={'flex-start'}
                    textAlign={'left'}
                  >
                    <Box flex={1} as={'span'}>
                      <Flex as={'span'} align={'center'} gap={3} flex={1}>
                        <Icon icon={faListTree} />
                        <Text as={'span'} fontWeight="bold">
                          {t(`${editFormNs}.prompt.title`)}
                        </Text>
                      </Flex>
                      <Text as={'span'} fontSize={'xs'}>
                        {t(`${editFormNs}.prompt.desc`)}
                      </Text>
                    </Box>
                    <AccordionIcon mt={1} />
                  </AccordionButton>
                  <AccordionPanel px={[4, 6]} pb={4}>
                    <Prompt />
                  </AccordionPanel>
                </AccordionItem>
                {data.type === XRayType.Digest && (
                  <AccordionItem>
                    <AccordionButton
                      px={[4, 6]}
                      py={4}
                      color={'gray.300'}
                      alignItems={'flex-start'}
                      textAlign={'left'}
                    >
                      <Box flex={1} as={'span'}>
                        <Flex as={'span'} align={'center'} gap={3} flex={1}>
                          <Icon icon={faBell} />
                          <Text as={'span'} fontWeight="bold">
                            {t(`${editFormNs}.frequency.title`)}
                          </Text>
                        </Flex>
                        <Text as={'span'} fontSize={'xs'}>
                          {t(`${editFormNs}.frequency.desc`)}
                        </Text>
                      </Box>
                      <AccordionIcon mt={1} />
                    </AccordionButton>
                    <AccordionPanel px={[4, 6]} pb={4}>
                      <Frequency />
                    </AccordionPanel>
                  </AccordionItem>
                )}
                <AccordionItem>
                  <AccordionButton
                    px={[4, 6]}
                    py={4}
                    color={'gray.300'}
                    alignItems={'flex-start'}
                    textAlign={'left'}
                  >
                    <Box flex={1} as={'span'}>
                      <Flex as={'span'} align={'center'} gap={3} flex={1}>
                        <Icon icon={faBell} />
                        <Text as={'span'} fontWeight="bold">
                          {t(`${editFormNs}.alerts.title`)}
                        </Text>
                      </Flex>
                      <Text as={'span'} fontSize={'xs'}>
                        {t(`${editFormNs}.alerts.desc`)}
                      </Text>
                    </Box>
                    <AccordionIcon mt={1} />
                  </AccordionButton>
                  <AccordionPanel px={[4, 6]} pb={4}>
                    <Alerts />
                  </AccordionPanel>
                </AccordionItem>
              </Accordion>
              <Flex
                gap={4}
                py={[2, 4]}
                px={[4, 6]}
                mt={'auto'}
                bgColor={'gray.900'}
                roundedTop={'xl'}
              >
                <Button
                  variant={'outline'}
                  colorScheme={'gray.500'}
                  color={'white'}
                  size={'sm'}
                  onClick={onCancel}
                >
                  {t('global.cancel')}
                </Button>
                <Button
                  colorScheme={'red'}
                  size={'sm'}
                  flex={1}
                  type="submit"
                  isLoading={mutation.isPending}
                  isDisabled={!form.formState.isDirty}
                >
                  {t(`${editFormNs}.saveBtn`)}
                </Button>
              </Flex>
            </DrawerBody>
          </DrawerContent>
        </form>
      </FormProvider>
      <DiscardConfirmModal {...exitDisclosure} onConfirm={handleConfirmDiscard} />
      <EditSuccessModal {...successDisclosure} />
    </>
  );
};

export type XRayEditSidebarProps = Omit<XRayEditSidebarBodyProps, 'toggleExpanded'> & {
  isOpen: boolean;
};

const XRayEditSidebar = ({ isOpen, onClose, ...rest }: XRayEditSidebarProps) => {
  const [isExpanded, setIsExpanded] = useState(false);
  return (
    <Drawer placement={'right'} isOpen={isOpen} onClose={onClose} size={isExpanded ? 'xl' : 'sm'}>
      <DrawerOverlay />
      {isOpen && (
        <XRayEditSidebarBody
          onClose={onClose}
          {...rest}
          toggleExpanded={() => setIsExpanded(!isExpanded)}
        />
      )}
    </Drawer>
  );
};

export default XRayEditSidebar;
