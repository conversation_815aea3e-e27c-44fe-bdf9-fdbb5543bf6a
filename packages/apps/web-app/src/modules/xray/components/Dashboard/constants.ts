import {
  faDisplayChartUp,
  faListOl,
  faWandMagicSparkles,
  IconDefinition,
} from '@fortawesome/pro-regular-svg-icons';
import { XRayType } from '@waitroom/models';

export const baseNs = 'dashboard.xray';
export const createFormNs = `${baseNs}.createForm`;
export const editFormNs = `${baseNs}.editForm`;

export const xrayTypeOptions: Record<
  XRayType,
  {
    icon: IconDefinition;
    color: string;
  }
> = {
  [XRayType.Monitor]: {
    icon: faDisplayChartUp,
    color: 'red.500',
  },
  [XRayType.Digest]: {
    icon: faListOl,
    color: 'green.500',
  },
  [XRayType.Build]: {
    icon: faWandMagicSparkles,
    color: 'orange.700',
  },
};
