import { Button, Heading, Text } from '@chakra-ui/react';
import Modal, { ModalProps } from '@core/components/Modal/Modal';
import { ReactElement } from 'react';
import { useTranslation } from 'react-i18next';
import { editFormNs } from '../../constants';

export type DiscardConfirmModalProps = ModalProps & {
  onConfirm: () => void;
};

const DiscardConfirmModal = ({
  onConfirm,
  ...rest
}: DiscardConfirmModalProps): ReactElement | null => {
  const { t } = useTranslation();
  return (
    <Modal {...rest} isCentered size="lg">
      <Modal.Header />
      <Modal.Body mb={6}>
        <Heading as="h4" size="2xl" mb={6}>
          {t(`${editFormNs}.discardModal.title`)}
        </Heading>
        <Text>{t(`${editFormNs}.discardModal.description`)}</Text>
      </Modal.Body>
      <Modal.Footer display="flex" justifyContent="center" gap={4}>
        <Button variant="outline" colorScheme="black" size={'def'} onClick={onConfirm}>
          {t(`${editFormNs}.discardModal.confirmBtn`)}
        </Button>
        <Button colorScheme="black" size={'def'} onClick={rest.onClose}>
          {t(`${editFormNs}.discardModal.cancelBtn`)}
        </Button>
      </Modal.Footer>
    </Modal>
  );
};
export default DiscardConfirmModal;
