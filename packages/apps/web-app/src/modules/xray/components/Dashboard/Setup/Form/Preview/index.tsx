import { Box, Button, Flex, FlexProps, Heading, Text, VStack } from '@chakra-ui/react';
import { Icon, IconProps } from '@core/components/Icon/Icon';
import {
  faBell,
  faList,
  faMagnifyingGlass,
  faPlus,
  faSubtitles,
  faTag,
} from '@fortawesome/pro-solid-svg-icons';
import { XRayComplete } from '@waitroom/models';
import { ReactElement, ReactNode } from 'react';
import { useFormContext } from 'react-hook-form';
import { Trans, useTranslation } from 'react-i18next';
import { Link } from 'react-router-dom';
import { routes } from '../../../../../../../constants/routes';
import { createFormNs, xrayTypeOptions } from '../../../constants';
import { XRayFormValues } from '../../../schema';
import { StageProps } from '../types';

export type PreviewItemProps = FlexProps & {
  icon: IconProps['icon'];
  label: string;
  value: ReactNode | string;
};

const ns = `${createFormNs}.preview`;
const PreviewItem = ({ icon, label, value, ...rest }: PreviewItemProps) => (
  <Flex w="full" align={'center'} py={4} gap={4} {...rest}>
    <Flex align={'center'} w={'full'} maxW={'160px'} color="gray.300">
      <Icon icon={icon} mr={2} />
      <Text as="span" fontSize="sm" fontWeight="bold">
        {label}
      </Text>
    </Flex>
    <Box flex="1">{typeof value === 'string' ? <Text>{value}</Text> : value}</Box>
  </Flex>
);

export type PreviewProps = StageProps & {
  owner?: XRayComplete['owner'];
};

const Preview = ({ onNext, onDiscard, owner }: PreviewProps): ReactElement | null => {
  const { t } = useTranslation();
  const { getValues } = useFormContext<XRayFormValues>();
  const { title, type, prompt, shortSummary, icon } = getValues();
  const selectedTypeOption = xrayTypeOptions[type];

  return (
    <>
      <Flex p={[4, 5, 6, 10, 12]} direction={'column'} justify={'center'} minH="480px" gap={8}>
        <Flex direction={'column'} gap={2} align={'center'} textAlign={'center'}>
          <Heading as={'h3'} size={'xl'} mb={1}>
            <Icon icon={faTag} color={'gray.500'} mr={2} />
            {owner ? t(`${ns}.sharedTitle`, { name: owner.firstName }) : t(`${ns}.title`)}
          </Heading>
          <Text color={'gray.300'} mb={4} fontSize={'sm'}>
            {owner ? (
              <Trans i18nKey={`${ns}.sharedDescription`} />
            ) : (
              <Trans i18nKey={`${ns}.description`} />
            )}{' '}
            <Link to={routes.DASHBOARD.XRAY.DEFAULT.link} className="strong">
              {t('global.learnMore')}
            </Link>
          </Text>
        </Flex>
        <VStack spacing={0} w="full">
          <PreviewItem
            icon={faMagnifyingGlass}
            borderBottom={'1px'}
            borderColor={'gray.600'}
            label={t(`${ns}.titleLabel`)}
            value={
              <Text as={'strong'} fontSize={'lg'}>
                <Text as={'span'} color={'gray.300'} fontSize={'2xl'} mr={2}>
                  {icon}
                </Text>
                {title}
              </Text>
            }
          />
          <PreviewItem
            icon={faBell}
            borderBottom={'1px'}
            borderColor={'gray.600'}
            label={t(`${ns}.alertsLabel`)}
            value={'/'}
          />
          <PreviewItem
            icon={faTag}
            label={t(`${ns}.typeLabel`)}
            borderBottom={'1px'}
            borderColor={'gray.600'}
            value={
              <Flex align="center" gap={4}>
                <Icon icon={selectedTypeOption.icon} color={selectedTypeOption.color} size={'xl'} />
                <Box>
                  <Text as={'strong'}>{t(`dashboard.xray.type.${type}.label`)}</Text>
                  <Text
                    as={'span'}
                    display={'flex'}
                    color={'gray.300'}
                    fontSize={'sm'}
                    lineHeight={'shorter'}
                  >
                    {t(`dashboard.xray.type.${type}.desc`)}
                  </Text>
                </Box>
              </Flex>
            }
          />
          <PreviewItem
            align={'flex-start'}
            icon={faSubtitles}
            borderBottom={'1px'}
            borderColor={'gray.600'}
            label={t(`${ns}.summaryLabel`)}
            value={shortSummary}
          />
          <PreviewItem
            align={'flex-start'}
            icon={faList}
            label={t(`${ns}.promptLabel`)}
            value={
              <Text fontFamily={'monospace'} whiteSpace="pre-wrap">
                {prompt}
              </Text>
            }
          />
        </VStack>
      </Flex>
      <Flex
        direction={{ base: 'column', md: 'row' }}
        justify={'space-between'}
        align={'center'}
        gap={2}
        px={6}
        py={[3, 3, 4]}
        borderTop="1px"
        borderColor="gray.600"
        position={'sticky'}
        bottom={0}
        bg={'gray.1000'}
        zIndex={2}
      >
        <Button
          variant="outline"
          size={{ base: 'xs', lg: 'sm' }}
          type={'button'}
          colorScheme={'gray.400'}
          color={'white'}
          onClick={onDiscard}
        >
          {t('global.discard')}
        </Button>
        <Button
          colorScheme={'green'}
          type="submit"
          px={[6, 8, 14]}
          size={{ base: 'xs', lg: 'sm' }}
          leftIcon={<Icon icon={faPlus} size={'lg'} />}
        >
          {t(`${ns}.submitBtn`)}
        </Button>
        <Button
          variant="outline"
          size={{ base: 'xs', lg: 'sm' }}
          type={'button'}
          colorScheme={'gray.400'}
          color={'white'}
          onClick={onNext}
        >
          {t(`${ns}.editBtn`)}
        </Button>
      </Flex>
    </>
  );
};
export default Preview;
