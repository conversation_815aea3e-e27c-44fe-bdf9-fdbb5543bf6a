import { Box, Heading, Text } from '@chakra-ui/react';
import { Icon } from '@core/components/Icon/Icon';
import { faFileSlash } from '@fortawesome/pro-solid-svg-icons';
import { ReactElement } from 'react';
import { useTranslation } from 'react-i18next';

const ns = 'dashboard.xray';

const NoData = (): ReactElement | null => {
  const { t } = useTranslation();
  return (
    <Box my={6}>
      <Heading as={'h5'} size={'md'} mb={2}>
        <Icon icon={faFileSlash} size={'lg'} mr={4} />
        {t(`${ns}.noData.header`)}
      </Heading>
      <Text color={'gray.300'} mb={8}>
        {t(`${ns}.noData.body`)}
      </Text>
    </Box>
  );
};
export default NoData;
