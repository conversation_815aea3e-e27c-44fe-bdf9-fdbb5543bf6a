import { Button, Checkbox, Flex, Text } from '@chakra-ui/react';
import EmojiPickerPopover from '@core/components/Emoji/Picker/Popover/EmojiPopover';
import {
  CounterInput,
  CounterTextarea,
} from '@core/components/FormControl/CounterControl/CounterControl';
import { FormControl } from '@core/components/FormControl/FormControl';
import FrequencyPicker from '@core/components/FormControl/Frequency';
import { Icon, IconBox } from '@core/components/Icon/Icon';
import {
  faBell,
  faCalendarPlus,
  faImage,
  faSearch,
  faSubtitles,
  faTag,
} from '@fortawesome/pro-regular-svg-icons';
import { faFaceSmilePlus, faPlus } from '@fortawesome/pro-solid-svg-icons';
import { selectCurrentUserEmail, useAuthStore } from '@waitroom/auth';
import { XRayType } from '@waitroom/models';
import { ReactElement, ReactNode } from 'react';
import { Controller, useFormContext, useWatch } from 'react-hook-form';
import { useTranslation } from 'react-i18next';
import { createFormNs, xrayTypeOptions } from '../../../constants';
import { SHORT_SUMMARY_MAX_LENGTH, TITLE_MAX_LENGTH, XRayFormValues } from '../../../schema';
import { StageProps } from '../types';

const Stage3 = ({
  onDiscard,
  isLoading,
  children,
}: Omit<StageProps, 'onNext'> & {
  isLoading: boolean;
  children?: ReactNode;
}): ReactElement | null => {
  const { t } = useTranslation();
  const email = useAuthStore(selectCurrentUserEmail);
  const {
    register,
    formState: { errors },
    setValue,
    control,
    getValues,
  } = useFormContext<XRayFormValues>();

  const type = useWatch({
    name: 'type',
  });
  const icon = useWatch({
    name: 'icon',
  });

  const typeOption = xrayTypeOptions[type as XRayType];

  return (
    <>
      <Flex p={[4, 5, 6, 10, 12]} direction={'column'} justify={'center'} minH="480px" gap={6}>
        <Flex
          direction={{ base: 'column', lg: 'row' }}
          columnGap={10}
          rowGap={3}
          align={{ base: 'flex-start', lg: 'center' }}
        >
          <Text as={'strong'} minW={180}>
            <Icon icon={faTag} color={'gray.500'} mr={2} />
            {t(`${createFormNs}.stage3.typeLabel`)}
          </Text>
          <Flex align={'center'} gap={2}>
            {!!type && (
              <>
                <Text as={'strong'}>
                  <Icon icon={typeOption?.icon} color={typeOption?.color} mr={2} />
                  {t(`dashboard.xray.type.${type}.label`)}
                </Text>
                <Text as={'span'} fontSize={'sm'} color={'gray.400'}>
                  {t(`${createFormNs}.stage3.typeDescription`)}
                </Text>
              </>
            )}
          </Flex>
        </Flex>
        <Flex
          direction={{ base: 'column', lg: 'row' }}
          columnGap={10}
          rowGap={2}
          align={{ base: 'flex-start', lg: 'center' }}
        >
          <Text as={'strong'} minW={180}>
            <Icon icon={faImage} color={'gray.500'} mr={2} />
            {t(`${createFormNs}.stage3.iconLabel`)}
          </Text>
          <FormControl error={errors.icon} display={'flex'} alignItems={'center'}>
            <EmojiPickerPopover
              placement="top-start"
              onSelect={(emoji) => setValue('icon', emoji, { shouldDirty: true })}
              closeOnSelect
            >
              <Flex display={'inline-flex'} cursor={'pointer'} role={'button'}>
                <IconBox
                  size={'sm'}
                  rounded={'lg'}
                  fontSize={'3xl'}
                  _hover={{
                    opacity: 0.8,
                  }}
                >
                  {icon || <Icon icon={faFaceSmilePlus} color={'gray.400'} />}
                </IconBox>
              </Flex>
            </EmojiPickerPopover>
            {!!icon && (
              <Button
                variant={'link'}
                size={'sm'}
                px={2}
                py={1}
                ml={1}
                type={'button'}
                textDecoration={'underline'}
                onClick={() => setValue('icon', '', { shouldDirty: true })}
              >
                {t('global.clear')}
              </Button>
            )}
          </FormControl>
        </Flex>
        <Flex
          direction={{ base: 'column', lg: 'row' }}
          columnGap={10}
          rowGap={1}
          align={{ base: 'flex-start', lg: 'center' }}
        >
          <Text as={'strong'} minW={180}>
            <Icon icon={faSearch} color={'gray.500'} mr={2} />
            {t(`${createFormNs}.stage3.titleLabel`)}
          </Text>
          <FormControl error={errors.title}>
            <CounterInput
              {...register('title')}
              maxLength={TITLE_MAX_LENGTH}
              placeholder={t(`${createFormNs}.stage3.titleLabel`)}
              size={'sm'}
              fontSize={'md'}
              initialValue={getValues('title')}
            />
          </FormControl>
        </Flex>
        <Flex
          direction={{ base: 'column', lg: 'row' }}
          columnGap={10}
          rowGap={1}
          align={{ base: 'flex-start', lg: 'center' }}
        >
          <Text as={'strong'} minW={180}>
            <Icon icon={faSubtitles} color={'gray.500'} mr={2} />
            {t(`${createFormNs}.stage3.shortSummaryLabel`)}
          </Text>
          <FormControl error={errors.shortSummary}>
            <CounterTextarea
              {...register('shortSummary')}
              initialValue={getValues('shortSummary')}
              maxLength={SHORT_SUMMARY_MAX_LENGTH}
              placeholder={t(`${createFormNs}.stage3.shortSummaryLabel`)}
              size={'sm'}
              rows={3}
              fontSize={'md'}
            />
          </FormControl>
        </Flex>
        {type === XRayType.Digest && (
          <Flex direction={{ base: 'column', lg: 'row' }} columnGap={10} rowGap={1}>
            <Text as={'strong'} minW={180} mt={{ base: 0, lg: 2 }}>
              <Icon icon={faCalendarPlus} color={'gray.500'} mr={2} />
              {t(`${createFormNs}.stage3.frequencyLabel`)}
            </Text>
            <FormControl error={errors.frequency}>
              <Controller
                control={control}
                name="frequency"
                render={({ field: { onChange, value } }) => (
                  <FrequencyPicker
                    value={value}
                    onChange={onChange}
                    placeholder={t(`${createFormNs}.stage3.frequencyLabel`)}
                    size={'sm'}
                    fontSize={'md'}
                  />
                )}
              />
              <Text as={'span'} fontSize={'xs'} color={'gray.400'} mt={1}>
                {t(`${createFormNs}.stage3.frequencySubtext`)}
              </Text>
            </FormControl>
          </Flex>
        )}
        <Flex
          direction={{ base: 'column', lg: 'row' }}
          columnGap={10}
          rowGap={3}
          align={{ base: 'flex-start', lg: 'center' }}
        >
          <Text as={'strong'} minW={180}>
            <Icon icon={faBell} color={'gray.500'} mr={2} />
            {t(`${createFormNs}.stage3.alertsLabel`)}
          </Text>
          <FormControl error={errors.notifyAuthorEmail}>
            <Flex justify={'space-between'} align={'center'} wrap={'wrap'} gap={2}>
              <Checkbox {...register('notifyAuthorEmail')}>
                {t(`${createFormNs}.stage3.emailLabel`)}
              </Checkbox>
              <Text as={'span'} fontSize={'sm'} color={'gray.200'}>
                {email}
              </Text>
            </Flex>
          </FormControl>
        </Flex>
        {children}
      </Flex>
      <Flex
        justify={'space-between'}
        align={'center'}
        gap={2}
        px={6}
        py={[3, 3, 4]}
        borderTop="1px"
        borderColor="gray.600"
        position={'sticky'}
        bottom={0}
        bg={'gray.1000'}
        zIndex={2}
      >
        <Flex gap={2} direction={{ base: 'column', md: 'row' }}>
          <Button
            variant="outline"
            size={{ base: 'xs', lg: 'sm' }}
            type={'button'}
            colorScheme={'gray.400'}
            color={'white'}
            onClick={onDiscard}
          >
            {t('global.discard')}
          </Button>
          <Button
            colorScheme={'green'}
            type={'submit'}
            size={{ base: 'xs', lg: 'sm' }}
            leftIcon={<Icon icon={faPlus} size={'lg'} />}
            isLoading={isLoading}
          >
            {t(`${createFormNs}.stage3.createBtn`)}
          </Button>
        </Flex>
      </Flex>
    </>
  );
};

export default Stage3;
