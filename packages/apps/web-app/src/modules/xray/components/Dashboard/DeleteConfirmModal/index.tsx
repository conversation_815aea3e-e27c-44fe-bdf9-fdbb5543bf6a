import { Button, Heading, Text } from '@chakra-ui/react';
import Modal from '@core/components/Modal/Modal';
import { getResponse } from '@waitroom/common-api';
import { XRay } from '@waitroom/models';
import { ReactElement } from 'react';
import { useTranslation } from 'react-i18next';
import { Response } from '../../../../core/components/Form/Form';
import { useDeleteXRay } from '../../../hooks/useDeleteXRay';
import { baseNs } from '../constants';

export type DeleteConfirmModalProps = {
  id?: XRay['id'];
  onClose: () => void;
};

const ns = `${baseNs}.deleteModal`;

const DeleteConfirmModal = ({ id, onClose }: DeleteConfirmModalProps): ReactElement | null => {
  const { t } = useTranslation();
  const { mutate, isPending, error, isError } = useDeleteXRay({
    id: id ?? 0,
    onSuccess: () => {
      onClose();
    },
  });

  return (
    <Modal isOpen={!!id} onClose={onClose} isCentered size={'lg'}>
      <Modal.Header />
      <Modal.Body mb={6}>
        <Heading as={'h4'} size={'2xl'} mb={6}>
          {t(`${ns}.title`)}
        </Heading>
        <Text>{t(`${ns}.description`)}</Text>
        {isError && <Response response={getResponse(error)} />}
      </Modal.Body>
      <Modal.Footer display={'flex'} justifyContent={'center'} gap={4}>
        <Button variant={'outline'} colorScheme={'black'} size={'def'} onClick={onClose}>
          {t(`${ns}.cancelBtn`)}
        </Button>
        <Button isLoading={isPending} colorScheme={'black'} size={'def'} onClick={() => mutate()}>
          {t(`${ns}.confirmBtn`)}
        </Button>
      </Modal.Footer>
    </Modal>
  );
};
export default DeleteConfirmModal;
