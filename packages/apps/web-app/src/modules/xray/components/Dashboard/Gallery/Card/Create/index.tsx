import { routes } from '@/constants/routes';
import { Box, Button, Flex, Text, VStack } from '@chakra-ui/react';
import { Icon, IconBox } from '@core/components/Icon/Icon';
import { faPlus } from '@fortawesome/pro-regular-svg-icons';
import { ReactElement } from 'react';
import { useTranslation } from 'react-i18next';
import { Link } from 'react-router-dom';
import { baseNs } from '../../../constants';

const XRayCreateCard = (): ReactElement | null => {
  const { t } = useTranslation();
  return (
    <Box
      bgGradient={'linear(to-b, gray.400, transparent 92%)'}
      pt={'px'}
      px={'px'}
      borderRadius={'xl'}
      minH={'full'}
    >
      <Flex
        bgGradient={'linear(150deg, gray.800 2%, gray.1000 50%)'}
        borderRadius={'xl'}
        flexDirection={'column'}
        justifyContent={'space-between'}
        p={6}
        minH={'full'}
      >
        <VStack align={'start'} spacing={4}>
          <IconBox
            rounded={'full'}
            size={'xs'}
            boxShadow={'none'}
            bgColor={'gray.1000'}
            aria-label={t(`${baseNs}create`)}
          >
            <Icon icon={faPlus} fontSize={'2xl'} />
          </IconBox>
          <div>
            <Text fontWeight="bold" mb={2}>
              {t(`${baseNs}.createCardTitle`)}
            </Text>
            <Text color="gray.200" fontSize="sm">
              {t(`${baseNs}.createCardDesc`)}
            </Text>
          </div>
        </VStack>
        <Button
          as={Link}
          to={routes.DASHBOARD.XRAY.SETUP.link}
          variant="outline"
          colorScheme="red.500"
          color="white"
          size="sm"
          w="full"
          mt={12}
          leftIcon={<Icon icon={faPlus} size="lg" />}
        >
          {t(`${baseNs}.createCardBtn`)}
        </Button>
      </Flex>
    </Box>
  );
};

export default XRayCreateCard;
