import { frequencySchema } from '@core/components/FormControl/Frequency/constants';
import { XRayType } from '@waitroom/models';
import { TFunction } from 'i18next';
import { z } from 'zod';

export const TITLE_MAX_LENGTH = 100;
export const DESCRIPTION_MAX_LENGTH = 500;
export const SHORT_SUMMARY_MAX_LENGTH = 500;

export const schema = (t: TFunction) =>
  z
    .object({
      icon: z.string().emoji().optional(),
      title: z
        .string()
        .min(1, {
          message: t('validation.required', {
            field: t('global.title'),
          }),
        })
        .max(TITLE_MAX_LENGTH, {
          message: t('validation.tooLong', {
            field: t('global.title'),
            max: TITLE_MAX_LENGTH,
          }),
        }),
      description: z
        .string()
        .min(1, {
          message: t('validation.required', {
            field: t('global.description'),
          }),
        })
        .max(DESCRIPTION_MAX_LENGTH, {
          message: t('validation.tooLong', {
            field: t('global.description'),
            max: DESCRIPTION_MAX_LENGTH,
          }),
        }),
      shortSummary: z
        .string()
        .min(1, {
          message: t('validation.required', {
            field: t('dashboard.xray.shortSummary'),
          }),
        })
        .max(SHORT_SUMMARY_MAX_LENGTH, {
          message: t('validation.tooLong', {
            field: t('dashboard.xray.shortSummary'),
            max: SHORT_SUMMARY_MAX_LENGTH,
          }),
        }),
      prompt: z.string().min(1, {
        message: t('validation.required', {
          field: t('dashboard.xray.prompt'),
        }),
      }),
      type: z.nativeEnum(XRayType),
      notifyAuthorEmail: z.boolean().optional(),
      //visibility: z.enum(['user', 'team']),
      //scope: z.enum(['all', 'personal', 'team']),
      frequency: frequencySchema.optional(),
    })
    .superRefine((data, ctx) => {
      // frequency is required if xray type is digest
      if (data.type === XRayType.Digest && !data.frequency?.type) {
        ctx.addIssue({
          path: ['frequency'],
          code: z.ZodIssueCode.custom,
          message: t('validation.required', {
            field: t('dashboard.xray.frequency'),
          }),
        });
      }
    });

export type XRayFormValues = z.infer<ReturnType<typeof schema>>;
