import { Flex, IconButton } from '@chakra-ui/react';
import EmojiPopover from '@core/components/Emoji/Picker/Popover/EmojiPopover';
import {
  CounterInput,
  CounterTextarea,
} from '@core/components/FormControl/CounterControl/CounterControl';
import { FormControl } from '@core/components/FormControl/FormControl';
import { ReactElement } from 'react';
import { useFormContext, useWatch } from 'react-hook-form';
import { useTranslation } from 'react-i18next';
import { editFormNs } from '../../../constants';
import { SHORT_SUMMARY_MAX_LENGTH, TITLE_MAX_LENGTH } from '../../../schema';

const Info = (): ReactElement | null => {
  const { t } = useTranslation();

  const {
    register,
    setValue,
    getValues,
    formState: { errors },
  } = useFormContext();
  const icon = useWatch({ name: 'icon' });

  return (
    <Flex direction="column" gap={6} pb={4}>
      <FormControl label={t(`${editFormNs}.info.iconLabel`)} fontSize={'sm'} error={errors.icon}>
        <Flex align="center" gap={3}>
          <EmojiPopover
            placement="bottom-start"
            onSelect={(emoji) => setValue('icon', emoji, { shouldDirty: true })}
            closeOnSelect
          >
            <IconButton
              aria-label={t(`${editFormNs}.info.iconLabel`)}
              variant="ghost"
              size="sm"
              fontSize="2xl"
              rounded={'lg'}
              px={3}
            >
              <span>{icon}</span>
            </IconButton>
          </EmojiPopover>
        </Flex>
      </FormControl>
      <FormControl label={t(`${editFormNs}.info.titleLabel`)} fontSize={'sm'} error={errors.title}>
        <CounterInput
          {...register('title')}
          size={'sm'}
          maxLength={TITLE_MAX_LENGTH}
          placeholder={t(`${editFormNs}.info.titleLabel`)}
          initialValue={getValues('title')}
        />
      </FormControl>
      <FormControl
        label={t(`${editFormNs}.info.shortSummaryLabel`)}
        fontSize={'sm'}
        error={errors.shortSummary}
      >
        <CounterTextarea
          {...register('shortSummary')}
          initialValue={getValues('shortSummary')}
          maxLength={SHORT_SUMMARY_MAX_LENGTH}
          placeholder={t(`${editFormNs}.info.shortSummaryPlaceholder`)}
          size={'sm'}
          minH={14}
          rows={3}
        />
      </FormControl>
    </Flex>
  );
};
export default Info;
