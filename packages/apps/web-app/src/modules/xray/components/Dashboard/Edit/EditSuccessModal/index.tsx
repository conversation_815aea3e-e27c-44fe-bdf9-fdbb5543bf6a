import { Button, Heading, Text } from '@chakra-ui/react';
import Modal, { ModalProps } from '@core/components/Modal/Modal';
import { ReactElement } from 'react';
import { useTranslation } from 'react-i18next';
import { editFormNs } from '../../constants';

export type DiscardConfirmModalProps = ModalProps;

const DiscardConfirmModal = (props: DiscardConfirmModalProps): ReactElement | null => {
  const { t } = useTranslation();
  return (
    <Modal {...props} isCentered size="lg">
      <Modal.Header />
      <Modal.Body mb={6}>
        <Heading as="h4" size="2xl" mb={6}>
          {t(`${editFormNs}.successModal.title`)}
        </Heading>
        <Text>{t(`${editFormNs}.successModal.description`)}</Text>
      </Modal.Body>
      <Modal.Footer display="flex" justifyContent="center" gap={4}>
        <Button colorScheme="black" size={'def'} minW={'120px'} onClick={props.onClose}>
          {t(`${editFormNs}.successModal.confirmBtn`)}
        </Button>
      </Modal.Footer>
    </Modal>
  );
};
export default DiscardConfirmModal;
