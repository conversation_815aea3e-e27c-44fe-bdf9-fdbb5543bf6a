import {
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  Modal<PERSON>ody,
  <PERSON>dal<PERSON><PERSON>nt,
  <PERSON><PERSON><PERSON>ooter,
  ModalOverlay,
  Text,
} from '@chakra-ui/react';
import { XRay } from '@waitroom/models';
import { ReactElement, ReactNode } from 'react';
import { useTranslation } from 'react-i18next';
import { useToggleXRayStatus } from '../../../../hooks/useToggleXRayStatus';
import { editFormNs } from '../../constants';

export interface ReactivateModalProps {
  id: XRay['id'];
  isOpen: boolean;
  onClose: () => void;
  children?: ReactNode;
}

const ReactivateModal = ({
  id,
  isOpen,
  onClose,
  children,
}: ReactivateModalProps): ReactElement | null => {
  const { t } = useTranslation();
  const { onSubmit, mutation } = useToggleXRayStatus({ id, onSuccess: onClose });
  return (
    <Modal isOpen={isOpen} onClose={onClose} isCentered size="lg">
      <ModalOverlay />
      <ModalContent>
        <ModalBody p={0} mb={10}>
          {children || (
            <>
              <Heading size={'2xl'} mb={6}>
                {t(`${editFormNs}.reactivateModal.title`)}
              </Heading>
              <Text>{t(`${editFormNs}.reactivateModal.description`)}</Text>
            </>
          )}
        </ModalBody>
        <ModalFooter p={0} display="flex" justifyContent="center" gap={4}>
          <Button variant="outline" colorScheme="black" size={'def'} onClick={onClose}>
            {t(`${editFormNs}.reactivateModal.cancelBtn`)}
          </Button>
          <Button
            variant="solid"
            colorScheme="black"
            size={'def'}
            isLoading={mutation.isPending}
            onClick={() => onSubmit(true)}
          >
            {t(`${editFormNs}.reactivateModal.confirmBtn`)}
          </Button>
        </ModalFooter>
      </ModalContent>
    </Modal>
  );
};

export default ReactivateModal;
