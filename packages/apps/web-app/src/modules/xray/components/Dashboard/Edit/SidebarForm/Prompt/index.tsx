import { Box, Text, Textarea } from '@chakra-ui/react';
import { FormControl } from '@core/components/FormControl/FormControl';
import { Icon } from '@core/components/Icon/Icon';
import { faDown } from '@fortawesome/pro-regular-svg-icons';
import { ReactElement } from 'react';
import { useFormContext } from 'react-hook-form';
import { useTranslation } from 'react-i18next';
import { editFormNs } from '../../../constants';

const Prompt = (): ReactElement | null => {
  const { t } = useTranslation();
  const {
    register,
    formState: { errors },
  } = useFormContext();

  return (
    <Box mb={6}>
      <FormControl
        label={t(`${editFormNs}.prompt.promptLabel`)}
        error={errors.prompt}
        fontSize={'sm'}
      >
        <Box position={'relative'}>
          <Text
            color={'red.300'}
            position={'absolute'}
            top={4}
            left={0}
            mx={4}
            py={'px'}
            px={2}
            rounded={'lg'}
            zIndex={2}
            fontSize={'xs'}
          >
            <Icon icon={faDown} mr={2} />
            {t(`${editFormNs}.prompt.promptEditWarning`)}
          </Text>
          <Textarea
            pt={16}
            fontFamily={'monospace'}
            placeholder={t(`${editFormNs}.prompt.promptLabel`)}
            size={'sm'}
            minH={350}
            {...register('prompt')}
          />
        </Box>
      </FormControl>
    </Box>
  );
};
export default Prompt;
