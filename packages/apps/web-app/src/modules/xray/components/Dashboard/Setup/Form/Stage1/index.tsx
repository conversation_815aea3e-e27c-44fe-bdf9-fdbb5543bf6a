import { routes } from '@/constants/routes';
import { Box, Button, Flex, Heading, SystemStyleObject, Text, Textarea } from '@chakra-ui/react';
import { Response } from '@core/components/Form/Form';
import { FormControl } from '@core/components/FormControl/FormControl';
import { Icon } from '@core/components/Icon/Icon';
import SimpleLoader from '@core/components/Loader/Simple/SimpleLoader';
import { updateXRayState } from '@core/store/store';
import { faChevronRight, faSparkles } from '@fortawesome/pro-solid-svg-icons';
import { useMutation } from '@tanstack/react-query';
import { getResponse } from '@waitroom/common-api';
import { generateXRayPromptMutation, getRequestData } from '@waitroom/react-query';
import { ReactElement, ReactNode } from 'react';
import { useFormContext, useWatch } from 'react-hook-form';
import { Trans, useTranslation } from 'react-i18next';
import { Link } from 'react-router-dom';
import { createFormNs } from '../../../constants';
import { DESCRIPTION_MAX_LENGTH, XRayFormValues } from '../../../schema';
import { StageProps } from '../types';

const wrapperSx: SystemStyleObject = {
  p: [4, 5, 6, 10, 12],
  flexDirection: 'column',
  alignItems: 'center',
  justifyContent: 'center',
  minH: '480px',
  textAlign: 'center',
};

const Stage1 = ({
  onNext,
  onDiscard,
  children,
}: StageProps & { children?: ReactNode }): ReactElement | null => {
  const { t } = useTranslation();
  const {
    register,
    setValue,
    getValues,
    formState: { dirtyFields, errors },
  } = useFormContext<XRayFormValues>();
  const description = useWatch<XRayFormValues, 'description'>({
    name: 'description',
  });

  const { mutate, isPending, isError, error, reset, variables } = useMutation({
    ...generateXRayPromptMutation(),
    onSuccess: (response) => {
      const data = getRequestData(response);
      if (data) {
        setValue('type', data.type, { shouldDirty: true });
        setValue('prompt', data.prompt, { shouldDirty: true });
      }
      onNext();
      updateXRayState(getValues(), 1);
    },
  });

  const onSubmit = () => {
    const v = getValues();
    // store the values in a local storage
    updateXRayState(v, 0);
    const shouldSubmit = v.description !== variables?.description && dirtyFields.description;
    if (shouldSubmit) mutate({ description: v.description });
    else onNext();
  };

  if (isPending) {
    return (
      <Flex sx={wrapperSx}>
        <SimpleLoader>
          <Trans i18nKey={`${createFormNs}.loadingPrompt`} />
        </SimpleLoader>
      </Flex>
    );
  }
  if (isError) {
    return (
      <Box px={[4, 8]} py={[12, 16]}>
        <Response response={getResponse(error)} />
        <Button mt={6} colorScheme={'black'} size={'xs'} onClick={reset}>
          {t('global.retry')}
        </Button>
      </Box>
    );
  }
  const isValid = description?.length > 0;
  return (
    <>
      <Flex sx={wrapperSx}>
        {children}
        <Heading as={'h3'} size={'2xl'} mb={4}>
          {t(`${createFormNs}.stage1.question`)}
        </Heading>
        <Text color={'gray.300'} mb={8}>
          <Trans i18nKey={`${createFormNs}.stage1.description`} />{' '}
          <Link to={routes.DASHBOARD.XRAY.DEFAULT.link} className="strong">
            {t('global.learnMore')}
          </Link>
        </Text>
        <FormControl error={errors.shortSummary} mb={6}>
          <Textarea
            {...register('description')}
            placeholder={t(`${createFormNs}.stage1.placeholder`)}
            maxLength={DESCRIPTION_MAX_LENGTH}
            minH={'100px'}
            size={'def'}
            resize={'vertical'}
          />
        </FormControl>
      </Flex>
      <Flex
        justify={'space-between'}
        align={'center'}
        gap={2}
        px={6}
        py={[3, 3, 4]}
        borderTop="1px"
        borderColor="gray.600"
        position={'sticky'}
        bottom={0}
        bg={'gray.1000'}
        zIndex={1}
      >
        <Flex gap={2} direction={{ base: 'column', md: 'row' }}>
          <Button
            variant="outline"
            size={{ base: 'xs', lg: 'sm' }}
            type={'button'}
            colorScheme={'gray.400'}
            color={'white'}
            onClick={onDiscard}
          >
            {t('global.discard')}
          </Button>
          <Button
            colorScheme={'red.500'}
            variant={'outline'}
            type="button"
            onClick={onSubmit}
            size={{ base: 'xs', lg: 'sm' }}
            color={'white'}
            leftIcon={<Icon icon={faSparkles} size={'lg'} />}
            rightIcon={<Icon icon={faChevronRight} size={'lg'} />}
            isDisabled={!isValid}
          >
            {t(`${createFormNs}.stage1.nextBtn`)}
          </Button>
        </Flex>
      </Flex>
    </>
  );
};
export default Stage1;
