import { cronToFrequency, frequencyToCron } from '@core/components/FormControl/Frequency/helpers';
import { XRayApiService } from '@waitroom/common-api';
import { XRay } from '@waitroom/models';
import { toBoolean } from '@waitroom/utils';
import { XRayFormValues } from './schema';

export const toFormValues = (
  values: Partial<XRay> | undefined,
): Partial<XRayFormValues> | undefined => {
  if (!values) return undefined;
  return {
    ...values,
    notifyAuthorEmail: !!values.alertChannels?.notifyAuthorEmail,
    frequency: values.frequency ? cronToFrequency(values.frequency) : undefined,
  };
};

export const fromFormValues = (values: XRayFormValues): XRayApiService.CreateData => {
  const { notifyAuthorEmail, ...rest } = values;
  return {
    ...rest,
    alertChannels: {
      notifyAuthorEmail: toBoolean(notifyAuthorEmail),
    },
    frequency: values.frequency ? frequencyToCron(values.frequency) : '0 0 * * *',
  };
};
