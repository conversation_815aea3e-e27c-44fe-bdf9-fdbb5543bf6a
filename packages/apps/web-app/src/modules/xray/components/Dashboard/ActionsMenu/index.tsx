import { routes } from '@/constants/routes';
import {
  IconButton,
  IconButtonProps,
  Menu,
  MenuButton,
  MenuDivider,
  MenuItem,
  MenuList,
  Spinner,
} from '@chakra-ui/react';
import { spinnerProps } from '@core/components/Common/styles';
import { Icon } from '@core/components/Icon/Icon';
import { useToastResponse } from '@core/hooks/useToastResponse';
import {
  faArchive,
  faCopy,
  faEllipsisVertical,
  faShare,
  faTrash,
  faWrench,
} from '@fortawesome/pro-regular-svg-icons';
import { XRay, XRayTemplate } from '@waitroom/models';
import { ReactElement } from 'react';
import { useTranslation } from 'react-i18next';
import { generatePath, Link } from 'react-router-dom';
import { useToggleXRayStatus } from '../../../hooks/useToggleXRayStatus';
import { baseNs } from '../constants';

export type XRayActionsMenuProps = {
  data: XRay | XRayTemplate;
  isTemplate?: boolean;
  showEdit?: boolean;
  onDelete?: () => void;
  buttonProps?: Partial<IconButtonProps>;
};

const XRayActionsMenu = ({
  data,
  isTemplate,
  showEdit = true,
  onDelete,
  buttonProps,
}: XRayActionsMenuProps): ReactElement | null => {
  const { t } = useTranslation();
  const { onError } = useToastResponse();
  // remove not needed fields for copy
  const { id, ...copyData } = data;
  const { mutation: statusMutation, onSubmit: onSubmitStatus } = useToggleXRayStatus({
    id,
    onError,
  });

  return (
    <>
      <Menu placement="bottom-end">
        <MenuButton
          aria-label={t('global.options')}
          as={IconButton}
          icon={<Icon icon={faEllipsisVertical} fontSize="lg" />}
          variant="ghost"
          size="xs"
          color="gray.400"
          {...buttonProps}
          zIndex={2}
        />
        <MenuList py={0} borderWidth={'1px'} borderColor="gray.700" overflow={'hidden'}>
          {showEdit && !isTemplate && (
            <>
              <MenuItem
                as={Link}
                to={generatePath(routes.DASHBOARD.XRAY.EDIT.fullRoute, { id: data.id })}
                state={data}
                className="none"
              >
                <Icon icon={faWrench} mr={3} />
                {t('global.edit')}
              </MenuItem>
              <MenuDivider my={0} />
            </>
          )}
          {!isTemplate && (
            <>
              <MenuItem
                onClick={() => {
                  console.log('! TODO');
                }}
              >
                <Icon icon={faShare} mr={3} />
                {t('global.share')}
              </MenuItem>
              <MenuDivider my={0} />
            </>
          )}
          <MenuItem
            as={Link}
            to={{
              pathname: generatePath(routes.DASHBOARD.XRAY.SETUP.fullRoute),
              search: isTemplate ? '?preview=true' : undefined,
            }}
            state={copyData}
            className="none"
          >
            <Icon icon={faCopy} mr={3} />
            {t('global.makeACopy')}
          </MenuItem>
          {!isTemplate && (
            <>
              <MenuDivider my={0} />
              <MenuItem
                onClick={() => onSubmitStatus(!data.isActive)}
                isDisabled={statusMutation.isPending}
                closeOnSelect={false}
              >
                {statusMutation.isPending ? (
                  <Spinner size={'sm'} mr={2} {...spinnerProps} />
                ) : (
                  <Icon icon={faArchive} mr={3} />
                )}
                {data.isActive ? t(`${baseNs}.disableXRay`) : t(`${baseNs}.reactivateXRay`)}
              </MenuItem>
              <MenuDivider my={0} />
              {!!onDelete && (
                <MenuItem onClick={onDelete}>
                  <Icon icon={faTrash} mr={3} />

                  {t('global.delete')}
                </MenuItem>
              )}
            </>
          )}
        </MenuList>
      </Menu>
    </>
  );
};
export default XRayActionsMenu;
