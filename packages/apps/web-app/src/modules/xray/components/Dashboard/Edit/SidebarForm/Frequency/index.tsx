import { Box, Text } from '@chakra-ui/react';
import { FormControl } from '@core/components/FormControl/FormControl';
import { ReactElement } from 'react';
import { Controller, useFormContext } from 'react-hook-form';
import { useTranslation } from 'react-i18next';
import { FrequencyPicker } from '../../../../../../core/components/FormControl/Frequency';
import { editFormNs } from '../../../constants';

const Frequency = (): ReactElement | null => {
  const { t } = useTranslation();
  const {
    control,
    formState: { errors },
  } = useFormContext();
  return (
    <Box mb={6}>
      <FormControl error={errors.frequency}>
        <Controller
          control={control}
          name="frequency"
          render={({ field: { onChange, value } }) => (
            <FrequencyPicker
              value={value}
              onChange={onChange}
              placeholder={t(`${editFormNs}.frequency.label`)}
              size={'sm'}
            />
          )}
        />
        <Text as={'span'} fontSize={'xs'} color={'gray.400'} mt={1}>
          {t(`${editFormNs}.frequency.subtext`)}
        </Text>
      </FormControl>
    </Box>
  );
};
export default Frequency;
