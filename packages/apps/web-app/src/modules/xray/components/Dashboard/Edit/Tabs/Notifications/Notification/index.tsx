import { Box, Flex, Text } from '@chakra-ui/react';
import Markdown from '@core/components/Markdown/Markdown';
import { mixDateFormatWithTime } from '@core/utils/dateTime';
import { Sources } from '@modules/ai/components/Sources/Sources';
import { XRayNotification } from '@waitroom/models';
import { ReactElement } from 'react';
import { useTranslation } from 'react-i18next';

export type NotificationProps = {
  data: XRayNotification;
};

const Notification = ({ data }: NotificationProps): ReactElement | null => {
  const { t } = useTranslation();
  const { content, source, createdAt } = data;

  return (
    <Box py={5} borderBottomWidth="1px" borderColor="gray.700">
      <Flex
        direction={{ base: 'column', md: 'row' }}
        justify={'space-between'}
        align={{ base: 'flex-start', md: 'center' }}
        gap={2}
        mb={2}
      >
        <Flex align={'flex-end'} wrap={'wrap'} columnGap={2}>
          <Text fontWeight={'bold'} fontSize={'lg'}>
            ! TODO - notification has no title ???
          </Text>
        </Flex>
        <Text fontSize="sm" color="gray.400">
          {mixDateFormatWithTime(createdAt * 1000, 'MMM d, h:mmaaa', t)}
        </Text>
      </Flex>
      <Markdown>{content}</Markdown>
      <Sources sources={source} size={'3xs'} mt={4} />
    </Box>
  );
};

export default Notification;
