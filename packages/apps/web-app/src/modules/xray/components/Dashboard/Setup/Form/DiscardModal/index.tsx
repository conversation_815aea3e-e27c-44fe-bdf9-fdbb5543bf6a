import { Button, Heading, Text, UseDisclosureReturn } from '@chakra-ui/react';
import Modal from '@core/components/Modal/Modal';
import { ReactElement } from 'react';
import { useTranslation } from 'react-i18next';
import { createFormNs } from '../../../constants';

export type DiscardModalProps = UseDisclosureReturn & {
  onConfirm: () => void;
};

const DiscardModal = ({ onConfirm, ...rest }: DiscardModalProps): ReactElement | null => {
  const { t } = useTranslation();
  return (
    <Modal {...rest} isCentered size="lg">
      <Modal.Header />
      <Modal.Body mb={6}>
        <Heading as="h4" size="2xl" mb={6}>
          {t(`${createFormNs}.discardModal.title`)}
        </Heading>
        <Text>{t(`${createFormNs}.discardModal.description`)}</Text>
      </Modal.Body>
      <Modal.Footer display="flex" justifyContent="center" gap={4}>
        <Button variant="outline" colorScheme="black" size={'def'} onClick={onConfirm}>
          {t(`${createFormNs}.discardModal.confirmBtn`)}
        </Button>
        <Button colorScheme="black" size={'def'} onClick={rest.onClose}>
          {t(`${createFormNs}.discardModal.cancelBtn`)}
        </Button>
      </Modal.Footer>
    </Modal>
  );
};
export default DiscardModal;
