import CardLoader from '@core/components/Loader/Card/CardLoader';
import { useInfiniteQuery } from '@tanstack/react-query';
import { selectAuthUserId, useAuthStore } from '@waitroom/auth';
import { XRay } from '@waitroom/models';
import {
  getInfinityRequestData,
  milliseconds,
  xrayGetNotificationsQuery,
} from '@waitroom/react-query';
import { repeat } from '@waitroom/react-utils';
import { memo, ReactElement, useEffect, useMemo } from 'react';
import Notification from './Notification';

export type NotificationsProps = {
  id: XRay['id'];
  setUnreadCount: (count: number) => void;
};

const Notifications = memo(({ id, setUnreadCount }: NotificationsProps): ReactElement | null => {
  const userId = useAuthStore(selectAuthUserId) || '';
  const { data, isPending } = useInfiniteQuery({
    ...xrayGetNotificationsQuery(id, userId),
    refetchInterval: milliseconds['5m'],
  });
  const notifications = getInfinityRequestData(data)?.notifications;

  const unreadCount = useMemo(() => {
    return notifications?.filter((n) => !n.seen).length || 0;
  }, [notifications]);
  useEffect(() => {
    setUnreadCount(unreadCount);
  }, [setUnreadCount, unreadCount]);

  if (isPending) return <>{repeat(<CardLoader bgColor={'gray.900'} image={false} />)}</>;
  if (!notifications) return <>! TODO: no notifications</>;
  return (
    <>
      {notifications.map((n, index) => (
        <Notification key={index} data={n} />
      ))}
    </>
  );
});
export default Notifications;
