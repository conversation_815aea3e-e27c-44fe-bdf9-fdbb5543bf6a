import { Box, Container } from '@chakra-ui/react';
import FormLoader from '@core/components/Loader/Form/FormLoader';
import { defaultBodyPadding } from '@dashboard/components/Layout/Default/constants';
import { HEADER_HEIGHT } from '@dashboard/components/Layout/Default/Header/Header';
import { useQuery } from '@tanstack/react-query';
import { XRayComplete, XRayTemplateComplete } from '@waitroom/models';
import { getQueryRequestData, xrayGetTemplateByIdQuery } from '@waitroom/react-query';
import { toBoolean } from '@waitroom/utils';
import { useTranslation } from 'react-i18next';
import { useLocation, useParams, useSearchParams } from 'react-router-dom';
import { createFormNs } from '../constants';
import XRaySetupForm from './Form';

export type XRaySetupPageState = Partial<XRayComplete> | Partial<XRayTemplateComplete>;

const XRaySetupPage = () => {
  const { t } = useTranslation();
  const urlParams = useParams<{ id?: string }>();
  const id = urlParams.id ? Number(urlParams.id) : undefined;
  const [params] = useSearchParams();
  const showPreview = toBoolean(params.get('preview'));
  const urlState = useLocation().state as XRaySetupPageState | undefined;
  const hasId = id !== undefined;
  const query = useQuery({ ...xrayGetTemplateByIdQuery(id ?? 0), enabled: hasId });
  const initialValues = hasId ? getQueryRequestData(query) : urlState;

  return (
    <Container
      maxW={'container.xl'}
      display={'flex'}
      flexDirection={'column'}
      alignItems={'space-between'}
      minH={680}
      maxH={{ md: `calc(100vh - ${HEADER_HEIGHT}px)` }}
      height={{ md: 'full' }}
      {...defaultBodyPadding}
    >
      {hasId && query.isPending ? (
        <FormLoader />
      ) : !hasId || initialValues ? (
        <XRaySetupForm
          initialValues={initialValues}
          showPreview={hasId || showPreview}
          owner={initialValues?.owner}
        />
      ) : (
        <>! TODO: Error fetching template</>
      )}
      <Box mt={'auto'} pt={[8, 12]} color={'gray.400'} fontSize={'sm'} textAlign={'center'}>
        {t(`${createFormNs}.disclaimer`)}
      </Box>
    </Container>
  );
};

export default XRaySetupPage;
