import { Box, Checkbox, Flex, Text } from '@chakra-ui/react';
import { selectCurrentUserEmail, useAuthStore } from '@waitroom/auth';
import { ReactElement } from 'react';
import { useFormContext } from 'react-hook-form';
import { useTranslation } from 'react-i18next';
import { FormControl } from '../../../../../../core/components/FormControl/FormControl';
import { editFormNs } from '../../../constants';
import { XRayFormValues } from '../../../schema';

const Alerts = (): ReactElement | null => {
  const { t } = useTranslation();
  const email = useAuthStore(selectCurrentUserEmail);
  const {
    register,
    formState: { errors },
  } = useFormContext<XRayFormValues>();

  return (
    <Box mb={6}>
      <FormControl error={errors.notifyAuthorEmail}>
        <Checkbox {...register('notifyAuthorEmail')} alignItems={'flex-start'} mt={2}>
          <Flex as={'span'} direction={'column'} mt={-1} ml={2}>
            <Text as={'span'}>{t(`${editFormNs}.alerts.emailLabel`)}</Text>
            <Text as={'span'} fontSize={'sm'} color={'gray.400'}>
              {email}
            </Text>
          </Flex>
        </Checkbox>
      </FormControl>
    </Box>
  );
};
export default Alerts;
