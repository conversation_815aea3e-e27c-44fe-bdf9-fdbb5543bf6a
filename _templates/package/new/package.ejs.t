---
to: packages/libs/<%=name%>/package.json
---

{
  "name": "@waitroom/<%=name%>",
  "version": "0.0.1",
  "license": "UNLICENSED",
  "author": "Rumi <<EMAIL>>",
  "description": "<PERSON>umi <%=name%>",
  "type": "module",
  "source": "src/index.ts",
  "types": "./dist/index.d.ts",
  "main": "./dist/index.es.js",
  "module": "./dist/index.es.js",
  "exports": {
    ".": {
      "types": "./dist/index.d.ts",
      "import": "./dist/index.es.js",
    }
  },
  "repository": {
    "type": "git",
    "url": "git+https://github.com/Waitroom/rumi.ai"
  },
  "bugs": {
    "url": "https://github.com/Waitroom/rumi.ai/issues"
  },
  "keywords": [],
  "files": [
    "dist"
  ],
  "scripts": {
    "start": "echo 'Noop'",
    "build": "rimraf dist && vite build && tsc --emitDeclarationOnly --project tsconfig.build.json",
    "build:libs": "yarn run build",
    "test": "vitest run",
    "test:cov": "vitest run --coverage",
    "test:nocov": "vitest run",
    "test:watch": "vitest",
    "test:related": "cross-env CI=true vitest related --run --passWithNoTests",
    "lint": "yarn run eslint",
    "lint:ts": "tsc --noEmit",
    "format": "prettier . --write",
    "publish": "yarn npm publish || true"
  },
  "peerDependencies": {<%- hasReact ? `
    "react": ">=19.1.0"` : null %>
  },
  "dependencies": {},
  "devDependencies": {
    "@testing-library/jest-dom": "~6.6.3",<%- hasReact ? `
    "@testing-library/react": "~16.3.0",` : null %>
    "@types/node": "~22.15.14",<%- hasReact ? `
    "@types/react": "~19.1.5",
    "@vitejs/plugin-react": "~4.4.1",` : null %>
    "eslint": "~9.24.0",<%- hasReact ? `
    "eslint-config-react-app": "~7.0.1",` : null %>
    "happy-dom": "~17.5.6",
    "prettier": "~3.5.3",<%- hasReact ? `
    "react": "~19.1.0",` : null %>
    "regenerator-runtime": "~0.14.1",
    "typescript": "~5.8.3",
    ""vite": "~6.3.5",
    "vitest": "~3.1.3",
    "vitest-dom": "~0.1.1"
  },
  "publishConfig":{
    "registry":"https://npm.pkg.github.com/"
  }
}
