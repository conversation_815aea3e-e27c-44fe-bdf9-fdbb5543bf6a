{
  "editor.codeActionsOnSave": {},
  "editor.formatOnSave": true,
  "editor.defaultFormatter": "esbenp.prettier-vscode",
  "editor.tabSize": 2,
  "files.eol": "\n",
  "javascript.preferences.importModuleSpecifier": "project-relative",
  "typescript.preferences.importModuleSpecifier": "project-relative",
  "typescript.tsdk": "node_modules/typescript/lib",
  "cSpell.words": ["rumi", "rumi.ai"],
  "prettier.printWidth": 100,
  "workbench.colorCustomizations": {
    "titleBar.activeBackground": "#1E2431", // change this color!
    "titleBar.inactiveBackground": "#1E2431", // change this color!
    "titleBar.activeForeground": "#ffffff", // change this color!
    "titleBar.inactiveForeground": "#ffffff" // change this color!
  },
}
